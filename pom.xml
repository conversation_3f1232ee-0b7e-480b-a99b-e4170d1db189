<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bimcoresolutions</groupId>
        <artifactId>bim-parent</artifactId>
        <version>4.3.1-SNAPSHOT</version>
    </parent>

    <groupId>com.bimcoresolutions.project.cityappng.bimapi</groupId>
    <artifactId>bimapi-cityappng</artifactId>
    <version>3.1.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <bimapibase.version>4.2.1-SNAPSHOT</bimapibase.version>
        <cityapp-api-generated.version>2.4.1-SNAPSHOT</cityapp-api-generated.version>
        <bimcity-api-plugins.version>3.4.1-SNAPSHOT</bimcity-api-plugins.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.bimcoresolutions.product.cityapp.api</groupId>
                <artifactId>cityapp-api-generated</artifactId>
                <version>${cityapp-api-generated.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api</groupId>
                <artifactId>bimcity-api-plugins</artifactId>
                <version>${bimcity-api-plugins.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.bimcoresolutions.api</groupId>
            <artifactId>api-base</artifactId>
            <version>${bimapibase.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bimcoresolutions.product.cityapp.api.generated</groupId>
            <artifactId>cityapp-api-generated-server</artifactId>
        </dependency>

        <!-- Plugins -->
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>maintenance</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>event</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>dynpanel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>authentication</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>streetlight</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>defectreport</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
            <artifactId>perimeters</artifactId>
            <version>3.4.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.api</groupId>
            <artifactId>api-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.api</groupId>
            <artifactId>api-base</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <classifier>exec</classifier>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <scm>
        <connection>scm:git:git@${env.CI_SERVER_HOST}:${env.CI_PROJECT_PATH}.git</connection>
        <developerConnection>scm:git:git@${env.CI_SERVER_HOST}:${env.CI_PROJECT_PATH}.git</developerConnection>
        <url>${env.CI_PROJECT_URL}</url>
        <tag>HEAD</tag>
    </scm>

    <repositories>
        <repository>
            <id>nexus.read</id>
            <url>https://nxs.factory.showroom.aixom.tech/repository/bim-maven/</url>
        </repository>
    </repositories>

</project>
