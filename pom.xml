<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>bim-parent</artifactId>
        <groupId>com.bimcoresolutions</groupId>
        <version>4.3.1-SNAPSHOT</version>
    </parent>

    <groupId>com.bimcoresolutions.product.bimcity.api</groupId>
    <artifactId>bimcity-api-plugins</artifactId>
    <version>3.4.1-SNAPSHOT</version>
    <packaging>pom</packaging>    <modules>
        <module>authentication</module>
        <module>camera</module>
        <module>common</module>
        <module>defectreport</module>
        <module>dynpanel</module>
        <module>event</module>
        <module>maintenance</module>
        <module>meteo</module>
        <module>ssopreferredusername</module>
        <module>streetlight</module>
        <module>perimeters</module>
    </modules>

    <properties>
        <bimapi-base.version>4.2.1-SNAPSHOT</bimapi-base.version>
        <bimcity-api-plugins.version>3.4.1-SNAPSHOT</bimcity-api-plugins.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- BIMCORE -->
            <dependency>
                <groupId>com.bimcoresolutions.api</groupId>
                <artifactId>api-base</artifactId>
                <version>${bimapi-base.version}</version>
            </dependency>

            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>authentication</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>camera</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>common</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>dynpanel</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>event</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>maintenance</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>meteo</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>            
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>streetlight</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>ssopreferredusername</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
                <artifactId>defectreport</artifactId>
                <version>${bimcity-api-plugins.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bimcoresolutions.product.cityapp.api</groupId>
                <artifactId>cityapp-api-generated</artifactId>
                <version>2.4.1-SNAPSHOT</version>
            </dependency>

            <!-- OTHERS -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <repositories>
        <repository>
            <id>nexus.read</id>
            <url>https://nxs.factory.showroom.aixom.tech/repository/bim-maven/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>nexus.read</id>
            <url>https://nxs.factory.showroom.aixom.tech/repository/bim-maven/</url>
        </pluginRepository>
    </pluginRepositories>

    <scm>
        <connection>scm:git:git@${env.CI_SERVER_HOST}:${env.CI_PROJECT_PATH}.git</connection>
        <developerConnection>scm:git:git@${env.CI_SERVER_HOST}:${env.CI_PROJECT_PATH}.git</developerConnection>
        <url>${env.CI_PROJECT_URL}</url>
        <tag>HEAD</tag>
    </scm>

</project>
