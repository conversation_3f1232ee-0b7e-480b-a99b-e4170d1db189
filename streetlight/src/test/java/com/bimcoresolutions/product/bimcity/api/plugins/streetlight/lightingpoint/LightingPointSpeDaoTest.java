package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.sql.DataSource;
import java.util.List;

import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static org.assertj.core.api.Assertions.assertThat;

class LightingPointSpeDaoTest {

    public static final QueryFormer REALTIME = new QueryFormer(POSTGRESQL, SCHEMA_RT);
    public static final ObjectMapper om = new ObjectMapper().findAndRegisterModules();
    private static LightingPointSpeDao lightingPointSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        lightingPointSpeDao = new LightingPointSpeDao(Mockito.mock(DataSource.class), REALTIME, om);
    }

    @Nested
    class RealTime {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        @Test
        void queryList() {
            String expected = """
                    SELECT _id_bimcore, _perimeters, circuitnumber, controllable, documents, status, externalids, forced, infos, inhibition, installationdate, lamps, location, manager, model, name, owner, site, status, operatingstate
                    FROM "realtime"."elem_LightingPoint"
                    WHERE 1=1
                     AND name = 'randomName'
                     AND status = 0
                     AND EXISTS
                      (SELECT 1
                      FROM jsonb_object_keys(lamps) AS j(key)
                      WHERE key = ANY(ARRAY['randomLampDevice1','randomLampDevice2']))
                    ORDER BY name desc
                    LIMIT 10 OFFSET 0;""";
            StringBuilder result = lightingPointSpeDao.queryListRealtime(10L, 0L, LightingPointRealtimeOrderby.name, "desc", "randomName", 0, List.of("randomLampDevice1", "randomLampDevice2"),userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryCount() {
            String expected = """
                    SELECT COUNT(*)
                    FROM "realtime"."elem_LightingPoint"
                    WHERE 1=1
                     AND name = 'randomName'
                     AND status = 0
                     AND EXISTS
                      (SELECT 1
                      FROM jsonb_object_keys(lamps) AS j(key)
                      WHERE key = ANY(ARRAY['randomLampDevice1','randomLampDevice2']));""";
            StringBuilder result = lightingPointSpeDao.queryCountRealtime("randomName", 0, List.of("randomLampDevice1", "randomLampDevice2"),userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }
    }

}