package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.spring.ErrorHandler;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;
import org.apache.commons.lang3.tuple.Pair;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(MockitoExtension.class)
class LightingPointSpeRestTest {

    private static MockMvc mockMvc;
    private static LightingPointSpeRest lightingPointSpeRest;
    private static LightingPointSpeService lightingPointSpeService;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        lightingPointSpeService = Mockito.mock(LightingPointSpeService.class);
        CustomJwtAuthenticationConverter jwtDecoder = Mockito.mock(CustomJwtAuthenticationConverter.class);
        ThreadLocal<Pair<Boolean, List<Integer>>> mockThreadLocal = new ThreadLocal<>();
        mockThreadLocal.set(Pair.of(true, List.of(1,2,3)));
        when(jwtDecoder.get_perimeters()).thenReturn(mockThreadLocal);

        lightingPointSpeRest = new LightingPointSpeRest(lightingPointSpeService, jwtDecoder);
        mockMvc = MockMvcBuilders.standaloneSetup(lightingPointSpeRest).setControllerAdvice(new ErrorHandler()).build();
    }

    Exception get_with_paramException(String endpoint, String param) throws Exception {
        return mockMvc.perform(get("/spe/streetlight/lightingpoint/" + endpoint + "?" + param).contentType(MediaType.APPLICATION_JSON))
                .andReturn()
                .getResolvedException();
    }

    @Nested
    class RealTime {

        private final String ENDPOINT_REALTIME = "list";
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        @Test
        void get_lightningPointList_success() throws Exception {
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(lightingPointSpeService.listRealtime(null, 0L, LightingPointRealtimeOrderby.name, "desc", null, null, null,userPerimeters))
                    .willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(get("/spe/streetlight/lightingpoint/" + ENDPOINT_REALTIME).contentType(APPLICATION_JSON))
                    .andReturn()
                    .getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(lightingPointSpeService).listRealtime(null, 0L, LightingPointRealtimeOrderby.name, "desc", null, null, null,userPerimeters);
        }

        @Test
        void lightningPointList_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=1&limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void lightningPointList_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=1&offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void lightningPointList_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=1&order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void lightningPointList_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=1&orderby=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'orderby' parameter given : 'randomFalseValue'. (Possible values :");
        }

        @Test
        void lightningPointList_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=1&order=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
        }

    }

}