package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LightingPointSpeServiceTest {

    @InjectMocks
    LightingPointSpeService lightingPointSpeService;

    @Mock
    LightingPointSpeDao lightingPointSpeDao;

    @Test
    void lightingPointList_success() {
        //given
        LightingPointRealtimeDTO model = new LightingPointRealtimeDTO();
        model.set_id_bimcore("randomId");
        List<LightingPointRealtimeDTO> models = List.of(model);
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

        EnveloppeGetter expected = new EnveloppeGetter();
        EnveloppeGetter.Pagination pagination = new EnveloppeGetter.Pagination();
        pagination.setDebut(0L);
        pagination.setFin(1L);
        pagination.setTotal(1L);

        expected.setPagination(pagination);
        expected.getItems().addAll(models);

        //mock
        when(lightingPointSpeDao.listRealtime(10L, 0L, LightingPointRealtimeOrderby.name, "desc", null, null, null,userPerimeters))
                .thenReturn(models);
        when(lightingPointSpeDao.countRealtime(null, null, null,userPerimeters))
                .thenReturn(1);

        //when
        EnveloppeGetter<LightingPointRealtimeDTO> result = lightingPointSpeService.listRealtime(10L, 0L, LightingPointRealtimeOrderby.name, "desc", null, null, null,userPerimeters);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

}