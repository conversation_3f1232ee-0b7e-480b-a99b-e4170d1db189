package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LightingPointAlertDTO extends ModelSpe {

    private String id;
    private List<Integer> _perimeters;
    private String code;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant laststatuschangedate;
    private Integer priority;
    private String category;
    private String status;
}
