package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ASpeDao;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatAndSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatValueToSQL;

@Log4j2
@Repository
public class LightingPointSpeDao extends ASpeDao {

    public static final String CLASS_ALERT = "Alert";
    public static final String CLASS_INTERVENTION_REQUEST = "InterventionRequest";
    public static final String CLASS_LIGHTING_POINT = "LightingPoint";
    public static final String ALERT_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_ALERT;
    public static final String INTERVENTION_REQUEST_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_INTERVENTION_REQUEST;
    public static final String LIGHTINGPOINT_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_LIGHTING_POINT;
    public static final String REL_ALERT_LIGHTINGPOINT = DB_REALTIME_PREFIX_RELTABLE + "Alert_lightingpoint";
    public static final String REL_INTERVENTION_REQUEST_LIGHTINGPOINT = DB_REALTIME_PREFIX_RELTABLE + "InterventionRequest_lightingpoints";
    private static final String TEMPLATE_COUNTERS = """
            with alertlinked AS (
             SELECT _id_bimcore, status, creationdate, laststatuschangedate FROM ${table_alert} ea
             INNER JOIN ${rel_alert_lightingpoint} ral
             ON ral.${id_bimcore_destination} = ${id} and ea.${id_bimcore} = ral.${id_bimcore_origin}
            ),
            interventionrequestlinked AS (
             SELECT _id_bimcore, status, creationdate, lastupdate FROM ${table_interventionrequest} eir
             INNER JOIN ${rel_interventionrequest_lightingpoint} ril
             ON ril.${id_bimcore_destination} = ${id} and eir.${id_bimcore} = ril.${id_bimcore_origin}
            )
            SELECT '${class_alert}' AS class, 'Terminated' AS status, COUNT(${id_bimcore}) AS count FROM alertlinked
              WHERE "status" = 'Closed' and "laststatuschangedate" > (current_date - interval '7 days')
            union
            SELECT '${class_alert}' AS class, 'New' AS status, COUNT(${id_bimcore}) AS count FROM alertlinked
             WHERE "creationdate" > (current_date - interval '7 days')
            union
            SELECT '${class_interventionrequest}' AS class, 'Terminated' AS status, COUNT(${id_bimcore}) AS count FROM interventionrequestlinked
             WHERE "status" = 'Completed' and "lastupdate" > (current_date - interval '7 days')
            union
            SELECT '${class_interventionrequest}' AS class, 'New' AS status, COUNT(${id_bimcore}) AS count FROM interventionrequestlinked
             WHERE "creationdate" > (current_date - interval '7 days');""";

    public LightingPointSpeDao(@Qualifier("dsRealtime") DataSource dataSource, @Qualifier("qfRealtime") QueryFormer qf, ObjectMapper om) {
        super(dataSource, qf, om);
    }

    public Map<String, Integer> counters(String id) {
        String sql = queryCounters(id);
        log.debug(sql);
        Map<String, Integer> result = new HashMap<>();
        try (Connection connection = ds.getConnection(); Statement stmtQuery = connection.createStatement(); ResultSet rs = stmtQuery.executeQuery(sql)) {
            while (rs.next()) {
                result.put(rs.getString("class") + "_" + rs.getString("status"), rs.getInt("count"));
            }
        } catch (Exception e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
        return result;
    }

    public String queryCounters(@NonNull String id) {
        Map<String, Object> valuesMap = new HashMap<>();
        valuesMap.put("id", formatAndSimpleQuote(id));
        valuesMap.put("class_alert", CLASS_ALERT);
        valuesMap.put("class_interventionrequest", CLASS_INTERVENTION_REQUEST);
        valuesMap.put("table_alert", qf.addSchema(ALERT_TABLE));
        valuesMap.put("table_interventionrequest", qf.addSchema(INTERVENTION_REQUEST_TABLE));
        valuesMap.put("rel_alert_lightingpoint", qf.addSchema(REL_ALERT_LIGHTINGPOINT));
        valuesMap.put("rel_interventionrequest_lightingpoint", qf.addSchema(REL_INTERVENTION_REQUEST_LIGHTINGPOINT));
        valuesMap.put("id_bimcore", qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
        valuesMap.put("id_bimcore_origin", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
        valuesMap.put("id_bimcore_destination", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));

        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(TEMPLATE_COUNTERS);
    }

    public List<LightingPointAlertDTO> alertList(String id, Long limit, Long offset, String orderby, String order, String category, Integer priority, String status, Instant start, Instant end,Pair<Boolean, List<Integer>> userPerimeters) {
        String sql = queryAlertList(id, limit, offset, orderby, order, category, priority, status, start, end,userPerimeters);
        try {
            return execQuery(sql, LightingPointAlertDTO.class);
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryAlertList(@NonNull String id, @NonNull Long limit, @NonNull Long offset, @NonNull String orderby, @NonNull String order, String category, Integer priority, String status, Instant start, Instant end,Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ea.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" AS id");
        sql.append(", ea.code");
        sql.append(", ea.priority");
        sql.append(", ea.category");
        sql.append(", ea.laststatuschangedate");
        sql.append(", ea.status");
        sql.append(", ea._perimeters");
        sql.append(" FROM ").append(qf.addSchema(ALERT_TABLE)).append( " ea");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_ALERT_LIGHTINGPOINT)).append(" ral");
        sql.append(" ON ea.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = ral.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));

        sql.append(" WHERE ral.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(alertListWhereClauses(category, priority, status, start, end,userPerimeters));

        sql.append(" ORDER BY " + qf.addIdentifier(orderby) + " " + order);
        sql.append(" LIMIT " + limit + " OFFSET " + offset);

        sql.append(";");
        return sql.toString();
    }

    private StringBuilder alertListWhereClauses(String category, Integer priority, String status, Instant start, Instant end,Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        if (category != null) {
            sql.append(" AND ea.category = ").append(formatAndSimpleQuote(category));
        }
        if (priority != null) {
            sql.append(" AND ea.priority = ").append(priority);
        }
        if (status != null) {
            sql.append(" AND ea.status = ").append(formatAndSimpleQuote(status));
        }
        if (start != null) {
            sql.append(" AND ea.laststatuschangedate > ").append(betweenSimpleQuote(start.toString()));
        }
        if (end != null) {
            sql.append(" AND ea.laststatuschangedate < ").append(betweenSimpleQuote(end.toString()));
        }if (!userPerimeters.getLeft()) {
            sql.append(" AND ea._perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }

    public int countAlertList(String id, String category, Integer priority, String status, Instant start, Instant end,Pair<Boolean, List<Integer>> userPerimeters) {
        String sql = queryAlertListCount(id, category, priority, status, start, end, userPerimeters);
        try {
            return execCount(sql);
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryAlertListCount(String id, String category, Integer priority, String status, Instant start, Instant end,Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append(" FROM ").append(qf.addSchema(ALERT_TABLE)).append( " ea");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_ALERT_LIGHTINGPOINT)).append(" ral");
        sql.append(" ON ea.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = ral.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));

        sql.append(" WHERE ral.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(alertListWhereClauses(category, priority, status, start, end,userPerimeters));

        sql.append(";");
        return sql.toString();
    }

    public List<LightingPointInterventionRequestDTO> interventionRequestList(String id, Long limit, Long offset, String orderby, String order, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        String sql = queryInterventionRequestList(id, limit, offset, orderby, order, category, status, start, end, userPerimeters);
        try {
            return execQuery(sql, LightingPointInterventionRequestDTO.class);
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryInterventionRequestList(@NonNull String id, @NonNull Long limit, @NonNull Long offset, @NonNull String orderby, @NonNull String order, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT eir.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" AS id");
        sql.append(", eir.code");
        sql.append(", eir.category");
        sql.append(", eir.lastupdate");
        sql.append(", eir.status");
        sql.append(", eir._perimeters");
        sql.append(" FROM ").append(qf.addSchema(INTERVENTION_REQUEST_TABLE)).append( " eir");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_INTERVENTION_REQUEST_LIGHTINGPOINT)).append(" rirl");
        sql.append(" ON eir.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = rirl.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));

        sql.append(" WHERE rirl.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(interventionRequestListWhereClauses(category, status, start, end, userPerimeters));

        sql.append(" ORDER BY " + qf.addIdentifier(orderby) + " " + order);
        sql.append(" LIMIT " + limit + " OFFSET " + offset);

        sql.append(";");
        return sql.toString();
    }

    public int countInterventionRequestList(String id, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        String sql = queryInterventionRequestListCount(id, category, status, start, end, userPerimeters);
        try {
            return execCount(sql);
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryInterventionRequestListCount(String id, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append(" FROM ").append(qf.addSchema(INTERVENTION_REQUEST_TABLE)).append( " eir");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_INTERVENTION_REQUEST_LIGHTINGPOINT)).append(" rirl");
        sql.append(" ON eir.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = rirl.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));

        sql.append(" WHERE rirl.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(interventionRequestListWhereClauses(category, status, start, end, userPerimeters));

        sql.append(";");
        return sql.toString();
    }

    private StringBuilder interventionRequestListWhereClauses(String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        if (category != null) {
            sql.append(" AND eir.category = ").append(formatAndSimpleQuote(category));
        }
        if (status != null) {
            sql.append(" AND eir.status = ").append(formatAndSimpleQuote(status));
        }
        if (start != null) {
            sql.append(" AND eir.lastupdate > ").append(betweenSimpleQuote(start.toString()));
        }
        if (end != null) {
            sql.append(" AND eir.lastupdate < ").append(betweenSimpleQuote(end.toString()));
        }
        if (!userPerimeters.getLeft()) {
            sql.append(" AND eir._perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }

    public List<LightingPointRealtimeDTO> listRealtime(Long limit, @NonNull Long offset, @NonNull LightingPointRealtimeOrderby orderBy, @NonNull String order, String name, Integer status, List<String> lamps, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryListRealtime(limit, offset, orderBy, order, name, status, lamps, userPerimeters);
        try {
            return execQuery(sql.toString(), LightingPointRealtimeDTO.class);
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryListRealtime(Long limit, @NonNull Long offset, @NonNull LightingPointRealtimeOrderby orderBy, @NonNull String order, String name, Integer status, List<String> lamps, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT");
        sql.append(" _id_bimcore,");
        sql.append(" _perimeters,");
        sql.append(" circuitnumber,");
        sql.append(" controllable,");
        sql.append(" documents,");
        sql.append(" status,");
        sql.append(" externalids,");
        sql.append(" forced,");
        sql.append(" infos,");
        sql.append(" inhibition,");
        sql.append(" installationdate,");
        sql.append(" lamps,");
        sql.append(" location,");
        sql.append(" manager,");
        sql.append(" model,");
        sql.append(" name,");
        sql.append(" owner,");
        sql.append(" site,");
        sql.append(" status,");
        sql.append(" operatingstate");

        sql.append("\nFROM " + qf.addSchema(LIGHTINGPOINT_TABLE));

        sql.append(whereClausesRealtime(name, status, lamps, userPerimeters));
        sql.append("\nORDER BY " + orderByRealtime(orderBy) + " " + order);
        if (limit != null) {
            sql.append("\nLIMIT " + limit + " OFFSET " + offset + ";");
        } else { // LIMIT ALL is equivalent to omitting LIMIT clause, needed for OFFSET if it's used
            sql.append("\nLIMIT ALL OFFSET " + offset + ";");
        }
        return sql;
    }

    public StringBuilder whereClausesRealtime(String name, Integer status, List<String> lamps, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("\nWHERE 1=1");
        if (name != null) {
            sql.append("\n AND name = " + betweenSimpleQuote(formatValueToSQL(name)));
        }
        if (status != null) {
            sql.append("\n AND status = " + status);
        }
        if (lamps != null) {
            sql.append("\n AND EXISTS");
            sql.append("\n  (SELECT 1");
            sql.append("\n  FROM jsonb_object_keys(lamps) AS j(key)");
            sql.append("\n  WHERE key = ANY(ARRAY[");
            if (!lamps.isEmpty()) {
                // append every lamp devicename strings into ARRAY
                sql.append(lamps.stream()
                        .map(lampDeviceName -> betweenSimpleQuote(formatValueToSQL(lampDeviceName)))
                        .collect(Collectors.joining(",")));
            }
            // else if lamps is empty but not null, look for lamps with key ''
            else {
                sql.append("''");
            }
            sql.append("]))");
        }
        if (!userPerimeters.getLeft()) {
            sql.append("\n AND _perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }

    private String orderByRealtime(LightingPointRealtimeOrderby orderBy) {
        return orderBy.name();
    }

    public int countRealtime(String name, Integer status, List<String> lamps, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryCountRealtime(name, status, lamps, userPerimeters);
        try {
            return execCount(sql.toString());
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryCountRealtime(String name, Integer status, List<String> lamps, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append("\nFROM " + qf.addSchema(LIGHTINGPOINT_TABLE));
        sql.append(whereClausesRealtime(name, status, lamps, userPerimeters));
        sql.append(";");
        return sql;
    }
}
