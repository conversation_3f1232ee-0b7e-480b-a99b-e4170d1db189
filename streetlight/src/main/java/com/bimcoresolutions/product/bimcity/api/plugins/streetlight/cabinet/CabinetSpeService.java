package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.cabinet;

import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Service
public class CabinetSpeService {
    private final CabinetSpeDao cabinetSpeDao;

    public CabinetSpeService(CabinetSpeDao cabinetSpeDao) {
        this.cabinetSpeDao = cabinetSpeDao;
    }

    public Map<String, Integer> counters(String _id_bimcore) {
        return cabinetSpeDao.counters(_id_bimcore);
    }

    public EnveloppeGetter<CabinetAlertDTO> alertList(String _id_bimcore, Long limit, Long offset, String orderby, String order, String category, Integer priority, String status, Instant start, Instant end) {
        List<CabinetAlertDTO> cabinetAlertDTOList = cabinetSpeDao.alertList(_id_bimcore, limit, offset, orderby, order, category, priority, status, start, end);
        if (cabinetAlertDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = cabinetSpeDao.countAlertList(_id_bimcore, category, priority, status, start, end);
        return buildEnveloppeGetter(offset, cabinetAlertDTOList, total);
    }

    public EnveloppeGetter<CabinetInterventionRequestDTO> interventionRequestList(String _id_bimcore, Long limit, Long offset, String orderby, String order, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<CabinetInterventionRequestDTO> cabinetInterventionRequestDTOList = cabinetSpeDao.interventionRequestList(_id_bimcore, limit, offset, orderby, order, category, status, start, end, userPerimeters);
        if (cabinetInterventionRequestDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = cabinetSpeDao.countInterventionRequestList(_id_bimcore, category, status, start, end, userPerimeters);
        return buildEnveloppeGetter(offset, cabinetInterventionRequestDTOList, total);
    }

    private <T> EnveloppeGetter<T> buildEnveloppeGetter(Long offset, List<T> resultat, int total) {
        EnveloppeGetter<T> toRet = new EnveloppeGetter<>();
        toRet.getPagination().setTotal((long) total);
        toRet.setItems(resultat);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + resultat.size());
        return toRet;
    }
}
