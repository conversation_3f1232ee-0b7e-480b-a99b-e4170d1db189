package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.cabinet;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = { "CabinetSpeRest" })
@RequestMapping("/spe/streetlight/cabinet")
public class CabinetSpeRest extends JwtDecoder {

    private final CabinetSpeService cabinetSpeService;

    public CabinetSpeRest(CabinetSpeService cabinetSpeService, CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.cabinetSpeService = cabinetSpeService;
    }

    @GetMapping(value = "/{_id_bimcore}/counters", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_Cabinet_show" })
    public Map<String, Integer> counters(@PathVariable String _id_bimcore) {
        return cabinetSpeService.counters(_id_bimcore);
    }

    @GetMapping(value = "/{_id_bimcore}/alerts", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_Cabinet_show", "ROLE_Alert_show" })
    public EnveloppeGetter<CabinetAlertDTO> alertList(
            @PathVariable String _id_bimcore,
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "laststatuschangedate") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("priority") @RequestParam(required = false) Integer priority,
            @ModelAttribute("status") @RequestParam(required = false) String status,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("laststatuschangedate", "category", "status").contains(orderby)) {
            throw new WrongArgumentValueException("Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : laststatuschangedate,category,status)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        return cabinetSpeService.alertList(_id_bimcore, limit, offset, orderby, order, category, priority, status, start, end);
    }

    @GetMapping(value = "/{_id_bimcore}/interventionrequests", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_Cabinet_show", "ROLE_InterventionRequest_show" })
    public EnveloppeGetter<CabinetInterventionRequestDTO> interventionRequestList(
            @PathVariable String _id_bimcore,
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "lastupdate") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("status") @RequestParam(required = false) String status,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();

        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("lastupdate", "category", "status").contains(orderby)) {
            throw new WrongArgumentValueException("Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : lastupdate,category,status)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        return cabinetSpeService.interventionRequestList(_id_bimcore, limit, offset, orderby, order, category, status, start, end, userPerimeters);
    }

}
