package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.cabinet;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ASpeDao;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatAndSimpleQuote;

@Repository
public class CabinetSpeDao extends ASpeDao {

    private static final Logger LOGGER = LogManager.getLogger();
    public static final String CLASS_ALERT = "Alert";
    public static final String CLASS_CABINET = "Cabinet";
    public static final String CLASS_INTERVENTION_REQUEST = "InterventionRequest";
    public static final String ALERT_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_ALERT;
    public static final String INTERVENTION_REQUEST_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_INTERVENTION_REQUEST;
    public static final String REL_ALERT_CABINET = DB_REALTIME_PREFIX_RELTABLE + "Alert_cabinet";
    public static final String REL_CABINET_INTERVENTION_REQUEST = DB_REALTIME_PREFIX_RELTABLE + "Cabinet_interventionrequests";

    public CabinetSpeDao(@Qualifier("dsRealtime") DataSource dataSource, @Qualifier("qfRealtime") QueryFormer qf, ObjectMapper om) {
        super(dataSource, qf, om);
    }

    public Map<String, Integer> counters(String id) {
        String sql = queryCounters(id);
        Map<String, Integer> result = new HashMap<>();
        try (Connection connection = ds.getConnection(); Statement stmtQuery = connection.createStatement(); ResultSet rs = stmtQuery.executeQuery(sql)) {
            while (rs.next()) {
                result.put(rs.getString("class") + "_" + rs.getString("status"), rs.getInt("count"));
            }
        } catch (Exception e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
        return result;
    }

    public String queryCounters(@NonNull String id) {
        Map<String, Object> valuesMap = new HashMap<>();
        valuesMap.put("id", formatAndSimpleQuote(id));
        valuesMap.put("class_alert", CLASS_ALERT);
        valuesMap.put("class_interventionrequest", CLASS_INTERVENTION_REQUEST);
        valuesMap.put("table_alert", qf.addSchema(ALERT_TABLE));
        valuesMap.put("table_interventionrequest", qf.addSchema(INTERVENTION_REQUEST_TABLE));
        valuesMap.put("rel_alert_cabinet", qf.addSchema(REL_ALERT_CABINET));
        valuesMap.put("rel_cabinet_interventionrequest", qf.addSchema(REL_CABINET_INTERVENTION_REQUEST));
        valuesMap.put("_id_bimcore", qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
        valuesMap.put("_id_bimcore_origin", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
        valuesMap.put("_id_bimcore_destination", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));

        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(TEMPLATE_COUNTERS);
    }

    private static final String TEMPLATE_COUNTERS = """
            with alertlinked AS (
             SELECT _id_bimcore, status, creationdate, laststatuschangedate FROM ${table_alert} ea
             INNER JOIN ${rel_alert_cabinet} rac
             ON rac.${_id_bimcore_destination} = ${id} and ea.${_id_bimcore} = rac.${_id_bimcore_origin}
            ),
            interventionrequestlinked AS (
             SELECT _id_bimcore, status, creationdate, lastupdate FROM ${table_interventionrequest} eir
             INNER JOIN ${rel_cabinet_interventionrequest} rci
             ON rci.${_id_bimcore_origin} = ${id} and eir.${_id_bimcore} = rci.${_id_bimcore_destination}
            )
            SELECT '${class_alert}' AS class, 'Terminated' AS status, COUNT(${_id_bimcore}) AS count FROM alertlinked
              WHERE "status" = 'Closed' and "laststatuschangedate" > (current_date - interval '7 days')
            union
            SELECT '${class_alert}' AS class, 'New' AS status, COUNT(${_id_bimcore}) AS count FROM alertlinked
             WHERE "creationdate" > (current_date - interval '7 days')
            union
            SELECT '${class_interventionrequest}' AS class, 'Terminated' AS status, COUNT(${_id_bimcore}) AS count FROM interventionrequestlinked
             WHERE "status" = 'Completed' and "lastupdate" > (current_date - interval '7 days')
            union
            SELECT '${class_interventionrequest}' AS class, 'New' AS status, COUNT(${_id_bimcore}) AS count FROM interventionrequestlinked
             WHERE "creationdate" > (current_date - interval '7 days');""";

    public List<CabinetAlertDTO> alertList(String id, Long limit, Long offset, String orderby, String order, String category, Integer priority, String status, Instant start, Instant end) {
        String sql = queryAlertList(id, limit, offset, orderby, order, category, priority, status, start, end);
        try {
            return execQuery(sql, CabinetAlertDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryAlertList(@NonNull String id, @NonNull Long limit, @NonNull Long offset, @NonNull String orderby, @NonNull String order, String category, Integer priority, String status, Instant start, Instant end) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ea.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
        sql.append(", ea.code");
        sql.append(", ea.priority");
        sql.append(", ea.category");
        sql.append(", ea.laststatuschangedate");
        sql.append(", ea.status");
        sql.append(", ea._perimeters");
        sql.append(" FROM ").append(qf.addSchema(ALERT_TABLE)).append(" ea");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_ALERT_CABINET)).append(" rac");
        sql.append(" ON ea.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = rac.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));

        sql.append(" WHERE rac.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(alertListWhereClauses(category, priority, status, start, end));

        sql.append(" ORDER BY " + qf.addIdentifier(orderby) + " " + order);
        sql.append(" LIMIT " + limit + " OFFSET " + offset);

        sql.append(";");
        return sql.toString();
    }

    public int countAlertList(String id, String category, Integer priority, String status, Instant start, Instant end) {
        String sql = queryAlertListCount(id, category, priority, status, start, end);
        try {
            return execCount(sql);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryAlertListCount(String id, String category, Integer priority, String status, Instant start, Instant end) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append(" FROM ").append(qf.addSchema(ALERT_TABLE)).append(" ea");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_ALERT_CABINET)).append(" rac");
        sql.append(" ON ea.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = rac.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));

        sql.append(" WHERE rac.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(alertListWhereClauses(category, priority, status, start, end));

        sql.append(";");
        return sql.toString();
    }

    private StringBuilder alertListWhereClauses(String category, Integer priority, String status, Instant start, Instant end) {
        StringBuilder sql = new StringBuilder();
        if (category != null) {
            sql.append(" AND ea.category = ").append(formatAndSimpleQuote(category));
        }
        if (priority != null) {
            sql.append(" AND ea.priority = ").append(priority);
        }
        if (status != null) {
            sql.append(" AND ea.status = ").append(formatAndSimpleQuote(status));
        }
        if (start != null) {
            sql.append(" AND ea.laststatuschangedate > ").append(betweenSimpleQuote(start.toString()));
        }
        if (end != null) {
            sql.append(" AND ea.laststatuschangedate < ").append(betweenSimpleQuote(end.toString()));
        }
        return sql;
    }

    public List<CabinetInterventionRequestDTO> interventionRequestList(String id, Long limit, Long offset, String orderby, String order, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        String sql = queryInterventionRequestList(id, limit, offset, orderby, order, category, status, start, end, userPerimeters);
        try {
            return execQuery(sql, CabinetInterventionRequestDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryInterventionRequestList(@NonNull String id, @NonNull Long limit, @NonNull Long offset, @NonNull String orderby, @NonNull String order, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT eir.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
        sql.append(", eir.code");
        sql.append(", eir.category");
        sql.append(", eir.lastupdate");
        sql.append(", eir.status");
        sql.append(", eir._perimeters");
        sql.append(" FROM ").append(qf.addSchema(INTERVENTION_REQUEST_TABLE)).append(" eir");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_CABINET_INTERVENTION_REQUEST)).append(" rcir");
        sql.append(" ON eir.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = rcir.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));

        sql.append(" WHERE rcir.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(interventionRequestListWhereClauses(category, status, start, end, userPerimeters));

        sql.append(" ORDER BY " + qf.addIdentifier(orderby) + " " + order);
        sql.append(" LIMIT " + limit + " OFFSET " + offset);

        sql.append(";");
        return sql.toString();
    }

    public int countInterventionRequestList(String id, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        String sql = queryInterventionRequestListCount(id, category, status, start, end, userPerimeters);
        try {
            return execCount(sql);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public String queryInterventionRequestListCount(String id, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append(" FROM ").append(qf.addSchema(INTERVENTION_REQUEST_TABLE)).append(" eir");
        sql.append(" INNER JOIN ").append(qf.addSchema(REL_CABINET_INTERVENTION_REQUEST)).append(" rcir");
        sql.append(" ON eir.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = rcir.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));

        sql.append(" WHERE rcir.").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN)).append(" = ").append(formatAndSimpleQuote(id));
        sql.append(interventionRequestListWhereClauses(category, status, start, end, userPerimeters));

        sql.append(";");
        return sql.toString();
    }

    private StringBuilder interventionRequestListWhereClauses(String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        if (category != null) {
            sql.append(" AND eir.category = ").append(formatAndSimpleQuote(category));
        }
        if (status != null) {
            sql.append(" AND eir.status = ").append(formatAndSimpleQuote(status));
        }
        if (start != null) {
            sql.append(" AND eir.lastupdate > ").append(betweenSimpleQuote(start.toString()));
        }
        if (end != null) {
            sql.append(" AND eir.lastupdate < ").append(betweenSimpleQuote(end.toString()));
        }
        if (!userPerimeters.getLeft()) {
            sql.append(" AND eir._perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }

}
