package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Service
public class LightingPointSpeService {
    private final LightingPointSpeDao lightingpointSpeDao;

    public LightingPointSpeService(LightingPointSpeDao lightingpointSpeDao) {
        this.lightingpointSpeDao = lightingpointSpeDao;
    }

    public Map<String, Integer> counters(String id) {
        return lightingpointSpeDao.counters(id);
    }

    public EnveloppeGetter<LightingPointAlertDTO> alertList(String id, Long limit, Long offset, String orderby, String order, String category, Integer priority, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<LightingPointAlertDTO> lightingpointAlertDTOList = lightingpointSpeDao.alertList(id, limit, offset, orderby, order, category, priority, status, start, end,userPerimeters);
        if (lightingpointAlertDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = lightingpointSpeDao.countAlertList(id, category, priority, status, start, end, userPerimeters);
        return buildEnveloppeGetter(offset, lightingpointAlertDTOList, total);
    }

    public EnveloppeGetter<LightingPointInterventionRequestDTO> interventionRequestList(String id, Long limit, Long offset, String orderby, String order, String category, String status, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<LightingPointInterventionRequestDTO> lightingpointInterventionRequestDTOList = lightingpointSpeDao.interventionRequestList(id, limit, offset, orderby, order, category, status, start, end, userPerimeters);
        if (lightingpointInterventionRequestDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = lightingpointSpeDao.countInterventionRequestList(id, category, status, start, end, userPerimeters);
        return buildEnveloppeGetter(offset, lightingpointInterventionRequestDTOList, total);
    }

    public EnveloppeGetter<LightingPointRealtimeDTO> listRealtime(
            Long limit, Long offset, LightingPointRealtimeOrderby orderBy, String order, String name,
            Integer status, List<String> lamps, Pair<Boolean, List<Integer>> userPerimeters) {
        List<LightingPointRealtimeDTO> lightingPointDTOList = lightingpointSpeDao.listRealtime(
                limit, offset, orderBy, order, name, status, lamps, userPerimeters);
        if (lightingPointDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = lightingpointSpeDao.countRealtime(name, status, lamps, userPerimeters);
        return buildEnveloppeGetter(offset, lightingPointDTOList, total);
    }

    private <T> EnveloppeGetter<T> buildEnveloppeGetter(Long offset, List<T> resultat, int total) {
        EnveloppeGetter<T> toRet = new EnveloppeGetter<>();
        toRet.getPagination().setTotal((long) total);
        toRet.setItems(resultat);
        if (offset != null){
            toRet.getPagination().setDebut(offset);
            toRet.getPagination().setFin(offset + resultat.size());
        } else {
            toRet.getPagination().setDebut(0L);
            toRet.getPagination().setFin((long) resultat.size());
        }
        return toRet;
    }
}
