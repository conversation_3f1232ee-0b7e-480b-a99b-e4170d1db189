package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = { "LightingPointSpeRest" })
@RequestMapping("/spe/streetlight/lightingpoint")
public class LightingPointSpeRest extends JwtDecoder {

    private final LightingPointSpeService lightingpointSpeService;

    public LightingPointSpeRest(LightingPointSpeService lightingpointSpeService, CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.lightingpointSpeService = lightingpointSpeService;
    }

    @GetMapping(value = "/{id}/counters", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_LightingPoint_show" })
    public Map<String, Integer> counters(@PathVariable String id) {
        return lightingpointSpeService.counters(id);
    }

    @GetMapping(value = "/{id}/alerts", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_LightingPoint_show", "ROLE_Alert_show" })
    public EnveloppeGetter<LightingPointAlertDTO> alertList(
            @PathVariable String id,
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "laststatuschangedate") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("priority") @RequestParam(required = false) Integer priority,
            @ModelAttribute("status") @RequestParam(required = false) String status,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("laststatuschangedate", "category", "status").contains(orderby)) {
            throw new WrongArgumentValueException("Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : laststatuschangedate,category,status)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        return lightingpointSpeService.alertList(id, limit, offset, orderby, order, category, priority, status, start, end,userPerimeters);
    }

    @GetMapping(value = "/{id}/interventionrequests", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_LightingPoint_show", "ROLE_InterventionRequest_show" })
    public EnveloppeGetter<LightingPointInterventionRequestDTO> interventionRequestList(
            @PathVariable String id,
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "lastupdate") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("status") @RequestParam(required = false) String status,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();

        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("lastupdate", "category", "status").contains(orderby)) {
            throw new WrongArgumentValueException("Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : lastupdate,category,status)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        return lightingpointSpeService.interventionRequestList(id, limit, offset, orderby, order, category, status, start, end, userPerimeters);
    }

    @GetMapping(value = "/list", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_LightingPoint_show", "ROLE_LightingPoint_read"})
    public EnveloppeGetter<LightingPointRealtimeDTO> lightingPointList(
            @ModelAttribute("limit") @RequestParam(required = false) Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "name") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("name") @RequestParam(required = false) String name,
            @ModelAttribute("status") @RequestParam(required = false) Integer status,
            @ModelAttribute("lamps") @RequestParam(required = false) List<String> lamps) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();

        checkLimitOffsetOrder(limit, offset, order);

        LightingPointRealtimeOrderby orderBy;
        if (!LightingPointRealtimeOrderby.nameValues().contains(orderby)) {
            throw new WrongArgumentValueException(
                    "Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : " + LightingPointRealtimeOrderby.nameValues() + ")");
        } else {
            orderBy = LightingPointRealtimeOrderby.valueOf(orderby);
        }

        return lightingpointSpeService.listRealtime(limit, offset, orderBy, order, name, status, lamps, userPerimeters);
    }

    private void checkLimitOffsetOrder(Long limit, Long offset, String order) {
        if (limit != null && limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
    }

}
