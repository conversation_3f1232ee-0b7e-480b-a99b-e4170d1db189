package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import com.bimcoresolutions.util.base.serialisation.GeometrySerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import org.locationtech.jts.geom.Geometry;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LightingPointRealtimeDTO extends ModelSpe {
    private String _id_bimcore;
    private List<Integer> _perimeters;
    private String circuitnumber;
    private Boolean controllable;
    private JsonNode documents;
    private JsonNode externalids;
    private Integer forced;
    private JsonNode infos;
    private Boolean inhibition;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant installationdate;
    private JsonNode lamps;
    @JsonSerialize(using = GeometrySerializer.class)
    private Geometry location;
    private String manager;
    private String model;
    private String name;
    private String owner;
    private String site;
    private Integer status;
    private Integer operatingstate;
}
