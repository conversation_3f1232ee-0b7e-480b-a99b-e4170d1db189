package com.bimcoresolutions.product.bimcity.api.plugins.streetlight.lightingpoint;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LightingPointDTO {
    private String id;
    private List<Integer> _perimeters;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant lastupdate;
    private String name;
    private String source;
    private String equipmentName;
    private String equipmentType;
}
