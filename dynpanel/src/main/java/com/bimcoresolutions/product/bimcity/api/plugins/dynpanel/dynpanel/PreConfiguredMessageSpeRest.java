package com.bimcoresolutions.product.bimcity.api.plugins.dynpanel.dynpanel;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@Api(tags = {"PreConfiguredMessageSpeRest"})
@RequestMapping("/spe/dynpanel/preconfiguredmessage")
public class PreConfiguredMessageSpeRest {

    private final PreConfiguredMessageSpeService dynPanelSpeService;

    public PreConfiguredMessageSpeRest(PreConfiguredMessageSpeService dynPanelSpeService) {
        this.dynPanelSpeService = dynPanelSpeService;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @ApiOperation(value = "messagelist", httpMethod = "GET", notes = "obtenir la liste des messages préparamétrés")
    @Secured({"ROLE_admin", "ROLE_DynPanel_show"})
    public List<PreConfiguredMessageDTO> listMessages() {
        return dynPanelSpeService.listMessages();
    }

    @PostMapping(produces = "application/json", consumes = "application/json")
    @ApiOperation(value = "createmessages", httpMethod = "POST", notes = "créer de nouveaux messages préparamétrés")
    @ResponseStatus(HttpStatus.CREATED)
    @Secured({ "ROLE_admin", "ROLE_DynPanel_manage" })
    public Map<String, Integer> createMessages(@RequestBody List<PreConfiguredMessageDTO> list) {
        return dynPanelSpeService.createMessages(list);
    }

    @PatchMapping(produces = "application/json", consumes = "application/json")
    @ApiOperation(value = "updatemessages", httpMethod = "POST", notes = "Mettre à jour des messages préparamétrés")
    @Secured({ "ROLE_admin", "ROLE_DynPanel_manage" })
    public Map<String, Integer> updateMessages(@RequestBody List<PreConfiguredMessageDTO> list) {
        return dynPanelSpeService.updateMessages(list);
    }
}
