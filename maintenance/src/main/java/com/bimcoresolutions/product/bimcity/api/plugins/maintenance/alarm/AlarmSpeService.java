package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm;

import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Service
public class AlarmSpeService {
    private final AlarmSpeDao alarmDao;

    public AlarmSpeService(AlarmSpeDao alarmDao) {
        this.alarmDao = alarmDao;
    }

    public EnveloppeGetter<AlarmDTO> alarmList(long limit, long offset, String orderBy, String order, Set<EquipmentClass> equipmentClasses, String defaultType,
            String equipementName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<AlarmDTO> alarmDTOList = alarmDao.list(limit, offset, orderBy, order, equipmentClasses, defaultType, equipementName, source, start, end, userPerimeters);
        if (alarmDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        EnveloppeGetter<AlarmDTO> toRet = new EnveloppeGetter<>();
        toRet.getItems().addAll(alarmDTOList);
        int total = alarmDao.count(equipmentClasses, defaultType, equipementName, source, start, end, userPerimeters);
        toRet.getPagination().setTotal((long) total);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + alarmDTOList.size());
        return toRet;
    }

    public List typesList(Set<EquipmentClass> eqs) {
        List alarmDTOList = alarmDao.typesList(eqs);
        if (alarmDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }
        return alarmDTOList;
    }

    public List<String> sources() {
        return alarmDao.sources();
    }
}
