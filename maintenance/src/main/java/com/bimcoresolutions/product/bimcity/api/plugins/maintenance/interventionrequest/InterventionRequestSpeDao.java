package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelRelation;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.AbstractSpeDao;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PeriodFilter;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatValueToSQL;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Repository
public class InterventionRequestSpeDao extends AbstractSpeDao<InterventionRequestDTO, InterventionRequestFilter> {

    private static final String CLASS_INTERVENTION_REQUEST = "InterventionRequest";
    private static final String INTERVENTION_REQUEST_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_INTERVENTION_REQUEST;
    private final Set<EquipmentClass> equipmentClassesInThisProject = new TreeSet<>();
    private final Map<String, BimCoreModelRelation> targetRelName = new TreeMap<>();

    private final QueryFormer qf;

    public InterventionRequestSpeDao(DataSource dataSource, FunctionalModelService functionalModelService, @Qualifier("qfRealtime") QueryFormer qf) {
        super(dataSource);
        this.qf = qf;
        for (BimCoreModelRelation bcr : functionalModelService.getModel().getElementsBimCore().get(CLASS_INTERVENTION_REQUEST).getRelations().values()) {
            String target = CLASS_INTERVENTION_REQUEST.equals(bcr.getElementOrigin().getName()) ? bcr.getElementDestination().getName() : bcr.getElementOrigin().getName();
            if (EquipmentClass.getNames().contains(target)) {
                equipmentClassesInThisProject.add(EquipmentClass.valueOf(target));
                if (bcr.isDirect()) {
                    targetRelName.put(target, bcr);
                } else {
                    targetRelName.put(target, bcr.getReverseRelation());
                }
            }
        }
    }

    public Set<EquipmentClass> getEquipmentClassesInThisProject() {
        return equipmentClassesInThisProject;
    }

    @Override
    protected String getTemplateCount() {
        return """
                SELECT COUNT(DISTINCT(ir."_id_bimcore"))
                FROM ${schema}."elem_InterventionRequest" ir${left_join_equipments}
                WHERE 1=1${filter}
                ;""";
    }

    @Override
    public String queryBddCount(InterventionRequestFilter filter, String schema) {
        Map<String, Object> valuesMap = new HashMap<>();
        valuesMap.put("schema", schema);
        valuesMap.put("left_join_equipments", buildCountLeftJoinEquipments(filter));
        valuesMap.put("filter", buildFilter(filter));

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        return sub.replace(getTemplateCount());
    }

    @Override
    protected String getTemplateList() {
        return """
                SELECT ir._id_bimcore, ir._perimeters, ir.category, ir.code, ir.comment, ir.creationdate,
                 ir.lastupdate, ir.location, ir.sourcesystem, ir.status,${equipment_types_and_names},ir.deadline
                FROM ${schema}."elem_InterventionRequest" ir${left_join_equipments}
                WHERE 1=1${filter}
                GROUP BY ir._id_bimcore, ir._perimeters, ir.category, ir.code, ir.comment, ir.creationdate,
                 ir.lastupdate, ir.location, ir.sourcesystem, ir.status
                ${paging}
                ;""";
    }

    @Override
    public String queryBddList(InterventionRequestFilter filter, String schema) {
        Map<String, Object> valuesMap = new HashMap<>();
        valuesMap.put("schema", schema);
        valuesMap.put("equipment_types_and_names", buildequipmentTypeAndNames());
        valuesMap.put("left_join_equipments", buildLeftJoinEquipments(filter));
        valuesMap.put("filter", buildFilter(filter));
        valuesMap.put("paging", buildPaging(filter));

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        return sub.replace(getTemplateList());
    }

    public String buildCountLeftJoinEquipments(InterventionRequestFilter interventionRequestFilter) {
        if (isNotBlank(interventionRequestFilter.getEquipmentName())) {
            return buildLeftJoinEquipments(interventionRequestFilter);
        } else {
            return "";
        }
    }

    public String buildLeftJoinEquipments(InterventionRequestFilter interventionRequestFilter) {
        StringBuilder query = new StringBuilder();
        char c = 'a';
        for (EquipmentClass e : equipmentClassesInThisProject) {
            String colDi;
            String colEquipment;
            if (CLASS_INTERVENTION_REQUEST.equals(targetRelName.get(e.name()).getElementOrigin().getName())) {
                colDi = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                colEquipment = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
            } else {
                colDi = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                colEquipment = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
            }
            query.append("\nLEFT JOIN ").append(qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + targetRelName.get(e.name()).getRelationTableName())).append(" " + c);
            query.append("\n ON ir.").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = " + c + ".").append(qf.addIdentifier(colDi));
            query.append("\nLEFT JOIN (SELECT _id_bimcore, name, '").append(e.name()).append("' AS type FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()))
                    .append(") " + c + c);
            query.append("\n ON " + c + ".").append(qf.addIdentifier(colEquipment)).append(" = " + c + c + ".")
                    .append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
            c++;
        }
        return query.toString();
    }

    public String buildequipmentTypeAndNames() {
        StringBuilder query = new StringBuilder();
        StringBuilder type = new StringBuilder();
        StringBuilder name = new StringBuilder();
        char c = 'a';
        for (int i = 0; i < equipmentClassesInThisProject.size(); i++) {
            type.append("array_agg(distinct " + c + c + ".type) || ");
            name.append("array_agg(distinct " + c + c + ".name) || ");
            c++;
        }
        type.delete(type.length() - 4, type.length());
        name.delete(name.length() - 4, name.length());
        query.append("\n array_remove(");
        query.append(type);
        query.append(", null) AS ").append(qf.addIdentifier("equipmentType")).append(",");
        query.append("\n array_remove(");
        query.append(name);
        query.append(", null) AS ").append(qf.addIdentifier("equipmentName"));
        return query.toString();
    }

    @Override
    protected String buildFilter(InterventionRequestFilter interventionRequestFilter) {
        StringBuilder query = new StringBuilder();
        if (isNotBlank(interventionRequestFilter.getEquipmentName())) {
            char c = 'a';
            query.append("\n AND (");
            for (int i = 0; i < equipmentClassesInThisProject.size(); i++) {
                query.append("" + c + c + ".name ILIKE ").append(betweenSimpleQuote("%" + formatValueToSQL(interventionRequestFilter.getEquipmentName()) + "%"));
                query.append("\n  OR ");
                c++;
            }
            query.delete(query.length() - 6, query.length());
            query.append(")");
        }
        if (isNotBlank(interventionRequestFilter.getCode())) {
            query.append("\n AND ir.code ILIKE " + betweenSimpleQuote("%" + formatValueToSQL(interventionRequestFilter.getCode()) + "%"));
        }
        if (isNotBlank(interventionRequestFilter.getCategory())) {
            query.append("\n AND ir.category ILIKE " + betweenSimpleQuote("%" + formatValueToSQL(interventionRequestFilter.getCategory()) + "%"));
        }
        if (isNotBlank(interventionRequestFilter.getStatus())) {
            query.append("\n AND ir.status = " + betweenSimpleQuote(formatValueToSQL(interventionRequestFilter.getStatus())));
        }
        if (interventionRequestFilter.getPeriod() != null) {
            query.append(between(interventionRequestFilter.getPeriod()));
        }
        if (interventionRequestFilter.getUserPerimeters() != null && !interventionRequestFilter.getUserPerimeters().getLeft()) {
            List<Integer> perimeters = new ArrayList<>(interventionRequestFilter.getUserPerimeters().getRight());
            query.append("\n AND ir._perimeters && ARRAY[")
                 .append(perimeters.stream().map(Object::toString).collect(Collectors.joining(",")))
                 .append("]::integer[]");
        }
        return query.toString();
    }

    @Override
    protected String orderBy(InterventionRequestFilter paginatedFilter) {
        if (paginatedFilter.getOrderby() == InterventionRequestFilter.InterventionFilterOrderby.equipmentName) {
            return qf.addIdentifier("equipmentName") + " " + paginatedFilter.getOrder();
        } else {
            return "ir." + paginatedFilter.getOrderby() + " " + paginatedFilter.getOrder();
        }
    }

    private String between(PeriodFilter period) {
        String query = "";
        if (period.getStart() != null) {
            query += "\n AND ir.lastupdate >= '" + period.getStart() + "'";
        }
        if (period.getEnd() != null) {
            query += "\n AND ir.lastupdate <= '" + period.getEnd() + "'";
        }
        return query;
    }

    @Override
    protected InterventionRequestDTO resultsetToObject(ResultSet rs) {
        InterventionRequestDTO.InterventionRequestDTOBuilder tmp = InterventionRequestDTO.builder();
        tmp._id_bimcore(nullIfNull(rs, "_id_bimcore", String.class));
        tmp._perimeters(getIntegerList(rs,"_perimeters"));
        tmp.category(nullIfNull(rs, "category", String.class));
        tmp.code(nullIfNull(rs, "code", String.class));
        tmp.comment(nullIfNull(rs, "comment", String.class));
        tmp.creationdate(nullIfNullDate(rs, "creationdate"));
        tmp.lastupdate(nullIfNullDate(rs, "lastupdate"));
        tmp.location(nullIfLocation(rs, "location"));
        tmp.sourcesystem(nullIfNull(rs, "sourcesystem", String.class));
        tmp.status(nullIfNull(rs, "status", String.class));
        tmp.equipmentName(emptyIfNull(rs, "equipmentName"));
        tmp.equipmentType(emptyIfNull(rs, "equipmentType"));
        tmp.deadline(nullIfNullDate(rs, "deadline"));
        InterventionRequestDTO interventionRequest = tmp.build();
        return interventionRequest;
    }
}