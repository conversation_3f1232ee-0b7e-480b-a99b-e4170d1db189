package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.util.base.serialisation.GeometryDeserializer;
import com.bimcoresolutions.util.base.serialisation.GeometrySerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Builder;
import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.time.Instant;
import java.util.List;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL;

@Data
@Builder
public class InterventionRequestDTO {
    private String _id_bimcore;
    private List<Integer> _perimeters;
    private String category;
    private String code;
    private String comment;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant creationdate;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant lastupdate;
    @JsonInclude(NON_NULL)
    @JsonSerialize(using = GeometrySerializer.class)
    @JsonDeserialize(using = GeometryDeserializer.class)
    private Geometry location;
    private String sourcesystem;
    private String status;
    private List<String> equipmentName;
    private List<String> equipmentType;
    private Instant deadline;
}
