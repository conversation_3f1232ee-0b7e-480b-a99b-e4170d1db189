package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert;

import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Service
public class AlertSpeService {
    private final AlertSpeDao alertDao;

    public AlertSpeService(AlertSpeDao alertDao) {
        this.alertDao = alertDao;
    }

    public EnveloppeGetter<AlertDTO> alertRealTimeList(Long limit, Long offset, String orderBy, String order, Set<EquipmentClass> equipmentClasses, Integer priority,
            EAlertStatus status, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<AlertDTO> alertDTOList = alertDao.alertRealtimeList(limit, offset, orderBy, order, equipmentClasses, priority, status, equipmentName, source, start, end, userPerimeters);
        if (alertDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }
        int total = alertDao.countRealTime(equipmentClasses, priority, status, equipmentName, source, start, end, userPerimeters);
        return buildEnveloppeGetter(offset, alertDTOList, total);
    }

    public EnveloppeGetter<AlertDTO> alertHistoList(Long limit, Long offset, String orderBy, String order, Set<EquipmentClass> equipmentClasses, Integer priority,
            EAlertStatus status, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<AlertDTO> alertDTOList = alertDao.alertHistoList(limit, offset, orderBy, order, equipmentClasses, priority, status, equipmentName, source, start, end, userPerimeters);
        if (alertDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = alertDao.countHisto(equipmentClasses, priority, status, equipmentName, source, start, end, userPerimeters);
        return buildEnveloppeGetter(offset, alertDTOList, total);
    }

    private EnveloppeGetter<AlertDTO> buildEnveloppeGetter(Long offset, List<AlertDTO> resultat, int total) {
        EnveloppeGetter<AlertDTO> toRet = new EnveloppeGetter<>();
        toRet.getPagination().setTotal((long) total);
        toRet.setItems(resultat);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + resultat.size());
        return toRet;
    }

    public List categoriesList(Set<EquipmentClass> eqs) {
        List alarmDTOList = alertDao.categoriesList(eqs);
        if (alarmDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }
        return alarmDTOList;
    }

    public List<String> sources() {
        return alertDao.sources();
    }

    public List<String> categoriesIn(boolean inRealtime) {
        return alertDao.categoriesIn(inRealtime);
    }
}
