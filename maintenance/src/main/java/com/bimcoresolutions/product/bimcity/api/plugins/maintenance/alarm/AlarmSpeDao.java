package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm;

import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelRelation;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ASpeDao;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;

@Repository
public class AlarmSpeDao extends ASpeDao {

    private static final Logger LOGGER = LogManager.getLogger();
    public static final String CLASS_ALARM = "Alarm";
    public static final String ALARM_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_ALARM;
    private final BimCoreModel model;
    private final Set<EquipmentClass> equipmentClassesInThisProject = new HashSet<>();
    private final Map<String, BimCoreModelRelation> targetRelName = new HashMap<>();

    public AlarmSpeDao(@Qualifier("dsRealtime") DataSource dataSource, @Qualifier("qfRealtime") QueryFormer qf, ObjectMapper om, FunctionalModelService functionalModelService) {
        super(dataSource, qf, om);
        this.model = functionalModelService.getModel();
        for (BimCoreModelRelation bcr : model.getElementsBimCore().get(CLASS_ALARM).getRelations().values()) {
            String target = CLASS_ALARM.equals(bcr.getElementOrigin().getName()) ? bcr.getElementDestination().getName() : bcr.getElementOrigin().getName();
            if (EquipmentClass.getNames().contains(target)) {
                equipmentClassesInThisProject.add(EquipmentClass.valueOf(target));
                if (bcr.isDirect()) {
                    targetRelName.put(target, bcr);
                } else {
                    targetRelName.put(target, bcr.getReverseRelation());
                }
            }
        }
    }

    public Set<EquipmentClass> getEquipmentClassesInThisProject() {
        return equipmentClassesInThisProject;
    }

    public List<AlarmDTO> list(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses, String defaultType,
                               String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryList(limit, offset, orderBy, order, equipmentClasses, defaultType, equipmentName, source, start, end, userPerimeters);
        try {
            return execQuery(sql.toString(), AlarmDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public List typesList(Set<EquipmentClass> eqs) {
        StringBuilder sql = queryDefaultType(eqs);
        try {
            return execQueryToList(sql.toString(), ds);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public int count(Set<EquipmentClass> equipmentClasses, String defaultType, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryCount(equipmentClasses, defaultType, equipmentName, source, start, end, userPerimeters);
        try {
            return execCount(sql.toString());
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryDefaultType(Set<EquipmentClass> equipmentClasses) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");
        sql.append("distinct a.name");
        sql.append("\n FROM " + qf.addSchema(ALARM_TABLE) + " AS a");
        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));
        sql.append(whereClausesOnEquipmentType(equipmentClasses));
        return sql;
    }

    private StringBuilder whereClausesOnEquipmentType(Set<EquipmentClass> equipmentClasses) {
        StringBuilder sb = new StringBuilder();
        sb.append("WHERE 1=1");
        sb.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE) + " is not null")
                .collect(Collectors.joining("\n  OR ", "\n AND (", ")")));
        sb.append(";");
        return sb;
    }

    public StringBuilder queryList(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses,
                                   String defaultType, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT");
        sql.append(" a._id_bimcore,");
        sql.append(" a._perimeters,");
        sql.append(" a.lastupdate,");
        sql.append(" a.name,");
        sql.append(" a.source,");
        sql.append(equipmentSelectNameAndType(equipmentClasses));

        sql.append("\nFROM " + qf.addSchema(ALARM_TABLE) + " AS a");

        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));
        sql.append(whereClauses(equipmentClasses, defaultType, equipmentName, source, start, end, userPerimeters));
        sql.append("\nORDER BY " + toColumn(orderBy) + " " + order);
        sql.append("\nLIMIT " + limit + " OFFSET " + offset + ";");
        return sql;
    }

    private StringBuilder equipmentSelectNameAndType(Set<EquipmentClass> equipmentClasses) {
        StringBuilder query = new StringBuilder();
        query.append("\n COALESCE(");
        query.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ".name").collect(Collectors.joining(", ")));
        query.append(") as " + qf.addIdentifier("equipmentName") + ",");
        query.append("\n CASE");
        query.append(equipmentClasses.stream().map(e -> "  WHEN " + qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE) + " is not null THEN " + betweenSimpleQuote(e.name()))
                .collect(Collectors.joining("\n", "\n", "\n  ELSE null\n END\n as " + qf.addIdentifier("equipmentType"))));
        return query;
    }

    private static final String JOIN_TEMPLATE = """
            LEFT JOIN ${rel_table} ON a.${_id_bimcore} = ${rel_table}.${col_alarm}
            LEFT JOIN ${equipment_table} ON ${rel_table}.${col_equipment} = ${equipment_table}.${_id_bimcore}
            """;

    private String equipmentJoins(EquipmentClass equipmentClass) {
        Map<String, String> valuesMap = new HashMap<>();
        valuesMap.put("equipment_table", qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + equipmentClass.name()));
        valuesMap.put("rel_table", qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + targetRelName.get(equipmentClass.name()).getRelationTableName()));

        if (CLASS_ALARM.equals(targetRelName.get(equipmentClass.name()).getElementOrigin().getName())) {
            valuesMap.put("col_alarm", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
            valuesMap.put("col_equipment", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));
        } else {
            valuesMap.put("col_alarm", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));
            valuesMap.put("col_equipment", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
        }
        valuesMap.put("_id_bimcore", qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(JOIN_TEMPLATE);
    }

    public StringBuilder queryCount(Set<EquipmentClass> equipmentClasses, String defaultType, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append("\nFROM " + qf.addSchema(ALARM_TABLE) + " AS a");
        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));
        sql.append(whereClauses(equipmentClasses, defaultType, equipmentName, source, start, end, userPerimeters));
        sql.append(";");
        return sql;
    }

    public StringBuilder whereClauses(Set<EquipmentClass> equipmentClasses, String defaultType, String equipmentName, String source, Instant start, Instant end,
            Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append(" WHERE ").append(qf.addIdentifier("presence")).append(" = true");
        /* si décommenté, n'affichera plus les alarmes non reliées à un equipement
        sql.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE) + " is not null")
                .collect(Collectors.joining("\n  OR ", "\n AND (", ")")));
         */
        if (defaultType != null) {
            sql.append("\n AND a.name ILIKE '%" + defaultType + "%'");
        }
        if (equipmentName != null) {
            sql.append(equipmentClasses
                    .stream()
                    .map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ".name ILIKE '%" + equipmentName + "%'")
                    .collect(Collectors.joining("\n  OR ", "\n AND (", ")")));
        }
        if (source != null) {
            sql.append("\n AND a.source ILIKE '" + source + "'");
        }
        if (start != null) {
            sql.append("\n AND a.lastupdate > '" + start + "'");
        }
        if (end != null) {
            sql.append("\n AND a.lastupdate < '" + end + "'");
        }
        if(!userPerimeters.getLeft()){
            sql.append(" AND a._perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }

    private String toColumn(String orderBy) {
        if (orderBy.equals("equipmentName")) {
            return qf.addIdentifier(orderBy);
        } else {
            return "a." + qf.addIdentifier(orderBy);
        }
    }

    public List<String> sources() {
        String sql = sourcesQuery();
        try {
            return execQueryToList(sql, ds).stream().map(Object::toString).toList();
        } catch (TechnicalException e) {
            LOGGER.error("{}", e.getMessage());
            throw new TechnicalException(e);
        }
    }

    String sourcesQuery() {
        StringBuilder query = new StringBuilder("SELECT DISTINCT(").append(qf.addIdentifier("source"));
        query.append(") FROM ").append(qf.addSchema(ALARM_TABLE));
        query.append(" WHERE ").append(qf.addIdentifier("source")).append(" IS NOT NULL");
        query.append(" ORDER BY ").append(qf.addIdentifier("source")).append(";");

        return query.toString();
    }
}
