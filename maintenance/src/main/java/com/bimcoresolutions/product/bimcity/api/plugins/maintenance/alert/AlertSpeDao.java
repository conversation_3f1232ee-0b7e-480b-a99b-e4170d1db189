package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert;

import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelRelation;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ASpeDao;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.histo.StaticVariables.DB_HISTO_PREFIX_ELEMTABLE;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;

@Repository
public class AlertSpeDao extends ASpeDao {
    private static final Logger LOGGER = LogManager.getLogger();
    public static final String CLASS_ALERT = "Alert";
    public static final String ALERT_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_ALERT;

    private final Set<EquipmentClass> equipmentClassesInThisProject = new HashSet<>();
    private final Map<String, BimCoreModelRelation> targetRelName = new HashMap<>();

    protected final DataSource dsh;
    protected final QueryFormer qfh;

    public AlertSpeDao(@Qualifier("dsRealtime") DataSource dsr, @Qualifier("qfRealtime") QueryFormer qfr, @Qualifier("dsHisto") DataSource dsh,
                       @Qualifier("qfHisto") QueryFormer qfh, ObjectMapper om, FunctionalModelService functionalModelService) {
        super(dsr, qfr, om);
        this.dsh = dsh;
        this.qfh = qfh;
        for (BimCoreModelRelation bcr : functionalModelService.getModel().getElementsBimCore().get(CLASS_ALERT).getRelations().values()) {
            String target = CLASS_ALERT.equals(bcr.getElementOrigin().getName()) ? bcr.getElementDestination().getName() : bcr.getElementOrigin().getName();
            if (EquipmentClass.getNames().contains(target)) {
                equipmentClassesInThisProject.add(EquipmentClass.valueOf(target));
                if (bcr.isDirect()) {
                    targetRelName.put(target, bcr);
                } else {
                    targetRelName.put(target, bcr.getReverseRelation());
                }
            }
        }
    }

    public Set<EquipmentClass> getEquipmentClassesInThisProject() {
        return equipmentClassesInThisProject;
    }

    public int countRealTime(Set<EquipmentClass> equipmentClasses, Integer priority, EAlertStatus status, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        return count(equipmentClasses, priority, status, equipmentName, source, start, end, ds, qf,userPerimeters);
    }

    public int countHisto(Set<EquipmentClass> equipmentClasses, Integer priority, EAlertStatus status, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        return count(equipmentClasses, priority, status, equipmentName, source, start, end, dsh, qfh,userPerimeters);
    }

    private int count(Set<EquipmentClass> equipmentClasses, Integer priority, EAlertStatus status, String equipmentName, String source, Instant start, Instant end, DataSource ds,
                      QueryFormer qf,Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryCount(equipmentClasses, priority, status, equipmentName, source, start, end, qf,userPerimeters);
        try {
            return execCount(sql.toString(), ds);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryCount(Set<EquipmentClass> equipmentClasses, Integer priority, EAlertStatus status, String equipmentName,
                                    String source, Instant start, Instant end, QueryFormer qf, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT COUNT(*)");
        sql.append("\n FROM " + qf.addSchema(ALERT_TABLE) + " AS a");

        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));

        sql.append(" WHERE 1=1");
        sql.append(whereClauses(equipmentClasses, priority, status, equipmentName, source, start, end,userPerimeters));
        sql.append(";");

        return sql;
    }

    public List<AlertDTO> alertRealtimeList(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses,
                                            Integer priority, EAlertStatus status, String equipmentName, String source, Instant start, Instant end,
            Pair<Boolean, List<Integer>> userPerimeters) {
        return list(limit, offset, orderBy, order, equipmentClasses, priority, status, equipmentName, source, start, end, ds, qf,userPerimeters);
    }

    public List<AlertDTO> alertHistoList(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses,
                                         Integer priority, EAlertStatus status, String equipmentName, String source, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        return list(limit, offset, orderBy, order, equipmentClasses, priority, status, equipmentName, source, start, end, dsh, qfh,userPerimeters);
    }

    private List<AlertDTO> list(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses, Integer priority,
                                EAlertStatus status, String equipmentName, String source, Instant start, Instant end, DataSource ds, QueryFormer qf,Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryList(limit, offset, orderBy, order, equipmentClasses, priority, status, equipmentName, source, start, end, qf,userPerimeters);
        try {
            return execQuery(sql.toString(), ds, AlertDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public List categoriesList(Set<EquipmentClass> eqs) {
        StringBuilder sql = queryCategories(eqs);
        try {
            return execQueryToList(sql.toString(), ds);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryCategories(Set<EquipmentClass> equipmentClasses) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT ");
        sql.append("distinct a.category");
        sql.append("\n FROM " + qf.addSchema(ALERT_TABLE) + " AS a");
        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));
        sql.append(whereClausesOnEquipmentType(equipmentClasses));
        return sql;
    }

    private StringBuilder whereClausesOnEquipmentType(Set<EquipmentClass> equipmentClasses) {
        StringBuilder sb = new StringBuilder();
        sb.append("WHERE 1=1");
        sb.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE) + " is not null")
                .collect(Collectors.joining("\n  OR ", "\n AND (", ")")));
        sb.append(";");
        return sb;
    }

    public StringBuilder queryList(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses,
                                   Integer priority, EAlertStatus status, String equipmentName, String source, Instant start, Instant end, QueryFormer qf,
            Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();

        sql.append("SELECT");
        sql.append(" a._id_bimcore,");
        sql.append(" a._perimeters,");
        sql.append(" a.comment,");
        sql.append(" a.laststatuschangedate,");
        sql.append(" a.source,");
        sql.append(" a.priority,");
        sql.append(" a.status,");
        sql.append(" a.creationdate,");
        sql.append(" a.location,");
        sql.append(" a.name,");
        sql.append(" a.metier,");
        sql.append(equipmentSelects(equipmentClasses));

        sql.append("\nFROM " + qf.addSchema(ALERT_TABLE) + " AS a");

        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));
        sql.append(" WHERE 1=1");
        sql.append(whereClauses(equipmentClasses, priority, status, equipmentName, source, start, end,userPerimeters));
        sql.append("\nORDER BY " + toColumn(orderBy) + " " + order);
        sql.append("\nLIMIT " + limit + " OFFSET " + offset + ";");

        return sql;
    }

    private StringBuilder equipmentSelects(Set<EquipmentClass> equipmentClasses) {
        StringBuilder sql = new StringBuilder();
        sql.append("\n COALESCE(");
        sql.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE))
                .collect(Collectors.joining(", ")));
        sql.append(") as " + qf.addIdentifier("equipmentId") + ",");
        sql.append("\n COALESCE(");
        sql.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ".name").collect(Collectors.joining(", ")));
        sql.append(") as " + qf.addIdentifier("equipmentName") + ",");
        sql.append("\n CASE");
        sql.append(equipmentClasses.stream().map(e -> "  WHEN " + qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(
                        DB_REALTIME_ID_BIMCORE) + " is not null THEN " + betweenSimpleQuote(e.name()))
                .collect(Collectors.joining("\n", "\n", "\n  ELSE null\n END\n as " + qf.addIdentifier("equipmentType"))));
        return sql;
    }

    private static final String JOIN_TEMPLATE = """
            LEFT JOIN ${rel_table} ON a.${_id_bimcore} = ${rel_table}.${col_alert}
            LEFT JOIN ${equipment_table} ON ${rel_table}.${col_equipment} = ${equipment_table}.${_id_bimcore}
            """;

    private String equipmentJoins(EquipmentClass equipmentClass) {
        Map<String, String> valuesMap = new HashMap<>();
        valuesMap.put("equipment_table", qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + equipmentClass.name()));
        valuesMap.put("rel_table", qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + targetRelName.get(equipmentClass.name()).getRelationTableName()));

        if (CLASS_ALERT.equals(targetRelName.get(equipmentClass.name()).getElementOrigin().getName())) {
            valuesMap.put("col_alert", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
            valuesMap.put("col_equipment", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));
        } else {
            valuesMap.put("col_alert", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));
            valuesMap.put("col_equipment", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
        }
        valuesMap.put("_id_bimcore", qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(JOIN_TEMPLATE);
    }

    private StringBuilder whereClauses(Set<EquipmentClass> equipmentClasses, Integer priority, EAlertStatus status, String equipmentName, String source, Instant start, Instant end,Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append(equipmentClasses
                .stream()
                .map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE) + " is not null")
                .collect(Collectors.joining("\n  OR ", "\n AND (", ")")));
        if (priority != null) {
            sql.append("\n AND a.priority = '" + priority + "'");
        }
        if (status != null) {
            sql.append("\n AND a.status = '" + status.name() + "'");
        }
        if (equipmentName != null) {
            sql.append(equipmentClasses
                    .stream()
                    .map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ".name ILIKE '%" + equipmentName + "%'")
                    .collect(Collectors.joining("\n  OR ", "\n AND (", ")")));
        }
        if (source != null) {
            sql.append("\n AND a.source ILIKE '" + source + "'");
        }
        if(!userPerimeters.getLeft()){
            sql.append(" AND a._perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        sql.append(whereClausesDate(status, start, end));
        return sql;
    }

    private StringBuilder whereClausesDate(EAlertStatus status, Instant start, Instant end) {
        StringBuilder sql = new StringBuilder();
        if (status == EAlertStatus.New) {
            if (start != null) {
                sql.append("\n AND a.creationdate > '" + start + "'");
            }
            if (end != null) {
                sql.append("\n AND a.creationdate < '" + end + "'");
            }
        } else {
            if (start != null) {
                sql.append("\n AND a.laststatuschangedate > '" + start + "'");
            }
            if (end != null) {
                sql.append("\n AND a.laststatuschangedate < '" + end + "'");
            }
        }
        return sql;
    }

    private String toColumn(String orderBy) {
        if (orderBy.equals("equipmentName")) {
            return qf.addIdentifier(orderBy);
        } else {
            return "a." + qf.addIdentifier(orderBy);
        }
    }

    public List<String> sources() {
        String sql = sourcesQuery();
        try {
            return execQueryToList(sql, ds).stream().map(Object::toString).toList();
        } catch (TechnicalException e) {
            LOGGER.error("{}", e.getMessage());
            throw new TechnicalException(e);
        }
    }

    String sourcesQuery() {
        StringBuilder query = new StringBuilder("SELECT DISTINCT(").append(qf.addIdentifier("source"));
        query.append(") FROM ").append(qf.addSchema(ALERT_TABLE));
        query.append(" WHERE ").append(qf.addIdentifier("source")).append(" IS NOT NULL");
        query.append(" ORDER BY ").append(qf.addIdentifier("source")).append(";");

        return query.toString();
    }

    public List<String> categoriesIn(boolean inRealtime) {
        String query = categoriesInQuery(inRealtime);
        try {
            if (inRealtime) {
                return execQueryToList(query, ds).stream().map(Object::toString).toList();
            } else {
                return execQueryToList(query, dsh).stream().map(Object::toString).toList();
            }
        } catch (TechnicalException e) {
            LOGGER.error("{}", e.getMessage());
            throw new TechnicalException(e);
        }
    }

    String categoriesInQuery(boolean inRealtime) {
        QueryFormer qf_;
        String prefix;
        if (inRealtime) {
            qf_ = qf;
            prefix = DB_REALTIME_PREFIX_ELEMTABLE;
        } else {
            qf_ = qfh;
            prefix = DB_HISTO_PREFIX_ELEMTABLE;
        }

        StringBuilder query = new StringBuilder("SELECT DISTINCT(").append(qf_.addIdentifier("category"));
        query.append(") FROM ").append(qf_.addSchema(prefix + CLASS_ALERT));
        query.append(" WHERE ").append(qf_.addIdentifier("category")).append(" IS NOT NULL");
        query.append(" ORDER BY ").append(qf_.addIdentifier("category")).append(";");

        return query.toString();
    }
}
