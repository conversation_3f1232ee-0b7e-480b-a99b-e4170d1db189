package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@Api(tags = { "InterventionRequestSpeRest" })
@RequestMapping("/spe/maintenance/interventionrequest")
public class InterventionRequestSpeRest extends JwtDecoder {

    private final InterventionRequestSpeService interventionRequestSpeService;
    private final InterventionRequestHistoSpeService interventionRequestHistoSpeService;
    private final IRSpeService irSpeService;

    public InterventionRequestSpeRest(InterventionRequestSpeService interventionRequestSpeService, InterventionRequestHistoSpeService interventionRequestHistoSpeService,
            IRSpeService irSpeService,CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.interventionRequestSpeService = interventionRequestSpeService;
        this.interventionRequestHistoSpeService = interventionRequestHistoSpeService;
        this.irSpeService = irSpeService;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @ApiOperation(value = "InterventionRequestlist", nickname = "InterventionRequestlist", httpMethod = "GET", notes = "obtenir la liste des InterventionRequestes")
    @Secured({ "ROLE_admin", "ROLE_InterventionRequest_show" })
    public EnveloppeGetter<InterventionRequestDTO> interventionRequestList(InterventionRequestFilter filter) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();
        filter.setUserPerimeters(userPerimeters);

        checkLimitOffset(filter.getLimit(), filter.getOffset());
        return interventionRequestSpeService.list(filter);
    }

    @GetMapping(value = "/listHisto", produces = "application/json")
    @ApiOperation(value = "InterventionRequestlist", nickname = "InterventionRequestlist", httpMethod = "GET", notes = "obtenir la liste des InterventionRequestes historique")
    @Secured({ "ROLE_admin", "ROLE_InterventionRequest_show" })
    public EnveloppeGetter<InterventionRequestHistoDTO> interventionRequestListHisto(InterventionRequestFilter filter) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();
        filter.setUserPerimeters(userPerimeters);

        checkLimitOffset(filter.getLimit(), filter.getOffset());

        return interventionRequestHistoSpeService.listHisto(filter);
    }

    private void checkLimitOffset(Long limit, Long offset) {
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
    }

    @GetMapping(value = "/categories", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_Alarm_read" })
    public List categorieslist(@RequestParam List<String> equipmentClass) {
        if (CollectionUtils.isEmpty(equipmentClass)) {
            throw new WrongArgumentValueException("equipmentClass shouldn't be null or empty");
        }
        try {
            Set<EquipmentClass> eqs = EquipmentClass.getEquipmentClassesFromString(equipmentClass);
            return irSpeService.categoriesList(eqs);
        } catch (IllegalArgumentException ex) {
            throw new WrongArgumentValueException("Wrong 'equipmentClass' parameter given : '" + equipmentClass + "'. " +
                    "(Known : " + EquipmentClass.getEquipmentClassesFromString(equipmentClass) + ")");
        }
    }

}

