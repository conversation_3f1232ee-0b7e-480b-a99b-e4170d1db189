package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@Api(tags = { "AlarmSpeRest" })
@RequestMapping("/spe/maintenance/alarm")
public class AlarmSpeRest extends JwtDecoder {

    private final AlarmSpeService alarmSpeService;

    private final AlarmSpeDao alarmSpeDao;

    public AlarmSpeRest(AlarmSpeService alarmSpeService, AlarmSpeDao alarmSpeDao, CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.alarmSpeService = alarmSpeService;
        this.alarmSpeDao = alarmSpeDao;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @ApiOperation(value = "alarmlist", nickname = "alarmlist", httpMethod = "GET", notes = "obtenir la liste des alarmes")
    @Secured({ "ROLE_admin", "ROLE_Alarm_show" })
    public EnveloppeGetter<AlarmDTO> alarmlist(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "lastupdate") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("equipmentType") @RequestParam(required = false) Set<String> equipmentType,
            @ModelAttribute("defaulttype") @RequestParam(required = false) String defaulttype,
            @ModelAttribute("equipmentName") @RequestParam(required = false) String equipmentName,
            @ModelAttribute("source") @RequestParam(required = false) String source,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();

        Set<EquipmentClass> eqs;
        if (CollectionUtils.isEmpty(equipmentType)) {
            eqs = alarmSpeDao.getEquipmentClassesInThisProject();
        } else {
            try {
                eqs = equipmentType.stream().map(EquipmentClass::valueOf).collect(Collectors.toCollection(HashSet::new));
            } catch (IllegalArgumentException ex) {
                throw new WrongArgumentValueException("Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + alarmSpeDao.getEquipmentClassesInThisProject() + ")");
            }
            if (!alarmSpeDao.getEquipmentClassesInThisProject().containsAll(eqs)) {
                throw new WrongArgumentValueException(
                        "Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + alarmSpeDao.getEquipmentClassesInThisProject() + ")");
            }
        }
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("lastupdate", "name", "source", "equipmentName").contains(orderby)) {
            throw new WrongArgumentValueException("Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : lastupdate,name,source,equipmentName)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        return alarmSpeService.alarmList(limit, offset, orderby, order, eqs, defaulttype, equipmentName, source, start, end, userPerimeters);
    }

    @GetMapping(value = "/types/{types}", produces = "application/json")
    @ApiOperation(value = "typeslist", nickname = "typeslist", httpMethod = "GET", notes = "obtenir la liste des types de défauts d'alarme")
    @Secured({ "ROLE_admin", "ROLE_Alarm_read" })
    public List typeslist(@PathVariable List<String> types) {

        if (CollectionUtils.isEmpty(types)) {
            throw new WrongArgumentValueException("types shouldn't be null or empty");
        }
        try {
            Set<EquipmentClass> eqs = EquipmentClass.getEquipmentClassesFromString(types);
            return alarmSpeService.typesList(eqs);
        } catch (IllegalArgumentException ex) {
            throw new WrongArgumentValueException("Wrong 'types' parameter given : '" + types + "'. " +
                    "(Known : " + EquipmentClass.getEquipmentClassesFromString(types) + ")");
        }
    }

    @GetMapping(value = "/sources", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Alarm_read"})
    public List<String> getSources() {
        return alarmSpeService.sources();
    }

}
