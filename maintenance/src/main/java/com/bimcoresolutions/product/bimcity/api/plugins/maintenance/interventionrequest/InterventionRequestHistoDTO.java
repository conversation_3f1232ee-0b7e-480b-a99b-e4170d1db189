package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
@Builder
public class InterventionRequestHistoDTO {
    private String _id_bimcore;
    private List<Integer> _perimeters;
    private String category;
    private String code;
    private String comment;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant creationdate;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant lastupdate;
    private String sourcesystem;
    private String status;
    private List<String> equipmentName;
    private List<String> equipmentType;
    private Instant timestamp;
    private Instant deadline;

}
