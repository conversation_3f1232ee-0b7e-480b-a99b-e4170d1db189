package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelRelation;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.AbstractSpeDao;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PeriodFilter;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.sql.ResultSet;
import java.util.*;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatValueToSQL;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Repository
public class InterventionRequestHistoSpeDao extends AbstractSpeDao<InterventionRequestHistoDTO, InterventionRequestFilter> {

    private static final String INTERVENTION_REQUEST = "InterventionRequest";
    private final Set<EquipmentClass> equipmentClassesInThisProject = new TreeSet<>();
    private final Map<String, BimCoreModelRelation> targetRelName = new TreeMap<>();

    private final QueryFormer qf;

    public InterventionRequestHistoSpeDao(DataSource dataSource, FunctionalModelService functionalModelService, @Qualifier("qfHisto") QueryFormer qf) {
        super(dataSource);
        this.qf = qf;
        for (BimCoreModelRelation bcr : functionalModelService.getModel().getElementsBimCore().get(INTERVENTION_REQUEST).getRelations().values()) {
            String target = INTERVENTION_REQUEST.equals(bcr.getElementOrigin().getName()) ? bcr.getElementDestination().getName() : bcr.getElementOrigin().getName();
            if (EquipmentClass.getNames().contains(target)) {
                equipmentClassesInThisProject.add(EquipmentClass.valueOf(target));
                if (bcr.isDirect()) {
                    targetRelName.put(target, bcr);
                } else {
                    targetRelName.put(target, bcr.getReverseRelation());
                }
            }
        }
    }

    public Set<EquipmentClass> getEquipmentClassesInThisProject() {
        return equipmentClassesInThisProject;
    }

    @Override
    protected String getTemplateCount() {
        return """
                with orderedrelunique AS (${select_rel_left_join_elem}
                )
                , orderedrels AS (
                 SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _creation_date AS timestamp
                 FROM orderedrelunique
                 UNION SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _end_date AS timestamp
                 FROM orderedrelunique
                  WHERE _end_date IS NOT NULL
                )
                , ordered AS (
                 SELECT _id_bimcore AS id_bimcore_ir, null AS id_bimcore_equipment, _creation_date, _end_date, null AS "equipmentName", null AS "equipmentType", _creation_date AS timestamp
                 FROM histo."elem_InterventionRequest"
                 WHERE 1=1
                 UNION SELECT * FROM orderedrels
                )
                SELECT count(distinct(ir._id_bimcore, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline, ordered.timestamp, ordered.id_bimcore_ir))
                FROM ordered
                LEFT JOIN orderedrels
                 ON ordered.id_bimcore_ir = orderedrels.id_bimcore_ir
                  AND tsrange(orderedrels._creation_date, orderedrels._end_date) @> ordered.timestamp
                LEFT JOIN histo."elem_InterventionRequest" ir
                 ON ordered.id_bimcore_ir = ir._id_bimcore
                  AND tsrange(ir._creation_date, ir._end_date) @> ordered.timestamp
                WHERE 1=1${filter}
                ;""";
    }

    @Override
    public String queryBddCount(InterventionRequestFilter filter, String schema) {
        Map<String, Object> valuesMap = new HashMap<>();
        valuesMap.put("select_rel_left_join_elem", buildRelLeftJoinElem());
        valuesMap.put("filter", buildFilter(filter));

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        return sub.replace(getTemplateCount());
    }

    @Override
    protected String getTemplateList() {
        return """
                with orderedrelunique AS (${select_rel_left_join_elem}
                )
                , orderedrels AS (
                 SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _creation_date AS timestamp
                 FROM orderedrelunique
                 UNION SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _end_date AS timestamp
                 FROM orderedrelunique
                  WHERE _end_date IS NOT NULL
                )
                , ordered AS (
                 SELECT _id_bimcore AS id_bimcore_ir, null AS id_bimcore_equipment, _creation_date, _end_date, null AS "equipmentName", null AS "equipmentType", _creation_date AS timestamp
                 FROM histo."elem_InterventionRequest"
                 WHERE 1=1
                 UNION SELECT * FROM orderedrels
                )
                SELECT ordered.timestamp, ir._id_bimcore, ir._perimeters, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline
                , array_remove(array_agg(distinct orderedrels."equipmentType"), null) AS "equipmentType"
                , array_remove(array_agg(distinct orderedrels."equipmentName"), null) AS "equipmentName"
                FROM ordered
                LEFT JOIN orderedrels
                 ON ordered.id_bimcore_ir = orderedrels.id_bimcore_ir
                  AND tsrange(orderedrels._creation_date, orderedrels._end_date) @> ordered.timestamp
                LEFT JOIN histo."elem_InterventionRequest" ir
                 ON ordered.id_bimcore_ir = ir._id_bimcore
                  AND tsrange(ir._creation_date, ir._end_date) @> ordered.timestamp
                WHERE 1=1${filter}
                GROUP BY ir._id_bimcore, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline, ordered.timestamp, ordered.id_bimcore_ir
                ${paging}
                ;""";
    }

    @Override
    public String queryBddList(InterventionRequestFilter filter, String schema) {
        Map<String, Object> valuesMap = new HashMap<>();
        valuesMap.put("select_rel_left_join_elem", buildRelLeftJoinElem());
        valuesMap.put("filter", buildFilter(filter));
        valuesMap.put("paging", buildPaging(filter));

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        return sub.replace(getTemplateList());
    }

    public String buildRelLeftJoinElem() {
        StringBuilder query = new StringBuilder();
        char c = 'a';
        for (EquipmentClass e : equipmentClassesInThisProject) {
            String colDi;
            String colEquipment;
            if (INTERVENTION_REQUEST.equals(targetRelName.get(e.name()).getElementOrigin().getName())) {
                colDi = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                colEquipment = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
            } else {
                colDi = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                colEquipment = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
            }
            query.append("\n SELECT " + qf.addIdentifier(colDi) + " AS id_bimcore_ir, ");
            query.append(qf.addIdentifier(colEquipment) + " AS id_bimcore_equipment, ");
            query.append("_creation_date, _end_date, " + c + ".name AS \"equipmentName\", '" + e.name() + "' AS \"equipmentType\"");
            query.append("\n FROM " + qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + targetRelName.get(e.name()).getRelationTableName()) + " " + c + c);
            query.append("\n LEFT JOIN (SELECT distinct(_id_bimcore), name FROM " + qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ") " + c);
            query.append("\n  ON " + c + c + "." + qf.addIdentifier(colEquipment) + " = " + c + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
            query.append("\n UNION");
            c++;
        }
        query.delete(query.length() - 7, query.length());
        return query.toString();
    }

    @Override
    protected String buildFilter(InterventionRequestFilter interventionRequestFilter) {
        String query = "";
        if (isNotBlank(interventionRequestFilter.getCode())) {
            query += "\n AND ir.code ILIKE " + betweenSimpleQuote("%" + formatValueToSQL(interventionRequestFilter.getCode()) + "%");
        }
        if (isNotBlank(interventionRequestFilter.getCategory())) {
            query += "\n AND ir.category ILIKE " + betweenSimpleQuote("%" + formatValueToSQL(interventionRequestFilter.getCategory()) + "%");
        }
        if (isNotBlank(interventionRequestFilter.getStatus())) {
            query += "\n AND ir.status = " + betweenSimpleQuote(formatValueToSQL(interventionRequestFilter.getStatus()));
        }
        if (interventionRequestFilter.getPeriod() != null) {
            query += between(interventionRequestFilter.getPeriod());
        }
        if (isNotBlank(interventionRequestFilter.getEquipmentName())) {
            query += "\n AND orderedrels.\"equipmentName\" ilike " + betweenSimpleQuote("%" + formatValueToSQL(interventionRequestFilter.getEquipmentName()) + "%");
        }
        if (interventionRequestFilter.getUserPerimeters() != null && !interventionRequestFilter.getUserPerimeters().getLeft()) {
            List<Integer> perimeters = new ArrayList<>(interventionRequestFilter.getUserPerimeters().getRight());
            query += "\n AND ir._perimeters && ARRAY[0," +
                     perimeters.stream().map(Object::toString).collect(Collectors.joining(",")) +
                     "]::integer[]";
        }
        return query;
    }

    @Override
    protected String orderBy(InterventionRequestFilter paginatedFilter) {
        if (paginatedFilter.getOrderby() == InterventionRequestFilter.InterventionFilterOrderby.equipmentName) {
            return qf.addIdentifier("equipmentName") + " " + paginatedFilter.getOrder();
        } else {
            return "ir." + paginatedFilter.getOrderby() + " " + paginatedFilter.getOrder();
        }
    }

    private String between(PeriodFilter period) {
        String query = "";
        if (period.getStart() != null) {
            query += "\n AND ir.lastupdate >= '" + period.getStart() + "'";
        }
        if (period.getEnd() != null) {
            query += "\n AND ir.lastupdate <= '" + period.getEnd() + "'";
        }
        return query;
    }

    @Override
    protected InterventionRequestHistoDTO resultsetToObject(ResultSet rs) {
        InterventionRequestHistoDTO.InterventionRequestHistoDTOBuilder tmp = InterventionRequestHistoDTO.builder();
        tmp._id_bimcore(nullIfNull(rs, "_id_bimcore", String.class));
        tmp._perimeters(getIntegerList(rs, "_perimeters"));
        tmp.category(nullIfNull(rs, "category", String.class));
        tmp.code(nullIfNull(rs, "code", String.class));
        tmp.comment(nullIfNull(rs, "comment", String.class));
        tmp.creationdate(nullIfNullDate(rs, "creationdate"));
        tmp.lastupdate(nullIfNullDate(rs, "lastupdate"));
        tmp.sourcesystem(nullIfNull(rs, "sourcesystem", String.class));
        tmp.status(nullIfNull(rs, "status", String.class));
        tmp.equipmentName(emptyIfNull(rs, "equipmentName"));
        tmp.equipmentType(emptyIfNull(rs, "equipmentType"));
        tmp.timestamp(nullIfNullDate(rs, "timestamp"));
        tmp.deadline(nullIfNullDate(rs, "deadline"));
        InterventionRequestHistoDTO interventionRequest = tmp.build();
        return interventionRequest;
    }
}