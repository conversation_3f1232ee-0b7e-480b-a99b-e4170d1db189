package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.EAlertStatus.New;

@RestController
@Api(tags = {"AlertSpeRest"})
@RequestMapping("/spe/maintenance/alert")
public class AlertSpeRest extends JwtDecoder {

    private final AlertSpeService alertSpeService;
    private final AlertSpeDao alertSpeDao;

    public AlertSpeRest(AlertSpeService alertSpeService, AlertSpeDao alertSpeDao, CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.alertSpeService = alertSpeService;
        this.alertSpeDao = alertSpeDao;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @ApiOperation(value = "alertlist", nickname = "alertlist", httpMethod = "GET", notes = "obtenir la liste des alertes")
    @Secured({ "ROLE_admin", "ROLE_Alert_show" })
    public EnveloppeGetter<AlertDTO> alertlist(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false) String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("equipmentType") @RequestParam(required = false) Set<String> equipmentType,
            @ModelAttribute("priority") @RequestParam(required = false) Integer priority,
            @ModelAttribute("status") @RequestParam EAlertStatus status,
            @ModelAttribute("equipmentName") @RequestParam(required = false) String equipmentName,
            @ModelAttribute("source") @RequestParam(required = false) String source,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {

        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();

        Set<EquipmentClass> eqs = checkequipmentType(equipmentType);
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }

        if (orderby == null) {
            orderby = status == New ? "creationdate" : "laststatuschangedate";
        } else {
            if (!List.of("source", "priority", "status", "equipmentName", "creationdate", "laststatuschangedate").contains(orderby)) {
                throw new WrongArgumentValueException(
                        "Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : source,priority,status,equipmentName,creationdate,laststatuschangedate)");
            }
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }

        return alertSpeService.alertRealTimeList(limit, offset, orderby, order, eqs, priority, status, equipmentName, source, start, end, userPerimeters);
    }

    @GetMapping(value = "/listHisto", produces = "application/json")
    @ApiOperation(value = "alertlist", nickname = "alertlist", httpMethod = "GET", notes = "obtenir la liste des alertes historique")
    @Secured({ "ROLE_admin", "ROLE_Alert_show" })
    public EnveloppeGetter<AlertDTO> alertlistHisto(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "laststatuschangedate") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("equipmentType") @RequestParam(required = false) Set<String> equipmentType,
            @ModelAttribute("priority") @RequestParam(required = false) Integer priority,
            @ModelAttribute("status") @RequestParam(required = false) EAlertStatus status,
            @ModelAttribute("equipmentName") @RequestParam(required = false) String equipmentName,
            @ModelAttribute("source") @RequestParam(required = false) String source,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {

        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();

        Set<EquipmentClass> eqs = checkequipmentType(equipmentType);
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("laststatuschangedate", "source", "priority", "status", "equipmentName", "metier", "comment").contains(orderby)) {
            throw new WrongArgumentValueException(
                    "Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : laststatuschangedate,source,priority,status,equipmentName,metier,comment)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }

        return alertSpeService.alertHistoList(limit, offset, orderby, order, eqs, priority, status, equipmentName, source, start, end, userPerimeters);
    }

    private Set<EquipmentClass> checkequipmentType(Set<String> equipmentType) {
        Set<EquipmentClass> eqs;
        if (CollectionUtils.isEmpty(equipmentType)) {
            eqs = alertSpeDao.getEquipmentClassesInThisProject();
        } else {
            try {
                eqs = equipmentType.stream().map(EquipmentClass::valueOf).collect(Collectors.toCollection(HashSet::new));
            } catch (IllegalArgumentException ex) {
                throw new WrongArgumentValueException("Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + alertSpeDao.getEquipmentClassesInThisProject() + ")");
            }
            if (!alertSpeDao.getEquipmentClassesInThisProject().containsAll(eqs)) {
                throw new WrongArgumentValueException(
                        "Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + alertSpeDao.getEquipmentClassesInThisProject() + ")");
            }
        }
        return eqs;
    }

    @GetMapping(value = "/categories", produces = "application/json")
    @Secured({ "ROLE_admin", "ROLE_Alarm_read" })
    public List categorieslist(@RequestParam List<String> equipmentClass) {
        if (CollectionUtils.isEmpty(equipmentClass)) {
            throw new WrongArgumentValueException("equipmentClass shouldn't be null or empty");
        }
        try {
            Set<EquipmentClass> eqs = EquipmentClass.getEquipmentClassesFromString(equipmentClass);
            return alertSpeService.categoriesList(eqs);
        } catch (IllegalArgumentException ex) {
            throw new WrongArgumentValueException("Wrong 'equipmentClass' parameter given : '" + equipmentClass + "'. " +
                    "(Known : " + EquipmentClass.getEquipmentClassesFromString(equipmentClass) + ")");
        }
    }

    @GetMapping(value = "/sources", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Alert_read"})
    public List<String> getSources() {
        return alertSpeService.sources();
    }

    @GetMapping(value = "/categories/realtime", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Alert_read"})
    public List<String> categoriesInRealtime() {
        return alertSpeService.categoriesIn(true);
    }

    @GetMapping(value = "/categories/history", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Alert_read"})
    public List<String> categoriesInHistory() {
        return alertSpeService.categoriesIn(false);
    }
}
