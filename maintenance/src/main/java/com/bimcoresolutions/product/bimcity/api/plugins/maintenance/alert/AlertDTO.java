package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import com.bimcoresolutions.util.base.serialisation.GeometrySerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import org.locationtech.jts.geom.Geometry;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AlertDTO extends ModelSpe {

    private String _id_bimcore;
    private List<Integer> _perimeters;
    private String comment;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant laststatuschangedate;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant creationdate;
    @JsonSerialize(using = GeometrySerializer.class)
    private Geometry location;
    private String source;
    private Long priority;
    private String status;
    private String name;
    private String equipmentId;
    private String equipmentName;
    private String equipmentType;
    private JsonNode metier;
}
