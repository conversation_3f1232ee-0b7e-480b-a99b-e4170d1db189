package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AlarmDTO extends ModelSpe {

    private String _id_bimcore;
    private List<Integer> _perimeters;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant lastupdate;
    private String name;
    private String source;
    private String equipmentName;
    private String equipmentType;

}
