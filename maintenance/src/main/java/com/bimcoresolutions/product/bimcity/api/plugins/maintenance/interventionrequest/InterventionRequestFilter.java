package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PaginatedFilter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PeriodFilter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.isBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class InterventionRequestFilter extends PaginatedFilter<InterventionRequestFilter.InterventionFilterOrderby> {
    String code;
    String category;
    String status;
    String comment;
    String equipmentName;
    Instant creationdate;
    PeriodFilter period;
    Pair<Boolean, List<Integer>> userPerimeters;

    @Builder
    public InterventionRequestFilter(Long limit, Long offset, InterventionFilterOrderby orderby, Order order, String code, String category, String status, String comment, String equipmentName, Instant creationdate,
                                     PeriodFilter period, Pair<Boolean, List<Integer>> userPerimeters) {
        super(limit, offset, orderby != null ? orderby : InterventionFilterOrderby.lastupdate, order);
        this.code = code;
        this.category = category;
        this.status = status;
        this.comment = comment;
        this.equipmentName = equipmentName;
        this.creationdate = creationdate;
        this.period = period;
        this.userPerimeters = userPerimeters;
    }

    public enum InterventionFilterOrderby {
        code,
        category,
        equipmentName,
        status,
        comment,
        creationdate,
        lastupdate;

        public static Set<String> nameValues() {
            return Arrays.stream(values()).map(Enum::name).collect(Collectors.toSet());
        }
    }

    public static class InterventionFilterOrderbyEditor extends PropertyEditorSupport {
        @Override
        public void setAsText(String text) {
            if (isBlank(text)) {
                setValue(InterventionFilterOrderby.lastupdate);
            } else {
                try {
                    setValue(InterventionFilterOrderby.valueOf(text));
                } catch (Exception ex) {
                    throw new WrongArgumentValueException(
                            "Wrong value for field 'orderby' given : '" + text + "'. (Possible values : " + InterventionFilterOrderby.nameValues() + ")");
                }
            }
        }
    }

    @InitBinder
    public void filterOrderbyBinder(WebDataBinder dataBinder) {
        dataBinder.registerCustomEditor(InterventionFilterOrderby.class, new InterventionFilterOrderbyEditor());
    }

}

