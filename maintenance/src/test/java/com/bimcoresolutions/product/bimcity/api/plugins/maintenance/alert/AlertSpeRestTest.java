package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.api.base.spring.ErrorHandler;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.sql.DataSource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;

import static com.bimcoresolutions.util.model.service.histo.HistoManager.SCHEMA_HISTO;
import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.Camera;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.DynPanel;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(MockitoExtension.class)
public class AlertSpeRestTest {

    public static final QueryFormer HISTO = new QueryFormer(POSTGRESQL, SCHEMA_HISTO);
    public static final QueryFormer REALTIME = new QueryFormer(POSTGRESQL, SCHEMA_RT);
    public static final ObjectMapper om = new ObjectMapper().findAndRegisterModules();
    private static final Semver VERSION_MODEL = new Semver("0.0.0");
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);
    private static MockMvc mockMvc;
    private static AlertSpeRest alertSpeRest;
    private static AlertSpeService alertSpeService;
    private static AlertSpeDao alertSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/Alert-DynPanel-Camera_0_0_0.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("Alert-DynPanel-Camera", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        alertSpeDao = new AlertSpeDao(Mockito.mock(DataSource.class), REALTIME, Mockito.mock(DataSource.class), HISTO, om, FUNCTIONAL_MODEL_SERVICE);
        alertSpeService = Mockito.mock(AlertSpeService.class);
        CustomJwtAuthenticationConverter customJwt = Mockito.mock(CustomJwtAuthenticationConverter.class);
        alertSpeRest = new AlertSpeRest(alertSpeService, alertSpeDao,customJwt);
        ThreadLocal<Pair<Boolean, List<Integer>>> mockThreadLocal = new ThreadLocal<>();
        mockThreadLocal.set(Pair.of(true, List.of(1,2,3)));
        when(customJwt.get_perimeters()).thenReturn(mockThreadLocal);
        mockMvc = MockMvcBuilders.standaloneSetup(alertSpeRest).setControllerAdvice(new ErrorHandler()).build();
    }

    @Nested
    class RealTime {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        private final String ENDPOINT_REALTIME = "list";
        @Test
        void get_alertList_success() throws Exception {
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(alertSpeService.alertRealTimeList(10L, 0L, "creationdate", "desc", Set.of(DynPanel), null, EAlertStatus.New, null, null, null, null,userPerimeters))
                    .willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(
                            get("/spe/maintenance/alert/" + ENDPOINT_REALTIME + "?equipmentType=DynPanel&status=New")
                                    .contentType(APPLICATION_JSON))
                    .andReturn().getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(alertSpeService).alertRealTimeList(10L, 0L, "creationdate", "desc", Set.of(DynPanel), null, EAlertStatus.New, null, null, null, null,userPerimeters);
        }

        @Test
        void alertList_wrong_equipmentType() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=RandomEquipment&status=Closed")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'equipmentType' parameter given : '[RandomEquipment]'.");
        }

        @Test
        void alertList_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=Closed&limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void alertList_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=Closed&offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void alertList_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=Closed&order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void alertList_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=Closed&orderby=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining(
                            "Wrong 'orderby' parameter given : 'randomFalseValue'. (Possible values : source,priority,status,equipmentName,creationdate,laststatuschangedate)");
        }

        @Test
        void alertList_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=Closed&order=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
        }

        @Test
        void alertList_null_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=null")).isInstanceOf(MethodArgumentTypeMismatchException.class)
                                                                                                        .hasMessageContaining(
                                                                                                                "Failed to convert value of type 'java.lang.String' to required type 'com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.EAlertStatus'");
        }

        @Test
        void alertList_wrong_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=randomFalseValue")).isInstanceOf(MethodArgumentTypeMismatchException.class)
                                                                                                                    .hasMessageContaining(
                                                                                                                            "Failed to convert value of type 'java.lang.String' to required type 'com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.EAlertStatus'");
        }

        @Test
        void alertList_status_new_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=New&orderby=RandomValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining(
                            "Wrong 'orderby' parameter given : 'RandomValue'. (Possible values : source,priority,status,equipmentName,creationdate,laststatuschangedate)");
        }

        @Test
        void alertList_status_inprogress_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "equipmentType=DynPanel&status=New&orderby=RandomValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining(
                            "Wrong 'orderby' parameter given : 'RandomValue'. (Possible values : source,priority,status,equipmentName,creationdate,laststatuschangedate)");
        }
    }

    @Nested
    class Histo {

        private final String ENDPOINT_HISTO = "listHisto";
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

        @Test
        void get_alertListHisto_success() throws Exception {
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(alertSpeService.alertHistoList(10L, 0L, "laststatuschangedate", "desc", Set.of(Camera), null, null, null, null, null, null,userPerimeters))
                    .willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(
                            get("/spe/maintenance/alert/" + ENDPOINT_HISTO + "?equipmentType=Camera")
                                    .contentType(APPLICATION_JSON))
                    .andReturn().getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(alertSpeService).alertHistoList(10L, 0L, "laststatuschangedate", "desc", Set.of(Camera), null, null, null, null, null, null,userPerimeters);
        }

        @Test
        void alertListHisto_wrong_equipmentType() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=RandomEquipment")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'equipmentType' parameter given : '[RandomEquipment]'.");
        }

        @Test
        void alertListHisto_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=Camera&limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void alertListHisto_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=Camera&offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void alertListHisto_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=Camera&order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void alertListHisto_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=Camera&orderby=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining(
                            "Wrong 'orderby' parameter given : 'randomFalseValue'. (Possible values : laststatuschangedate,source,priority,status,equipmentName,metier,comment)");
        }

        @Test
        void alertListHisto_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=Camera&order=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
        }

        @Test
        void alertListHisto_null_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=Camera&status=null")).isInstanceOf(MethodArgumentTypeMismatchException.class)
                                                                                                   .hasMessageContaining(
                                                                                                           "Failed to convert value of type 'java.lang.String' to required type 'com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.EAlertStatus'");
        }

        @Test
        void alertListHisto_wrong_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "equipmentType=Camera&status=randomFalseValue")).isInstanceOf(MethodArgumentTypeMismatchException.class)
                                                                                                               .hasMessageContaining(
                                                                                                                       "Failed to convert value of type 'java.lang.String' to required type 'com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.EAlertStatus'");
        }
    }

    Exception get_with_paramException(String endpoint, String param) throws Exception {
        return mockMvc.perform(get("/spe/maintenance/alert/" + endpoint + "?" + param).contentType(MediaType.APPLICATION_JSON))
                .andReturn()
                .getResolvedException();
    }

}
