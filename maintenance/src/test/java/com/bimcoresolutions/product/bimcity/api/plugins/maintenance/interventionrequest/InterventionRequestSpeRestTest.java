package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.api.base.spring.ErrorHandler;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(MockitoExtension.class)
class InterventionRequestSpeRestTest {
    private static final Semver VERSION_MODEL = new Semver("0.0.0");
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);
    private static MockMvc mockMvc;
    private static InterventionRequestSpeRest interventionRequestSpeRest;
    private static InterventionRequestSpeService interventionRequestSpeService;
    private static InterventionRequestHistoSpeService interventionRequestHistoSpeService;
    private static IRSpeService irSpeService;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/InterventionRequest-DynPanel-Camera_0_0_0.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("InterventionRequest-DynPanel-Camera", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        interventionRequestSpeService = Mockito.mock(InterventionRequestSpeService.class);
        interventionRequestHistoSpeService = Mockito.mock(InterventionRequestHistoSpeService.class);
        irSpeService = Mockito.mock(IRSpeService.class);
        CustomJwtAuthenticationConverter customJwt = Mockito.mock(CustomJwtAuthenticationConverter.class);
        ThreadLocal<Pair<Boolean, List<Integer>>> mockThreadLocal = new ThreadLocal<>();
        mockThreadLocal.set(Pair.of(true, List.of(1,2,3)));
        when(customJwt.get_perimeters()).thenReturn(mockThreadLocal);
        interventionRequestSpeRest = new InterventionRequestSpeRest(interventionRequestSpeService, interventionRequestHistoSpeService, irSpeService,customJwt);
        mockMvc = MockMvcBuilders.standaloneSetup(interventionRequestSpeRest).setControllerAdvice(new ErrorHandler()).build();
    }

    @Nested
    class RealTime {

        private final String ENDPOINT_REALTIME = "list";

        @Test
        void get_alertList_success() throws Exception {
            InterventionRequestFilter filter = InterventionRequestFilter.builder()
                    .userPerimeters(Pair.of(true, List.of(1, 2, 3)))
                    .build();
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(interventionRequestSpeService.list(filter)).willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(get("/spe/maintenance/interventionrequest/" + ENDPOINT_REALTIME + "?").contentType(APPLICATION_JSON)).andReturn()
                    .getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(interventionRequestSpeService).list(filter);
        }

        @Test
        void alertList_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=Closed&limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void alertList_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=Closed&offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void alertList_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=Closed&order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void alertList_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=Closed&orderby=randomFalseValue")).hasMessageContaining(
                    "Wrong value for field 'orderby' given : 'randomFalseValue'. (Possible values : [creationdate, code, equipmentName, comment, lastupdate, category, status])");
        }

        @Test
        void alertList_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=Closed&order=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
        }
    }

    @Nested
    class Histo {

        private final String ENDPOINT_HISTO = "listHisto";

        @Test
        void get_alertListHisto_success() throws Exception {
            InterventionRequestFilter filter = InterventionRequestFilter.builder()
                    .userPerimeters(Pair.of(true, List.of(1,2,3)))
                    .build();
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(interventionRequestHistoSpeService.listHisto(filter)).willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(get("/spe/maintenance/interventionrequest/" + ENDPOINT_HISTO + "?").contentType(APPLICATION_JSON)).andReturn()
                    .getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(interventionRequestHistoSpeService).listHisto(filter);
        }

        @Test
        void alertListHisto_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void alertListHisto_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void alertListHisto_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void alertListHisto_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "orderby=randomFalseValue")).hasMessageContaining(
                    "Wrong value for field 'orderby' given : 'randomFalseValue'. (Possible values : [creationdate, code, equipmentName, comment, lastupdate, category, status])");
        }

        @Test
        void alertListHisto_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "order=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
        }
    }

    Exception get_with_paramException(String endpoint, String param) throws Exception {
        return mockMvc.perform(get("/spe/maintenance/interventionrequest/" + endpoint + "?" + param).contentType(MediaType.APPLICATION_JSON)).andReturn().getResolvedException();
    }
}