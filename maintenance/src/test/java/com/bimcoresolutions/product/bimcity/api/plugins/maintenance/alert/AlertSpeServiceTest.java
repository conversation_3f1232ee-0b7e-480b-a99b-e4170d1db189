package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert;

import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;

import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.Camera;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AlertSpeServiceTest {

    @InjectMocks
    AlertSpeService alertSpeService;

    @Mock
    AlertSpeDao alertSpeDao;

    @Test
    void alertList_success() {
        //given
        AlertDTO model = new AlertDTO();
        model.setComment("comment");
        List<AlertDTO> models = List.of(model);

        EnveloppeGetter expected = new EnveloppeGetter();
        EnveloppeGetter.Pagination pagination = new EnveloppeGetter.Pagination();
        pagination.setDebut(0L);
        pagination.setFin(1L);
        pagination.setTotal(1L);

        expected.setPagination(pagination);
        expected.getItems().addAll(models);
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

        //mock
        when(alertSpeDao.alertRealtimeList(10L, 0L, "laststatuschangedate", "desc", Set.of(Camera), null, null, null, null, null, null, userPerimeters))
                .thenReturn(models);
        when(alertSpeDao.countRealTime(Set.of(Camera), null, null, null, null, null, null,userPerimeters))
                .thenReturn(1);

        //when
        EnveloppeGetter result = alertSpeService.alertRealTimeList(10L, 0L, "laststatuschangedate", "desc", Set.of(Camera), null, null, null, null, null, null,userPerimeters);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void alertListHisto_success() {
        //given
        AlertDTO model = new AlertDTO();
        model.setComment("comment");
        List<AlertDTO> models = List.of(model);

        EnveloppeGetter expected = new EnveloppeGetter();
        EnveloppeGetter.Pagination pagination = new EnveloppeGetter.Pagination();
        pagination.setDebut(0L);
        pagination.setFin(1L);
        pagination.setTotal(1L);

        expected.setPagination(pagination);
        expected.getItems().addAll(models);
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        //mock
        when(alertSpeDao.alertHistoList(10L, 0L, "laststatuschangedate", "desc", Set.of(Camera), null, null, null, null, null, null,userPerimeters))
                .thenReturn(models);
        when(alertSpeDao.countHisto(Set.of(Camera), null, null, null, null, null, null,userPerimeters))
                .thenReturn(1);

        //when
        EnveloppeGetter result = alertSpeService.alertHistoList(10L, 0L, "laststatuschangedate", "desc", Set.of(Camera), null, null, null, null, null, null,userPerimeters);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

}