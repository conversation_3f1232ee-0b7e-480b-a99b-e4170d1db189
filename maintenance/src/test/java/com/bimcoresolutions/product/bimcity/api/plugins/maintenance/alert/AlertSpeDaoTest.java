package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert;

import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.sql.DataSource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import static com.bimcoresolutions.util.model.service.histo.HistoManager.SCHEMA_HISTO;
import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.Camera;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.DynPanel;
import static com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.AlertSpeDao.ALERT_TABLE;
import static com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.AlertSpeDao.CLASS_ALERT;
import static com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alert.EAlertStatus.New;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PREFIX_ELEMTABLE;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static org.assertj.core.api.Assertions.assertThat;

public class AlertSpeDaoTest {
    public static final QueryFormer HISTO = new QueryFormer(POSTGRESQL, SCHEMA_HISTO);
    public static final QueryFormer REALTIME = new QueryFormer(POSTGRESQL, SCHEMA_RT);
    public static final ObjectMapper om = new ObjectMapper().findAndRegisterModules();

    private static final Semver VERSION_MODEL = new Semver("0.0.0");
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);

    private static AlertSpeDao alertSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/Alert-DynPanel-Camera_0_0_0.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("Alert-DynPanel-Camera", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        alertSpeDao = new AlertSpeDao(Mockito.mock(DataSource.class), REALTIME, Mockito.mock(DataSource.class), HISTO, om, FUNCTIONAL_MODEL_SERVICE);
    }

    @Nested
    class RealTime {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        @Test
        void queryList_with_one_equipment() {
            String expected = """
                    SELECT a._id_bimcore, a._perimeters, a.comment, a.laststatuschangedate, a.source, a.priority, a.status, a.creationdate, a.location, a.name, a.metier,
                     COALESCE("realtime"."elem_DynPanel"."_id_bimcore") as "equipmentId",
                     COALESCE("realtime"."elem_DynPanel".name) as "equipmentName",
                     CASE
                      WHEN "realtime"."elem_DynPanel"."_id_bimcore" is not null THEN 'DynPanel'
                      ELSE null
                     END
                     as "equipmentType"
                    FROM "realtime"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alert_dynpanel"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alert_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_DynPanel"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_DynPanel".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z'
                    ORDER BY a."creationdate" desc
                    LIMIT 10 OFFSET 0;""";
            StringBuilder result = alertSpeDao.queryList(10L, 0L, "creationdate", "desc", Set.of(DynPanel), 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"),
                    null, REALTIME, userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryList_with_two_equipment() {
            String expected = """
                    SELECT a._id_bimcore, a._perimeters, a.comment, a.laststatuschangedate, a.source, a.priority, a.status, a.creationdate, a.location, a.name, a.metier,
                     COALESCE("realtime"."elem_Camera"."_id_bimcore", "realtime"."elem_DynPanel"."_id_bimcore") as "equipmentId",
                     COALESCE("realtime"."elem_Camera".name, "realtime"."elem_DynPanel".name) as "equipmentName",
                     CASE
                      WHEN "realtime"."elem_Camera"."_id_bimcore" is not null THEN 'Camera'
                      WHEN "realtime"."elem_DynPanel"."_id_bimcore" is not null THEN 'DynPanel'
                      ELSE null
                     END
                     as "equipmentType"
                    FROM "realtime"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_camera" ON a."_id_bimcore" = "realtime"."rel_Alert_camera"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alert_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                    LEFT JOIN "realtime"."rel_Alert_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alert_dynpanel"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alert_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_Camera"."_id_bimcore" is not null
                      OR "realtime"."elem_DynPanel"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_Camera".name ILIKE '%myName%'
                      OR "realtime"."elem_DynPanel".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z'
                    ORDER BY a."creationdate" desc
                    LIMIT 10 OFFSET 0;""";
            Set<EquipmentClass> e = new LinkedHashSet<>();
            e.add(Camera);
            e.add(DynPanel);
            StringBuilder result = alertSpeDao.queryList(10L, 0L, "creationdate", "desc", e, 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"), null,
                    REALTIME, userPerimeters);

            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryCount_with_one_equipment() {
            String expected = """
                    SELECT COUNT(*)
                     FROM "realtime"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_camera" ON a."_id_bimcore" = "realtime"."rel_Alert_camera"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alert_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_Camera"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_Camera".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z';""";
            StringBuilder result = alertSpeDao.queryCount(Set.of(Camera), 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"), null, REALTIME,userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryCount_with_two_equipment() {
            String expected = """
                    SELECT COUNT(*)
                     FROM "realtime"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_camera" ON a."_id_bimcore" = "realtime"."rel_Alert_camera"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alert_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                    LEFT JOIN "realtime"."rel_Alert_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alert_dynpanel"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alert_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_Camera"."_id_bimcore" is not null
                      OR "realtime"."elem_DynPanel"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_Camera".name ILIKE '%myName%'
                      OR "realtime"."elem_DynPanel".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z';""";
            Set<EquipmentClass> e = new LinkedHashSet<>();
            e.add(Camera);
            e.add(DynPanel);
            StringBuilder result = alertSpeDao.queryCount(e, 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"), null, REALTIME,userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

    }

    @Nested
    class Histo {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        @Test
        void queryList_with_one_equipment() {
            String expected = """
                    SELECT a._id_bimcore, a._perimeters, a.comment, a.laststatuschangedate, a.source, a.priority, a.status, a.creationdate, a.location, a.name, a.metier,
                     COALESCE("realtime"."elem_DynPanel"."_id_bimcore") as "equipmentId",
                     COALESCE("realtime"."elem_DynPanel".name) as "equipmentName",
                     CASE
                      WHEN "realtime"."elem_DynPanel"."_id_bimcore" is not null THEN 'DynPanel'
                      ELSE null
                     END
                     as "equipmentType"
                    FROM "histo"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alert_dynpanel"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alert_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_DynPanel"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_DynPanel".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z'
                    ORDER BY a."creationdate" desc
                    LIMIT 10 OFFSET 0;""";
            StringBuilder result = alertSpeDao.queryList(10L, 0L, "creationdate", "desc", Set.of(DynPanel), 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"),
                    null, HISTO, userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryList_with_two_equipment() {
            String expected = """
                    SELECT a._id_bimcore, a._perimeters, a.comment, a.laststatuschangedate, a.source, a.priority, a.status, a.creationdate, a.location, a.name, a.metier,
                     COALESCE("realtime"."elem_Camera"."_id_bimcore", "realtime"."elem_DynPanel"."_id_bimcore") as "equipmentId",
                     COALESCE("realtime"."elem_Camera".name, "realtime"."elem_DynPanel".name) as "equipmentName",
                     CASE
                      WHEN "realtime"."elem_Camera"."_id_bimcore" is not null THEN 'Camera'
                      WHEN "realtime"."elem_DynPanel"."_id_bimcore" is not null THEN 'DynPanel'
                      ELSE null
                     END
                     as "equipmentType"
                    FROM "histo"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_camera" ON a."_id_bimcore" = "realtime"."rel_Alert_camera"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alert_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                    LEFT JOIN "realtime"."rel_Alert_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alert_dynpanel"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alert_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_Camera"."_id_bimcore" is not null
                      OR "realtime"."elem_DynPanel"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_Camera".name ILIKE '%myName%'
                      OR "realtime"."elem_DynPanel".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z'
                    ORDER BY a."creationdate" desc
                    LIMIT 10 OFFSET 0;""";
            Set<EquipmentClass> e = new LinkedHashSet<>();
            e.add(Camera);
            e.add(DynPanel);
            StringBuilder result = alertSpeDao.queryList(10L, 0L, "creationdate", "desc", e, 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"), null, HISTO,
                    userPerimeters);

            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryCount_with_one_equipment() {
            String expected = """
                    SELECT COUNT(*)
                     FROM "histo"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_camera" ON a."_id_bimcore" = "realtime"."rel_Alert_camera"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alert_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_Camera"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_Camera".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z';""";
            StringBuilder result = alertSpeDao.queryCount(Set.of(Camera), 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"), null, HISTO,userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryCount_with_two_equipment() {
            String expected = """
                    SELECT COUNT(*)
                     FROM "histo"."elem_Alert" AS a
                    LEFT JOIN "realtime"."rel_Alert_camera" ON a."_id_bimcore" = "realtime"."rel_Alert_camera"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alert_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                    LEFT JOIN "realtime"."rel_Alert_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alert_dynpanel"."_id_bimcore_origin"
                    LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alert_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                     WHERE 1=1
                     AND ("realtime"."elem_Camera"."_id_bimcore" is not null
                      OR "realtime"."elem_DynPanel"."_id_bimcore" is not null)
                     AND a.priority = '2'
                     AND a.status = 'New'
                     AND ("realtime"."elem_Camera".name ILIKE '%myName%'
                      OR "realtime"."elem_DynPanel".name ILIKE '%myName%')
                     AND a.source ILIKE 'mySource'
                     AND a.creationdate > '2022-05-01T13:16:00Z';""";
            Set<EquipmentClass> e = new LinkedHashSet<>();
            e.add(Camera);
            e.add(DynPanel);
            StringBuilder result = alertSpeDao.queryCount(e, 2, New, "myName", "mySource", Instant.parse("2022-05-01T13:16:00.00Z"), null, HISTO,userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

    }

    @Test
    void build_sources_query() {
        String query = alertSpeDao.sourcesQuery();
        String expected = "SELECT DISTINCT(\"source\") FROM \"" + REALTIME.getSchema() + "\".\"" + ALERT_TABLE + "\" WHERE \"source\" IS NOT NULL ORDER BY \"source\";";
        assertThat(query).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Nested
    class CategoriesInQuery {

        @Test
        void Realtime() {
            String query = alertSpeDao.categoriesInQuery(true);
            String expected = "SELECT DISTINCT(\"category\") FROM \"" + REALTIME.getSchema() + "\".\"" + DB_REALTIME_PREFIX_ELEMTABLE + CLASS_ALERT + "\" WHERE \"category\" IS NOT NULL ORDER BY \"category\";";
            assertThat(query).isEqualTo(StringUtils.normalizeSpace(expected));
        }

        @Test
        void Histo() {
            String query = alertSpeDao.categoriesInQuery(false);
            String expected = "SELECT DISTINCT(\"category\") FROM \"" + HISTO.getSchema() + "\".\"" + DB_REALTIME_PREFIX_ELEMTABLE + CLASS_ALERT + "\" WHERE \"category\" IS NOT NULL ORDER BY \"category\";";

            assertThat(query).isEqualTo(StringUtils.normalizeSpace(expected));
        }
    }

}