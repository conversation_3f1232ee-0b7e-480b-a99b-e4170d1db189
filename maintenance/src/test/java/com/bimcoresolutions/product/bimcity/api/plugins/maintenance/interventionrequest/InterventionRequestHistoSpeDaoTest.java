package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PeriodFilter;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.sql.DataSource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.Instant;

import static com.bimcoresolutions.util.model.service.histo.HistoManager.SCHEMA_HISTO;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PaginatedFilter.Order.asc;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

class InterventionRequestHistoSpeDaoTest {

    private static final DataSource dataSource = mock(DataSource.class);
    private static final Semver VERSION_MODEL = new Semver("0.0.0");

    public static final QueryFormer HISTO = new QueryFormer(POSTGRESQL, SCHEMA_HISTO);
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);
    private static InterventionRequestHistoSpeDao interventionRequestHistoSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/InterventionRequest-DynPanel-Camera_0_0_0.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("InterventionRequest-DynPanel-Camera", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        interventionRequestHistoSpeDao = new InterventionRequestHistoSpeDao(dataSource, FUNCTIONAL_MODEL_SERVICE, HISTO);
    }

    @Test
    void queryList_without_any_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                with orderedrelunique AS (
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, a.name AS "equipmentName", 'Camera' AS "equipmentType"
                 FROM "histo"."rel_Camera_interventionrequests" aa
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_Camera") a
                  ON aa."_id_bimcore_origin" = a."_id_bimcore"
                 UNION
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, b.name AS "equipmentName", 'DynPanel' AS "equipmentType"
                 FROM "histo"."rel_DynPanel_interventionrequests" bb
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_DynPanel") b
                  ON bb."_id_bimcore_origin" = b."_id_bimcore"
                )
                , orderedrels AS (
                 SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _creation_date AS timestamp
                 FROM orderedrelunique
                 UNION SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _end_date AS timestamp
                 FROM orderedrelunique
                  WHERE _end_date IS NOT NULL
                )
                , ordered AS (
                 SELECT _id_bimcore AS id_bimcore_ir, null AS id_bimcore_equipment, _creation_date, _end_date, null AS "equipmentName", null AS "equipmentType", _creation_date AS timestamp
                 FROM histo."elem_InterventionRequest"
                 WHERE 1=1
                 UNION SELECT * FROM orderedrels
                )
                SELECT ordered.timestamp, ir._id_bimcore, ir._perimeters, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline
                , array_remove(array_agg(distinct orderedrels."equipmentType"), null) AS "equipmentType"
                , array_remove(array_agg(distinct orderedrels."equipmentName"), null) AS "equipmentName"
                FROM ordered
                LEFT JOIN orderedrels
                 ON ordered.id_bimcore_ir = orderedrels.id_bimcore_ir
                  AND tsrange(orderedrels._creation_date, orderedrels._end_date) @> ordered.timestamp
                LEFT JOIN histo."elem_InterventionRequest" ir
                 ON ordered.id_bimcore_ir = ir._id_bimcore
                  AND tsrange(ir._creation_date, ir._end_date) @> ordered.timestamp
                WHERE 1=1
                GROUP BY ir._id_bimcore, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline, ordered.timestamp, ordered.id_bimcore_ir
                 ORDER BY ir.code asc OFFSET 0 LIMIT 10
                ;""";
        String result = interventionRequestHistoSpeDao.queryBddList(filter, SCHEMA_HISTO);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Test
    void queryCount_without_any_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                with orderedrelunique AS (
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, a.name AS "equipmentName", 'Camera' AS "equipmentType"
                 FROM "histo"."rel_Camera_interventionrequests" aa
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_Camera") a
                  ON aa."_id_bimcore_origin" = a."_id_bimcore"
                 UNION
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, b.name AS "equipmentName", 'DynPanel' AS "equipmentType"
                 FROM "histo"."rel_DynPanel_interventionrequests" bb
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_DynPanel") b
                  ON bb."_id_bimcore_origin" = b."_id_bimcore"
                )
                , orderedrels AS (
                 SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _creation_date AS timestamp
                 FROM orderedrelunique
                 UNION SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _end_date AS timestamp
                 FROM orderedrelunique
                  WHERE _end_date IS NOT NULL
                )
                , ordered AS (
                 SELECT _id_bimcore AS id_bimcore_ir, null AS id_bimcore_equipment, _creation_date, _end_date, null AS "equipmentName", null AS "equipmentType", _creation_date AS timestamp
                 FROM histo."elem_InterventionRequest"
                 WHERE 1=1
                 UNION SELECT * FROM orderedrels
                )
                SELECT count(distinct(ir._id_bimcore, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline, ordered.timestamp, ordered.id_bimcore_ir))
                FROM ordered
                LEFT JOIN orderedrels
                 ON ordered.id_bimcore_ir = orderedrels.id_bimcore_ir
                  AND tsrange(orderedrels._creation_date, orderedrels._end_date) @> ordered.timestamp
                LEFT JOIN histo."elem_InterventionRequest" ir
                 ON ordered.id_bimcore_ir = ir._id_bimcore
                  AND tsrange(ir._creation_date, ir._end_date) @> ordered.timestamp
                WHERE 1=1
                ;""";
        String result = interventionRequestHistoSpeDao.queryBddCount(filter, SCHEMA_HISTO);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Test
    void queryList_with_all_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .code("randomCode")
                .period(new PeriodFilter(Instant.parse("2022-11-03T17:21:26.273Z"), Instant.parse("2022-11-03T17:21:26.273Z")))
                .status("randomStatus")
                .equipmentName("randomName")
                .category("randomCategory")
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                with orderedrelunique AS (
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, a.name AS "equipmentName", 'Camera' AS "equipmentType"
                 FROM "histo"."rel_Camera_interventionrequests" aa
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_Camera") a
                  ON aa."_id_bimcore_origin" = a."_id_bimcore"
                 UNION
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, b.name AS "equipmentName", 'DynPanel' AS "equipmentType"
                 FROM "histo"."rel_DynPanel_interventionrequests" bb
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_DynPanel") b
                  ON bb."_id_bimcore_origin" = b."_id_bimcore"
                )
                , orderedrels AS (
                 SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _creation_date AS timestamp
                 FROM orderedrelunique
                 UNION SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _end_date AS timestamp
                 FROM orderedrelunique
                  WHERE _end_date IS NOT NULL
                )
                , ordered AS (
                 SELECT _id_bimcore AS id_bimcore_ir, null AS id_bimcore_equipment, _creation_date, _end_date, null AS "equipmentName", null AS "equipmentType", _creation_date AS timestamp
                 FROM histo."elem_InterventionRequest"
                 WHERE 1=1
                 UNION SELECT * FROM orderedrels
                )
                SELECT ordered.timestamp, ir._id_bimcore, ir._perimeters, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline
                , array_remove(array_agg(distinct orderedrels."equipmentType"), null) AS "equipmentType"
                , array_remove(array_agg(distinct orderedrels."equipmentName"), null) AS "equipmentName"
                FROM ordered
                LEFT JOIN orderedrels
                 ON ordered.id_bimcore_ir = orderedrels.id_bimcore_ir
                  AND tsrange(orderedrels._creation_date, orderedrels._end_date) @> ordered.timestamp
                LEFT JOIN histo."elem_InterventionRequest" ir
                 ON ordered.id_bimcore_ir = ir._id_bimcore
                  AND tsrange(ir._creation_date, ir._end_date) @> ordered.timestamp
                WHERE 1=1
                 AND ir.code ILIKE '%randomCode%'
                 AND ir.category ILIKE '%randomCategory%'
                 AND ir.status = 'randomStatus'
                 AND ir.lastupdate >= '2022-11-03T17:21:26.273Z'
                 AND ir.lastupdate <= '2022-11-03T17:21:26.273Z'
                 AND orderedrels."equipmentName" ilike '%randomName%'
                GROUP BY ir._id_bimcore, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline, ordered.timestamp, ordered.id_bimcore_ir
                 ORDER BY ir.code asc OFFSET 0 LIMIT 10
                ;""";
        String result = interventionRequestHistoSpeDao.queryBddList(filter, SCHEMA_HISTO);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Test
    void queryCount_with_all_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .code("randomCode")
                .period(new PeriodFilter(Instant.parse("2022-11-03T17:21:26.273Z"), Instant.parse("2022-11-03T17:21:26.273Z")))
                .status("randomStatus")
                .equipmentName("randomName")
                .category("randomCategory")
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                with orderedrelunique AS (
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, a.name AS "equipmentName", 'Camera' AS "equipmentType"
                 FROM "histo"."rel_Camera_interventionrequests" aa
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_Camera") a
                  ON aa."_id_bimcore_origin" = a."_id_bimcore"
                 UNION
                 SELECT "_id_bimcore_destination" AS id_bimcore_ir, "_id_bimcore_origin" AS id_bimcore_equipment, _creation_date, _end_date, b.name AS "equipmentName", 'DynPanel' AS "equipmentType"
                 FROM "histo"."rel_DynPanel_interventionrequests" bb
                 LEFT JOIN (SELECT distinct(_id_bimcore), name FROM "histo"."elem_DynPanel") b
                  ON bb."_id_bimcore_origin" = b."_id_bimcore"
                )
                , orderedrels AS (
                 SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _creation_date AS timestamp
                 FROM orderedrelunique
                 UNION SELECT id_bimcore_ir, id_bimcore_equipment, _creation_date, _end_date, "equipmentName", "equipmentType", _end_date AS timestamp
                 FROM orderedrelunique
                  WHERE _end_date IS NOT NULL
                )
                , ordered AS (
                 SELECT _id_bimcore AS id_bimcore_ir, null AS id_bimcore_equipment, _creation_date, _end_date, null AS "equipmentName", null AS "equipmentType", _creation_date AS timestamp
                 FROM histo."elem_InterventionRequest"
                 WHERE 1=1
                 UNION SELECT * FROM orderedrels
                )
                SELECT count(distinct(ir._id_bimcore, ir.code, ir.category, ir.creationdate, ir.lastupdate, ir.status, ir.comment, ir.sourcesystem, ir.deadline, ordered.timestamp, ordered.id_bimcore_ir))
                FROM ordered
                LEFT JOIN orderedrels
                 ON ordered.id_bimcore_ir = orderedrels.id_bimcore_ir
                  AND tsrange(orderedrels._creation_date, orderedrels._end_date) @> ordered.timestamp
                LEFT JOIN histo."elem_InterventionRequest" ir
                 ON ordered.id_bimcore_ir = ir._id_bimcore
                  AND tsrange(ir._creation_date, ir._end_date) @> ordered.timestamp
                WHERE 1=1
                 AND ir.code ILIKE '%randomCode%'
                 AND ir.category ILIKE '%randomCategory%'
                 AND ir.status = 'randomStatus'
                 AND ir.lastupdate >= '2022-11-03T17:21:26.273Z'
                 AND ir.lastupdate <= '2022-11-03T17:21:26.273Z'
                 AND orderedrels."equipmentName" ilike '%randomName%'
                ;""";
        String result = interventionRequestHistoSpeDao.queryBddCount(filter, SCHEMA_HISTO);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }
}