package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.api.base.spring.ErrorHandler;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import javax.sql.DataSource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;

import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.Camera;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(MockitoExtension.class)
public class AlarmSpeRestTest {

    public static final ObjectMapper om = new ObjectMapper().findAndRegisterModules();
    private static final Semver VERSION_MODEL = new Semver("0.0.0");
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);
    private static MockMvc mockMvc;
    private static AlarmSpeRest alarmSpeRest;
    private static AlarmSpeService alarmSpeService;
    private static AlarmSpeDao alarmSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/Alarm-DynPanel-Camera_0_0_0.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("Alarm-DynPanel-Camera", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        alarmSpeDao = new AlarmSpeDao(Mockito.mock(DataSource.class), new QueryFormer(POSTGRESQL, SCHEMA_RT), om, FUNCTIONAL_MODEL_SERVICE);
        alarmSpeService = Mockito.mock(AlarmSpeService.class);

        CustomJwtAuthenticationConverter jwtDecoder = Mockito.mock(CustomJwtAuthenticationConverter.class);
        // Mock the get_perimeters() method to return a valid ThreadLocal
        ThreadLocal<Pair<Boolean, List<Integer>>> mockThreadLocal = new ThreadLocal<>();
        mockThreadLocal.set(Pair.of(true, List.of(1,2,3)));
        when(jwtDecoder.get_perimeters()).thenReturn(mockThreadLocal);

        alarmSpeRest = new AlarmSpeRest(alarmSpeService, alarmSpeDao, jwtDecoder);
        mockMvc = MockMvcBuilders.standaloneSetup(alarmSpeRest).setControllerAdvice(new ErrorHandler()).build();
    }
    Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

    @Test
    void get_alarmList_success() throws Exception {
        EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
        enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
        given(alarmSpeService.alarmList(10L, 0L, "lastupdate", "desc", Set.of(Camera), null, null, null, null, null,userPerimeters))
                .willReturn(enveloppeGetter);

        MockHttpServletResponse response = mockMvc.perform(
                        get("/spe/maintenance/alarm/list?equipmentType=Camera")
                                .contentType(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        verify(alarmSpeService).alarmList(10L, 0L, "lastupdate", "desc", Set.of(Camera), null, null, null, null, null,userPerimeters);
    }

    @Test
    void get_alarmTypes_success() throws Exception {
        List types = List.of("Défaut de communication", "Neutre");
        Set<EquipmentClass> eqs = EquipmentClass.getEquipmentClassesFromString(List.of("Camera", "DynPanel"));
        given(alarmSpeService.typesList(eqs)).willReturn(types);

        MockHttpServletResponse response = mockMvc.perform(
                        get("/spe/maintenance/alarm/types/Camera,DynPanel")
                                .contentType(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();

        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
        verify(alarmSpeService).typesList(eqs);
    }

    @Test
    void alarmList_wrong_equipmentType() throws Exception {
        assertThat(get_with_paramException("equipmentType=RandomEquipment"))
                .isInstanceOf(WrongArgumentValueException.class)
                .hasMessageContaining("Wrong 'equipmentType' parameter given : '[RandomEquipment]'.");
    }

    @Test
    void alarmList_negative_limit() throws Exception {
        assertThat(get_with_paramException("equipmentType=Camera&limit=-1"))
                .isInstanceOf(WrongArgumentValueException.class)
                .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
    }

    @Test
    void alarmList_negative_offset() throws Exception {
        assertThat(get_with_paramException("equipmentType=Camera&offset=-1"))
                .isInstanceOf(WrongArgumentValueException.class)
                .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
    }

    @Test
    void alarmList_null_order() throws Exception {
        assertThat(get_with_paramException("equipmentType=Camera&order=null"))
                .isInstanceOf(WrongArgumentValueException.class)
                .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
    }

    @Test
    void alarmList_wrong_orderby() throws Exception {
        assertThat(get_with_paramException("equipmentType=Camera&orderby=randomFalseValue"))
                .isInstanceOf(WrongArgumentValueException.class)
                .hasMessageContaining("Wrong 'orderby' parameter given : 'randomFalseValue'. (Possible values : lastupdate,name,source,equipmentName)");
    }

    @Test
    void alarmList_wrong_order() throws Exception {
        assertThat(get_with_paramException("equipmentType=Camera&order=randomFalseValue"))
                .isInstanceOf(WrongArgumentValueException.class)
                .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
    }

    int get_with_param(String param) throws Exception {
        MockHttpServletResponse response = mockMvc.perform(
                        get("/spe/maintenance/alarm/list?" + param)
                                .contentType(MediaType.APPLICATION_JSON))
                .andReturn().getResponse();
        return response.getStatus();
    }

    Exception get_with_paramException(String param) throws Exception {
        return mockMvc.perform(get("/spe/maintenance/alarm/list?" + param).contentType(MediaType.APPLICATION_JSON)).andReturn().getResolvedException();
    }
}
