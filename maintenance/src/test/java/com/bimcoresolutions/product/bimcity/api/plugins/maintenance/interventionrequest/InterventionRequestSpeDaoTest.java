package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.interventionrequest;

import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PeriodFilter;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.sql.DataSource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.Instant;

import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PaginatedFilter.Order.asc;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

class InterventionRequestSpeDaoTest {

    private static final DataSource dataSource = mock(DataSource.class);
    private static final Semver VERSION_MODEL = new Semver("0.0.0");

    public static final QueryFormer REALTIME = new QueryFormer(POSTGRESQL, SCHEMA_RT);
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);
    private static InterventionRequestSpeDao interventionRequestSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/InterventionRequest-DynPanel-Camera_0_0_0.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("InterventionRequest-DynPanel-Camera", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        interventionRequestSpeDao = new InterventionRequestSpeDao(dataSource, FUNCTIONAL_MODEL_SERVICE, REALTIME);
    }

    @Test
    void queryList_without_any_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                SELECT ir._id_bimcore, ir._perimeters, ir.category, ir.code, ir.comment, ir.creationdate,
                 ir.lastupdate, ir.location, ir.sourcesystem, ir.status,
                 array_remove(array_agg(distinct aa.type) || array_agg(distinct bb.type), null) AS "equipmentType",
                 array_remove(array_agg(distinct aa.name) || array_agg(distinct bb.name), null) AS "equipmentName",ir.deadline
                FROM realtime."elem_InterventionRequest" ir
                LEFT JOIN "realtime"."rel_Camera_interventionrequests" a
                 ON ir."_id_bimcore" = a."_id_bimcore_destination"
                LEFT JOIN (SELECT _id_bimcore, name, 'Camera' AS type FROM "realtime"."elem_Camera") aa
                 ON a."_id_bimcore_origin" = aa."_id_bimcore"
                LEFT JOIN "realtime"."rel_DynPanel_interventionrequests" b
                 ON ir."_id_bimcore" = b."_id_bimcore_destination"
                LEFT JOIN (SELECT _id_bimcore, name, 'DynPanel' AS type FROM "realtime"."elem_DynPanel") bb
                 ON b."_id_bimcore_origin" = bb."_id_bimcore"
                WHERE 1=1
                GROUP BY ir._id_bimcore, ir._perimeters, ir.category, ir.code, ir.comment, ir.creationdate,
                 ir.lastupdate, ir.location, ir.sourcesystem, ir.status
                 ORDER BY ir.code asc OFFSET 0 LIMIT 10
                ;""";
        String result = interventionRequestSpeDao.queryBddList(filter, SCHEMA_RT);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Test
    void queryCount_without_any_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                SELECT COUNT(DISTINCT(ir."_id_bimcore"))
                FROM realtime."elem_InterventionRequest" ir
                WHERE 1=1
                ;""";
        String result = interventionRequestSpeDao.queryBddCount(filter, SCHEMA_RT);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Test
    void queryList_with_all_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .code("randomCode")
                .period(new PeriodFilter(Instant.parse("2022-11-03T17:21:26.273Z"), Instant.parse("2022-11-03T17:21:26.273Z")))
                .status("randomStatus")
                .equipmentName("randomName")
                .category("randomCategory")
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                SELECT ir._id_bimcore, ir._perimeters, ir.category, ir.code, ir.comment, ir.creationdate,
                 ir.lastupdate, ir.location, ir.sourcesystem, ir.status,
                 array_remove(array_agg(distinct aa.type) || array_agg(distinct bb.type), null) AS "equipmentType",
                 array_remove(array_agg(distinct aa.name) || array_agg(distinct bb.name), null) AS "equipmentName",ir.deadline
                FROM realtime."elem_InterventionRequest" ir
                LEFT JOIN "realtime"."rel_Camera_interventionrequests" a
                 ON ir."_id_bimcore" = a."_id_bimcore_destination"
                LEFT JOIN (SELECT _id_bimcore, name, 'Camera' AS type FROM "realtime"."elem_Camera") aa
                 ON a."_id_bimcore_origin" = aa."_id_bimcore"
                LEFT JOIN "realtime"."rel_DynPanel_interventionrequests" b
                 ON ir."_id_bimcore" = b."_id_bimcore_destination"
                LEFT JOIN (SELECT _id_bimcore, name, 'DynPanel' AS type FROM "realtime"."elem_DynPanel") bb
                 ON b."_id_bimcore_origin" = bb."_id_bimcore"
                WHERE 1=1
                 AND (aa.name ILIKE '%randomName%'
                  OR bb.name ILIKE '%randomName%')
                 AND ir.code ILIKE '%randomCode%'
                 AND ir.category ILIKE '%randomCategory%'
                 AND ir.status = 'randomStatus'
                 AND ir.lastupdate >= '2022-11-03T17:21:26.273Z'
                 AND ir.lastupdate <= '2022-11-03T17:21:26.273Z'
                GROUP BY ir._id_bimcore, ir._perimeters, ir.category, ir.code, ir.comment, ir.creationdate,
                 ir.lastupdate, ir.location, ir.sourcesystem, ir.status
                 ORDER BY ir.code asc OFFSET 0 LIMIT 10
                ;""";
        String result = interventionRequestSpeDao.queryBddList(filter, SCHEMA_RT);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Test
    void queryCount_with_all_where() {
        InterventionRequestFilter filter = InterventionRequestFilter.builder()
                .code("randomCode")
                .period(new PeriodFilter(Instant.parse("2022-11-03T17:21:26.273Z"), Instant.parse("2022-11-03T17:21:26.273Z")))
                .status("randomStatus")
                .equipmentName("randomName")
                .category("randomCategory")
                .orderby(InterventionRequestFilter.InterventionFilterOrderby.code)
                .order(asc)
                .offset(0l)
                .limit(10l)
                .build();
        String expected = """
                SELECT COUNT(DISTINCT(ir."_id_bimcore"))
                FROM realtime."elem_InterventionRequest" ir
                LEFT JOIN "realtime"."rel_Camera_interventionrequests" a
                 ON ir."_id_bimcore" = a."_id_bimcore_destination"
                LEFT JOIN (SELECT _id_bimcore, name, 'Camera' AS type FROM "realtime"."elem_Camera") aa
                 ON a."_id_bimcore_origin" = aa."_id_bimcore"
                LEFT JOIN "realtime"."rel_DynPanel_interventionrequests" b
                 ON ir."_id_bimcore" = b."_id_bimcore_destination"
                LEFT JOIN (SELECT _id_bimcore, name, 'DynPanel' AS type FROM "realtime"."elem_DynPanel") bb
                 ON b."_id_bimcore_origin" = bb."_id_bimcore"
                WHERE 1=1
                 AND (aa.name ILIKE '%randomName%'
                  OR bb.name ILIKE '%randomName%')
                 AND ir.code ILIKE '%randomCode%'
                 AND ir.category ILIKE '%randomCategory%'
                 AND ir.status = 'randomStatus'
                 AND ir.lastupdate >= '2022-11-03T17:21:26.273Z'
                 AND ir.lastupdate <= '2022-11-03T17:21:26.273Z'
                ;""";
        String result = interventionRequestSpeDao.queryBddCount(filter, SCHEMA_RT);
        assertThat(StringUtils.normalizeSpace(result.toString())).isEqualTo(StringUtils.normalizeSpace(expected));
    }
}