package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm;

import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.List;
import java.util.Set;

import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.Camera;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AlarmSpeServiceTest {

    @InjectMocks
    AlarmSpeService alarmSpeService;

    @Mock
    AlarmSpeDao alarmSpeDao;

    @Test
    void alarmBddList_success() {
        //given
        AlarmDTO model = new AlarmDTO();
        model.setName("nom_model");
        model.set_id_bimcore("id1");
        List<AlarmDTO> models = List.of(model);
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

        EnveloppeGetter expected = new EnveloppeGetter();
        EnveloppeGetter.Pagination pagination = new EnveloppeGetter.Pagination();
        pagination.setDebut(0L);
        pagination.setFin(1L);
        pagination.setTotal(1L);

        expected.setPagination(pagination);
        expected.getItems().addAll(models);

        //mock
        when(alarmSpeDao.list(10L, 0L, "lastupdate", "desc", Set.of(Camera), "typeDefaut", "nameEquipement", "source",
                Instant.parse("2022-08-05T14:00:00.00Z"), Instant.parse("2022-08-05T14:00:00.000Z"),userPerimeters))
                .thenReturn(models);
        when(alarmSpeDao.count(Set.of(Camera), "typeDefaut", "nameEquipement", "source", Instant.parse("2022-08-05T14:00:00.000Z"),
                Instant.parse("2022-08-05T14:00:00.000Z"),userPerimeters))
                .thenReturn(1);
        //when
        EnveloppeGetter result = alarmSpeService.alarmList(10L, 0L, "lastupdate", "desc", Set.of(Camera), "typeDefaut", "nameEquipement", "source",
                Instant.parse("2022-08-05T14:00:00.00Z"), Instant.parse("2022-08-05T14:00:00.000Z"),userPerimeters);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void alarmBddTypes_success() {

        List expected = List.of("Défaut de communication", "Neutre");
        Set<EquipmentClass> eqs = EquipmentClass.getEquipmentClassesFromString(List.of("Camera", "DynPanel"));

        //mock
        when(alarmSpeDao.typesList(eqs)).thenReturn(expected);

        //when
        List result = alarmSpeService.typesList(eqs);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

}