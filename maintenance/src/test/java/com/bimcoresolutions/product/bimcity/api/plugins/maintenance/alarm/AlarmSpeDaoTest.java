package com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm;

import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.sql.DataSource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.time.Instant;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.Camera;
import static com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass.DynPanel;
import static com.bimcoresolutions.product.bimcity.api.plugins.maintenance.alarm.AlarmSpeDao.ALARM_TABLE;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static org.assertj.core.api.Assertions.assertThat;

public class AlarmSpeDaoTest {

    public static final QueryFormer REALTIME = new QueryFormer(POSTGRESQL, SCHEMA_RT);
    public static final ObjectMapper om = new ObjectMapper().findAndRegisterModules();
    private static final Semver VERSION_MODEL = new Semver("0.0.0");
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);

    private static AlarmSpeDao alarmSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/Alarm-DynPanel-Camera_0_0_0.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("Alarm-DynPanel-Camera", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        alarmSpeDao = new AlarmSpeDao(Mockito.mock(DataSource.class), REALTIME, om, FUNCTIONAL_MODEL_SERVICE);
    }

    Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
    @Test
    void queryList_with_one_equipment() {
        String expected = """
                SELECT a._id_bimcore, a._perimeters, a.lastupdate, a.name, a.source,
                 COALESCE("realtime"."elem_DynPanel".name) as "equipmentName",
                 CASE
                  WHEN "realtime"."elem_DynPanel"."_id_bimcore" is not null THEN 'DynPanel'
                  ELSE null
                 END
                 as "equipmentType"
                FROM "realtime"."elem_Alarm" AS a
                LEFT JOIN "realtime"."rel_Alarm_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alarm_dynpanel"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alarm_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                 WHERE "presence" = true
                 AND a.name ILIKE '%defaultType%'
                 AND ("realtime"."elem_DynPanel".name ILIKE '%nameEquipement%')
                 AND a.source ILIKE 'source'
                 AND a.lastupdate > '2022-05-05T14:00:00Z'
                 AND a.lastupdate < '2022-08-05T14:00:00Z'
                ORDER BY a."lastupdate" desc
                LIMIT 10 OFFSET 0;""";
        StringBuilder result = alarmSpeDao.queryList(10L, 0L, "lastupdate", "desc", Set.of(DynPanel), "defaultType", "nameEquipement", "source",
                Instant.parse("2022-05-05T14:00:00.00Z"), Instant.parse("2022-08-05T14:00:00.00Z"),userPerimeters);
        assertThat(result.toString()).isEqualTo(expected);
    }

    @Test
    void queryList_with_two_equipment() {
        String expected = """
                SELECT a._id_bimcore, a._perimeters, a.lastupdate, a.name, a.source,
                 COALESCE("realtime"."elem_Camera".name, "realtime"."elem_DynPanel".name) as "equipmentName",
                 CASE
                  WHEN "realtime"."elem_Camera"."_id_bimcore" is not null THEN 'Camera'
                  WHEN "realtime"."elem_DynPanel"."_id_bimcore" is not null THEN 'DynPanel'
                  ELSE null
                 END
                 as "equipmentType"
                FROM "realtime"."elem_Alarm" AS a
                LEFT JOIN "realtime"."rel_Alarm_camera" ON a."_id_bimcore" = "realtime"."rel_Alarm_camera"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alarm_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                LEFT JOIN "realtime"."rel_Alarm_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alarm_dynpanel"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alarm_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                 WHERE "presence" = true
                 AND a.name ILIKE '%defaultType%'
                 AND ("realtime"."elem_Camera".name ILIKE '%nameEquipement%'
                  OR "realtime"."elem_DynPanel".name ILIKE '%nameEquipement%')
                 AND a.source ILIKE 'source'
                 AND a.lastupdate > '2022-05-05T14:00:00Z'
                 AND a.lastupdate < '2022-08-05T14:00:00Z'
                ORDER BY a."lastupdate" desc
                LIMIT 10 OFFSET 0;""";
        Set<EquipmentClass> e = new LinkedHashSet<>();
        e.add(Camera);
        e.add(DynPanel);
        StringBuilder result = alarmSpeDao.queryList(10L, 0L, "lastupdate", "desc", e, "defaultType", "nameEquipement", "source", Instant.parse("2022-05-05T14:00:00.00Z"),
                Instant.parse("2022-08-05T14:00:00.00Z"),userPerimeters);
        assertThat(result.toString()).isEqualTo(expected);
    }

    @Test
    void queryCount_with_one_equipment() {
        String expected = """
                SELECT COUNT(*)
                FROM "realtime"."elem_Alarm" AS a
                LEFT JOIN "realtime"."rel_Alarm_camera" ON a."_id_bimcore" = "realtime"."rel_Alarm_camera"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alarm_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                 WHERE "presence" = true
                 AND a.name ILIKE '%defaultType%'
                 AND ("realtime"."elem_Camera".name ILIKE '%nameEquipement%')
                 AND a.source ILIKE 'source'
                 AND a.lastupdate > '2022-05-05T14:00:00Z'
                 AND a.lastupdate < '2022-08-05T14:00:00Z';""";
        StringBuilder result = alarmSpeDao.queryCount(Set.of(Camera), "defaultType", "nameEquipement", "source", Instant.parse("2022-05-05T14:00:00.00Z"),
                Instant.parse("2022-08-05T14:00:00.00Z"),userPerimeters);
        assertThat(result.toString()).isEqualTo(expected);
    }

    @Test
    void queryCount_with_two_equipment() {
        String expected = """
                SELECT COUNT(*)
                FROM "realtime"."elem_Alarm" AS a
                LEFT JOIN "realtime"."rel_Alarm_camera" ON a."_id_bimcore" = "realtime"."rel_Alarm_camera"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alarm_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                LEFT JOIN "realtime"."rel_Alarm_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alarm_dynpanel"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alarm_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                 WHERE "presence" = true
                 AND a.name ILIKE '%defaultType%'
                 AND ("realtime"."elem_Camera".name ILIKE '%nameEquipement%'
                  OR "realtime"."elem_DynPanel".name ILIKE '%nameEquipement%')
                 AND a.source ILIKE 'source'
                 AND a.lastupdate > '2022-05-05T14:00:00Z'
                 AND a.lastupdate < '2022-08-05T14:00:00Z';""";
        Set<EquipmentClass> e = new LinkedHashSet<>();
        e.add(Camera);
        e.add(DynPanel);
        StringBuilder result = alarmSpeDao.queryCount(e, "defaultType", "nameEquipement", "source", Instant.parse("2022-05-05T14:00:00.00Z"),
                Instant.parse("2022-08-05T14:00:00.00Z"),userPerimeters);
        assertThat(result.toString()).isEqualTo(expected);
    }

    @Test
    void queryTypes_with_two_equipments() {
        String expected = """
                SELECT distinct a.name
                 FROM "realtime"."elem_Alarm" AS a
                LEFT JOIN "realtime"."rel_Alarm_dynpanel" ON a."_id_bimcore" = "realtime"."rel_Alarm_dynpanel"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_DynPanel" ON "realtime"."rel_Alarm_dynpanel"."_id_bimcore_destination" = "realtime"."elem_DynPanel"."_id_bimcore"
                LEFT JOIN "realtime"."rel_Alarm_camera" ON a."_id_bimcore" = "realtime"."rel_Alarm_camera"."_id_bimcore_origin"
                LEFT JOIN "realtime"."elem_Camera" ON "realtime"."rel_Alarm_camera"."_id_bimcore_destination" = "realtime"."elem_Camera"."_id_bimcore"
                WHERE 1=1
                 AND ("realtime"."elem_DynPanel"."_id_bimcore" is not null
                  OR "realtime"."elem_Camera"."_id_bimcore" is not null);""";

        Set<EquipmentClass> e = new LinkedHashSet<>();
        e.add(DynPanel);
        e.add(Camera);
        StringBuilder result = alarmSpeDao.queryDefaultType(e);
        assertThat(result.toString()).isEqualTo(expected);
    }

    @Test
    void build_sources_query() {
        String query = alarmSpeDao.sourcesQuery();
        String expected = "SELECT DISTINCT(\"source\") FROM \"" + REALTIME.getSchema() + "\".\"" + ALARM_TABLE + "\" WHERE \"source\" IS NOT NULL ORDER BY \"source\";";
        assertThat(query).isEqualTo(StringUtils.normalizeSpace(expected));
    }

}
