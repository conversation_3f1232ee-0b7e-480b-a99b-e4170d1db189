package com.bimcoresolutions.product.bimcity.api.plugins.common.util;

import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.FunctionalException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.postgis.jdbc.PGgeometry;
import net.postgis.jdbc.geometry.Geometry;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.locationtech.jts.io.WKTReader;
import org.reflections.ReflectionUtils;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.*;
import java.util.stream.Collectors;

public abstract class ASpeDao {

    private static final Logger LOGGER = LogManager.getLogger();
    static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    protected final DataSource ds;
    protected final ObjectMapper om;
    protected final QueryFormer qf;
    protected final WKTReader wktReader;

    protected ASpeDao(DataSource ds, QueryFormer qf, ObjectMapper om) {
        this.om = om;
        this.ds = ds;
        this.qf = qf;
        this.wktReader = new WKTReader();
    }

    public List<Map<String, Object>> resultSetToGeoJson(ResultSet rs) throws SQLException {
        List<Map<String, Object>> elements = new ArrayList<>();
        ResultSetMetaData rsmd = rs.getMetaData();
        ObjectMapper mapper = new ObjectMapper();
        if (rs.isBeforeFirst()) { // S'il y a des résultats retournés
            while (rs.next()) {
                Map<String, Object> element = new HashMap<>();
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    if (!rsmd.getColumnTypeName(i + 1).equalsIgnoreCase("geometry")) {
                        String value = rs.getString(i + 1);
                        if (value != null) {
                            element.put(rsmd.getColumnName(i + 1), value);
                        }
                    } else {
                        Geometry geom = null;
                        PGgeometry pgGeometry = (PGgeometry) rs.getObject(i + 1);
                        if (!rs.wasNull() && pgGeometry != null)
                            geom = pgGeometry.getGeometry();
                        if (geom != null) {
                            if (geom.getTypeString().equals("GEOMETRYCOLLECTION")) {
                                StringBuilder st = new StringBuilder();
                                st.append("{");
                                st.append("\"type\":\"GeometryCollection\",\"geometries\": [");
                                String toto = geom.getValue().substring(0, geom.getValue().length() - 1).replace("GEOMETRYCOLLECTION(", "");
                                String[] toto2 = toto.split("\\W, ");
                                for (int j = 0; j < toto2.length; j++) {
                                    String type = toto2[j].substring(0, toto2[j].indexOf("("));
                                    String data = toto2[j].replace(type, "");
                                    if (j < toto2.length - 1) {
                                        data = data + ")";
                                    }
                                    st.append(convertToGeoJsonGeometry(data, type));
                                    st.append(",");
                                }
                                st.delete(st.length() - 1, st.length());
                                st.append("]}");
                                element.put("geometry", st);
                            } else {
                                element.put("geometry", convertToGeoJsonGeometry(geom.getValue(), geom.getTypeString()));
                            }
                        }
                    }
                }
                element.remove("RANG");
                element.remove("idt");

                // creation de la geometry
                Map<String, Object> geojson = new HashMap<>();
                try {
                    geojson.put("geometry", mapper.readValue(element.get("geometry").toString(), new TypeReference<Map<String, Object>>() {
                    }));
                } catch (Exception e) {
                    LOGGER.error("Erreur lors de la creation du geojson", e);
                }
                element.remove("geometry");
                geojson.put("type", "Feature");
                geojson.put("properties", element);
                elements.add(geojson);
            }
        }
        return elements;
    }

    /**
     * construis un geojson a partir du retour des colonnes geometry de la BDD
     *
     * @param wkt  valeur de la colonne geometry retournee dans le resulset
     * @param type type de la geometry : POINT/POLYGON/MULTIPOLYGON/MULTIPOINT/LINESTRING
     * @return
     */
    private String convertToGeoJsonGeometry(String wkt, String type) { // A utiliser sur les colonnes geometry pour geojson geometry
        StringBuilder geojson = new StringBuilder();
        geojson.append("{");
        geojson.append("\"type\":\"");
        switch (type) {
            case "POINT" -> geojson.append("Point");
            case "POLYGON" -> geojson.append("Polygon");
            case "MULTIPOLYGON" -> geojson.append("MultiPolygon");
            case "MULTIPOINT" -> geojson.append("MultiPoint");
            case "LINESTRING" -> geojson.append("LineString");
            default -> {
            }
        }
        geojson.append("\",");
        String coordinates = wkt.replaceAll(type.toUpperCase(), "");
        coordinates = coordinates.replaceAll("\\r", "").replaceAll("\\n", "").replaceAll("\\t", "");
        coordinates = coordinates.replaceAll("\\)\\),\\(\\( ", "@").replaceAll("\\),\\( ", "#").replaceAll(", ", "%");
        coordinates = coordinates.replaceAll(" ", ",").replaceAll("%", "],[").replaceAll("#", "]],[[").replaceAll("@", "]]],[[[");
        coordinates = coordinates.replaceAll("\\)", "]").replaceAll("\\(", "[");
        coordinates = "[" + coordinates + "]";
        if (type.equalsIgnoreCase("point")) {
            coordinates = coordinates.substring(1, coordinates.length() - 1);
        }
        //LOGGER.debug(coordinates);
        geojson.append("\"coordinates\":");
        geojson.append(coordinates);
        geojson.append("}");
        return geojson.toString();
    }

    /**
     * map un ResulSet sur un model
     *
     * @param rs    ResultSet postgresql
     * @param clazz classe model de retour
     * @return
     * @throws SQLException
     */
    protected <T> List<T> resultSetToObj(ResultSet rs, Class<T> clazz) throws SQLException {

        List<T> result = new ArrayList<>();
        ResultSetMetaData rsmd = rs.getMetaData();

        if (rs.isBeforeFirst()) { // S'il y a des résultats retournés
            while (rs.next()) {
                T obj = null;
                try {
                    obj = clazz.getDeclaredConstructor().newInstance();
                } catch (Exception e) {
                    LOGGER.error(e);
                }
                for (int i = 0; i < rsmd.getColumnCount(); i++) {
                    String column = rsmd.getColumnName(i + 1);
                    Optional<Field> opField = ReflectionUtils.getAllFields(clazz, ReflectionUtils.withName(column)).stream().findFirst();
                    // LOGGER.debug(opField.toString());
                    Field champ;
                    if (opField.isPresent()) {
                        champ = opField.get();
                        try {
                            String value = rs.getString(i + 1);
                            if (value == null) {
                                LOGGER.warn("{} : NULL", column);
                            } else if (champ.getType().isAssignableFrom(String.class)) {
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), String.class).invoke(obj, value);
                            } else if (champ.getType().isAssignableFrom(org.locationtech.jts.geom.Geometry.class)) {
                                Geometry geom = null;
                                PGgeometry pgGeometry = (PGgeometry) rs.getObject(i + 1);
                                if (!rs.wasNull() && pgGeometry != null)
                                    geom = pgGeometry.getGeometry();
                                if (geom != null) {
                                    StringBuffer sb = new StringBuffer();
                                    geom.outerWKT(sb);
                                    org.locationtech.jts.geom.Geometry geometryObject = this.wktReader.read(sb.toString());
                                    clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), org.locationtech.jts.geom.Geometry.class).invoke(obj, geometryObject);
                                }
                            } else if (champ.getType().isAssignableFrom(Boolean.class)) {
                                Boolean value2 = rs.getBoolean(i + 1);
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), Boolean.class).invoke(obj, value2);
                            } else if (champ.getType().isAssignableFrom(Integer.class)) {
                                Integer value2 = rs.getInt(i + 1);
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), Integer.class).invoke(obj, value2);
                            } else if (champ.getType().isAssignableFrom(Float.class)) {
                                Float value2 = rs.getFloat(i + 1);
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), Float.class).invoke(obj, value2);
                            } else if (champ.getType().isAssignableFrom(Long.class)) {
                                Long value2 = rs.getLong(i + 1);
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), Long.class).invoke(obj, value2);
                            } else if (champ.getType().isAssignableFrom(Double.class)) {
                                Double value2 = rs.getDouble(i + 1);
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), Double.class).invoke(obj, value2);
                            } else if (champ.getType().isAssignableFrom(Date.class)) {
                                Date value2 = rs.getTimestamp(i + 1);
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), Date.class).invoke(obj, value2);
                            } else if (champ.getType().isAssignableFrom(Instant.class)) {
                                Instant value2 = rs.getTimestamp(i + 1).toInstant();
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), Instant.class).invoke(obj, value2);
                            } else if (champ.getGenericType().getTypeName().equals("java.util.List<java.lang.Integer>") ||
                                champ.getGenericType().getTypeName().equals("java.util.ArrayList<java.lang.Integer>")) {
                                value = value.substring(1, value.length() - 1);
                                List<Integer> items = Arrays.stream(value.split("\\s*,\\s*"))
                                        .map(String::trim)
                                        .filter(s -> !s.isEmpty())
                                        .map(Integer::valueOf)
                                        .collect(Collectors.toList());
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), List.class).invoke(obj, items);
                            } else if (champ.getType().isAssignableFrom(List.class)) {
                                value = value.substring(1, value.length() - 1);
                                List<String> items = Arrays.asList(value.split("\\s*,\\s*"));
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), List.class).invoke(obj, items);
                            } else if (champ.getType().isAssignableFrom(ArrayList.class)) {
                                value = value.substring(1, value.length() - 1);
                                ArrayList<String> items = new ArrayList<>(Arrays.asList(value.split("\\s*,\\s*")));
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), ArrayList.class).invoke(obj, items);
                            } else if (champ.getType().isAssignableFrom(JsonNode.class)) {
                                JsonNode value2 = om.readValue(value, JsonNode.class);
                                clazz.getMethod("set" + StringUtils.capitalize(champ.getName()), JsonNode.class).invoke(obj, value2);
                            } else {
                                LOGGER.error("Type non pris en charge : {}", champ.getType().getName());
                            }
                        } catch (Exception e) {
                            LOGGER.error("erreur de mapping : ", e);
                        }
                    }
                }
                result.add(obj);
            }
        }
        return result;
    }

    /**
     * voir {@link #testOrderByGenerique(List, List, List, List, Class)}
     */
    protected <T extends ModelSpe> StringBuilder testOrderByRef(List<String> prefix, List<String> orderBy, List<String> order, Class<T> clazz) {
        return testOrderByGenerique(prefix, orderBy, new ArrayList<>(), order, clazz);
    }

    /**
     * voir {@link #testOrderByGenerique(List, List, List, List, Class)}
     */
    protected <T extends ModelSpe> StringBuilder testOrderBySpe(List<String> prefix, List<String> orderBy, List<String> orderBySpe, List<String> order, Class<T> clazz) {
        return testOrderByGenerique(prefix, orderBy, orderBySpe, order, clazz);
    }

    /**
     * pour creer la partie ORDER BY de la query.
     * <br> <b>ATTENTION : </b>prefix.size == orderBy.size == order.size
     *
     * @param prefix     prefixs a ajouter devant les orderBy
     * @param orderBy    noms des colonnes de la base ordronnees par ordre d'ORDER BY
     * @param orderBySpe a utiliser si le nom de la colonne sur laquelle on filtre n'est pas une colonne du model de retour
     * @param order      ASC/DESC pour chaque element de orderBy
     * @param clazz      class model de retour
     * @return
     */
    private <T> StringBuilder testOrderByGenerique(List<String> prefix, List<String> orderBy, List<String> orderBySpe, List<String> order, Class<T> clazz) {
        StringBuilder orderByBuilder = new StringBuilder();
        orderByBuilder.append(" ORDER BY ");
        if (orderBy.size() != order.size()) {
            throw new FunctionalException("orderBy.size() != order.size() : " + orderBy.size() + " != " + order.size());
        } else if (orderBy.size() != prefix.size()) {
            throw new FunctionalException("orderBy.size() != prefix.size() : " + orderBy.size() + " != " + prefix.size());
        } else if (!orderBy.isEmpty()) { // Si on a un pattern spécifié
            for (int i = 0; i < orderBy.size(); i++) {
                String pre = prefix.get(i);
                if (!pre.isEmpty()) { // s'il y a un prefix, on ajoute le point
                    pre += ".";
                }
                String column = orderBy.get(i);
                String ord = order.get(i);
                if (!"asc".equalsIgnoreCase(ord) && !"desc".equalsIgnoreCase(ord)) {
                    throw new FunctionalException("bad order specified : '" + ord + "', must be 'asc' or 'desc'");
                }
                Optional<Field> opField = ReflectionUtils.getAllFields(clazz, ReflectionUtils.withName(column)).stream().findFirst();
                if (opField.isPresent()) { // Si ce champ appartient a l'objet retour
                    orderByBuilder.append(pre + qf.addIdentifier(column) + " " + ord + ", ");
                } else if (orderBySpe.contains(column)) { // Si ce champ est une colonne du resultset mais pas de l'objet retourne
                    orderByBuilder.append(pre + qf.addIdentifier(column) + " " + ord + ", ");
                } else { // Si ce champ n'est pas une colonne du resultset
                    LOGGER.error("champ non reconnu : {}", column);
                    throw new TechnicalException("champ non reconnu : " + column);
                }
            }
            orderByBuilder.delete(orderByBuilder.length() - 2, orderByBuilder.length());
        }
        return orderByBuilder;
    }

    /**
     * pour creer tester et ajouter ASC/DESC apres un orderby
     * <br> a utiliser dans les cas de requetes complexes seulement, sinon utliser les methodes testOrderBy
     *
     * @param order
     * @return
     */
    protected StringBuilder testOrder(String order) {
        StringBuilder orderBuilder = new StringBuilder();

        if (order != null) {
            if ("ASC".equalsIgnoreCase(order) || "DESC".equalsIgnoreCase(order)) {
                orderBuilder.append(" " + order);
            } else {
                throw new TechnicalException("Ordre de retour des données mal renseigné (ASC/DESC)");
            }
        }
        return orderBuilder;
    }

    /**
     * pour creer la partie LIMIT / OFFSET de la query
     * <br>ex : limit 10, offset 10 => les elements de 10 à 19 inclus
     *
     * @param limit  nombre d'elements max retournes
     * @param offset
     * @return
     */
    protected StringBuilder testLimitOffset(Long limit, Long offset) {
        StringBuilder limitOffsetBuilder = new StringBuilder();
        // Si l'on veut requeter toute la profondeur, soit on ne donne pas le param limit au endpoint, soit on met un limit <=0 dans le cas d'endpoint avec defaultvalue
        if (limit != null && limit < 0) {
            limit = null;
        }

        if (limit != null) {
            if (offset != null) { // Limit et offset
                limitOffsetBuilder.append(" LIMIT " + limit + " OFFSET " + offset);
            } else { // Seulement limit
                limitOffsetBuilder.append(" LIMIT " + limit + " OFFSET 0");
            }
        } else {
            if (offset != null) { // Seulement offset
                limitOffsetBuilder.append(" OFFSET " + offset);
            }  // Ni limit, ni offset

        }
        return limitOffsetBuilder;
    }

    /**
     * pour creer les WHERE du type a.code IN ('','') OR b.code IN ('','')
     * <br>voir {@link #testWhereClauseSearchExacteQuery(String, Boolean, List, String, boolean)}
     *
     * @param col        colonne de la base
     * @param isNegative true si clause 'NOT'
     * @param values     valeurs recherchees possibles
     * @param prefixs    differents prefixs sur de recherche (a et b dans l'exemple)
     * @return
     */
    protected StringBuilder testWhereClauseSearchExacteQueryOr(String col, Boolean isNegative, List<? extends Object> values, List<String> prefixs) {
        if (!values.isEmpty() && !prefixs.isEmpty()) {
            if (prefixs.size() < 2) {
                LOGGER.warn("Only 1 prefix, OR clause can't be used. Executing simple testWhereClauseSearchExacteQuery");
                return testWhereClauseSearchExacteQuery(col, isNegative, values, prefixs.get(0), true);
            } else {
                StringBuilder sqlQuery = new StringBuilder().append(" AND (");
                prefixs.forEach(p -> sqlQuery.append(testWhereClauseSearchExacteQuery("code", false, values, p, true)).append(" OR"));
                sqlQuery.delete(sqlQuery.length() - 3, sqlQuery.length()).append(")");
                return sqlQuery;
            }
        } else {
            return new StringBuilder();
        }
    }

    /**
     * pour creer les WHERE de type =, IN
     *
     * @param key                colonne de la base
     * @param isNegative         true si clause 'NOT'
     * @param value              valeur recherchee possible
     * @param prefix             prefix de la colonne
     * @param isFirstWhereClause true ajoute 'AND ' au debut
     * @return
     */
    protected StringBuilder testWhereClauseSearchExacteQuery(String key, Boolean isNegative, Object value, String prefix, boolean isFirstWhereClause) {
        if (value != null) {
            return testWhereClauseSearchExacteQuery(key, isNegative, List.of(value), prefix, isFirstWhereClause);
        } else {
            return new StringBuilder();
        }
    }

    /**
     * pour creer les WHERE de type =, IN
     *
     * @param col                colonne de la base
     * @param isNegative         true si clause 'NOT'
     * @param values             valeurs recherchees possibles
     * @param prefix             prefix de la colonne
     * @param isFirstWhereClause true ajoute 'AND ' au debut
     * @return
     */
    protected StringBuilder testWhereClauseSearchExacteQuery(String col, Boolean isNegative, List<? extends Object> values, String prefix, boolean isFirstWhereClause) {
        StringBuilder query = new StringBuilder();
        if (!values.isEmpty()) {
            if (!isFirstWhereClause) {
                query.append("\nAND ");
            } else {
                query.append(" ");
            }

            if (prefix != null && !prefix.isEmpty()) {
                query.append(prefix).append(".");
            }
            query.append(qf.addIdentifier(col));
            if (isNegative) {
                query.append(" NOT");
            }
            query.append(" IN (");
            for (Object value : values) {
                query.append("'").append(value.toString().replaceAll("'", "''")).append("', ");
            }
            query.delete(query.length() - 2, query.length());
            query.append(")");
        }
        return query;
    }

    /**
     * pour creer les clauses WHERE de type LIKE, ILIKE, NOT LIKE, NOT ILIKE
     *
     * @param col                colonne de la base
     * @param isNegative         true si clause 'NOT'
     * @param value              valeur recherchee
     * @param prefix             prefix de la colonne
     * @param isFirstWhereClause true ajoute 'AND ' au debut
     * @param isCaseIsensitive   true si clause 'ILIKE'
     * @return
     */
    protected StringBuilder testWhereClauseSearchQuery(String col, Boolean isNegative, Object value, String prefix, boolean isFirstWhereClause, boolean isCaseIsensitive) {
        if (value != null) {
            return testWhereClauseSearchQuery(col, isNegative, List.of(value), prefix, isFirstWhereClause, isCaseIsensitive);
        } else {
            return new StringBuilder();
        }
    }

    /**
     * pour creer les clauses WHERE de type LIKE, ILIKE, NOT LIKE, NOT ILIKE
     *
     * @param col                colonne de la base
     * @param isNegative         true si clause 'NOT'
     * @param values             valeurs recherchees possibles
     * @param prefix             prefix de la colonne
     * @param isFirstWhereClause true ajoute 'AND ' au debut
     * @param isCaseIsensitive   true si clause 'ILIKE'
     * @return
     */
    protected StringBuilder testWhereClauseSearchQuery(String col, Boolean isNegative, List<? extends Object> values, String prefix, boolean isFirstWhereClause,
                                                       boolean isCaseIsensitive) {
        StringBuilder query = new StringBuilder();
        if (!values.isEmpty()) {
            if (!isFirstWhereClause) {
                query.append("\nAND ");
            } else {
                query.append(" ");
            }
            boolean isFirst = true;
            for (Object value : values) {
                if (isFirst) {
                    query.append("(");
                    isFirst = false;
                } else {
                    query.append(" AND ");
                }
                if (prefix != null && !prefix.isEmpty()) {
                    query.append(prefix).append(".");
                }
                query.append(qf.addIdentifier(col));
                if (isNegative) {
                    query.append(" NOT");
                }
                if (isCaseIsensitive) {
                    query.append(" ILIKE");
                } else {
                    query.append(" LIKE");
                }
                query.append(" '%").append(value.toString().replaceAll("'", "''")).append("%'");
            }
            query.append(")");
        }
        return query;
    }

    /**
     * ouvre un statement sur la base et execute la query. A utliser en cas de requete COUNT uniquement
     * <br>voir {@link Statement#executeUpdate(String)}
     *
     * @param query
     * @return
     */
    protected int execCount(String query) {
        return execCount(query, ds);
    }

    /**
     * ouvre un statement et execute la query. A utliser en cas de requete COUNT uniquement
     * <br>voir {@link Statement#executeUpdate(String)}
     *
     * @param query
     * @return
     */
    protected int execCount(String query, DataSource dataSource) {
        LOGGER.debug(query);
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement(); ResultSet rs = stmtQuery.executeQuery(query)) {
            rs.next();
            return rs.getInt(1);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    /**
     * ouvre un statement sur la base et execute la query. A utiliser en cas de requete UPDATE
     * <br>voir {@link Statement#executeUpdate(String)}
     *
     * @param query
     * @return le nombre de lignes affectees
     */
    protected int execUpdate(String query) {
        return execUpdate(query, ds);
    }

    /**
     * ouvre un statement et execute la query. A utiliser en cas de requete UPDATE
     * <br>voir {@link Statement#executeUpdate(String)}
     *
     * @param query
     * @return le nombre de lignes affectees
     */
    protected int execUpdate(String query, DataSource dataSource) {
        LOGGER.debug(query);
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement()) {
            return stmtQuery.executeUpdate(query);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    /**
     * ouvre un statement sur la base et execute la query. A utiliser en cas de requete SELECT
     * <br>voir {@link Statement#executeQuery(String)}
     *
     * @param query
     * @param clazz classe model sur laquelle map le ResultSet
     * @return
     */
    protected <T> List<T> execQuery(String query, Class<T> clazz) {
        return execQuery(query, ds, clazz);
    }

    /**
     * ouvre un statement et execute la query. A utiliser en cas de requete SELECT
     * <br>voir {@link Statement#executeQuery(String)}
     *
     * @param query
     * @param clazz classe model sur laquelle map le ResultSet
     * @return
     */
    protected <T> List<T> execQuery(String query, DataSource dataSource, Class<T> clazz) {
        LOGGER.debug(query);
        ResultSet rs;
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement()) {
            rs = stmtQuery.executeQuery(query);
            return resultSetToObj(rs, clazz);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    protected void execQuery(String query) {
        execQuery(query, ds);
    }

    protected void execQuery(String query, DataSource dataSource) {
        LOGGER.debug(query);
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement()) {
            stmtQuery.execute(query);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    protected List<Object> execQueryToList(String query, DataSource dataSource) {
        LOGGER.debug(query);
        ResultSet rs;
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement()) {
            rs = stmtQuery.executeQuery(query);
            List<Object> ret = new ArrayList<>();
            while (rs.next()) {
                ret.add(rs.getObject(1));
            }
            return ret;
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    protected List<Map<String, Object>> execQueryRealtimeToGeoJson(String query) {
        return execQueryToGeoJson(query, ds);
    }

    protected List<Map<String, Object>> execQueryToGeoJson(String query, DataSource dataSource) {
        LOGGER.debug(query);
        ResultSet rs;
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement()) {
            rs = stmtQuery.executeQuery(query);
            return resultSetToGeoJson(rs);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

}
