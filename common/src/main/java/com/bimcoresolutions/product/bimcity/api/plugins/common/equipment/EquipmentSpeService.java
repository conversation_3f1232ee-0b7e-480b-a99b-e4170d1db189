package com.bimcoresolutions.product.bimcity.api.plugins.common.equipment;

import com.bimcoresolutions.util.base.filter.IntArrayFilter;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class EquipmentSpeService {
    private final EquipmentSpeDao dao;
    public EquipmentSpeService(EquipmentSpeDao groupDao) {
        this.dao = groupDao;
    }

    public  PaginatedResponse<Equipment> getEquipement(EquipmentCriteria criteria, Long limit, Long offset, List<Sort.Order> order, List<String> orderby, boolean isOr, Pair<Boolean, List<Integer>> userPerimeters) {
        IntArrayFilter perimetersFilter = new IntArrayFilter();
        if (userPerimeters.getLeft()) {
            criteria.set_perimeters(null);
        } else {
            perimetersFilter.setIn(new ArrayList<>(userPerimeters.getRight()));
            criteria.set_perimeters(perimetersFilter);
        }
        long count = dao.count(criteria,isOr);
        if (count < 1) {
            return new PaginatedResponse<>(List.of(), offset, offset, count);
        }
        List<Equipment> result = dao.listEquipement(criteria,limit,offset,order,orderby,isOr);
        return new PaginatedResponse<>(result, offset, offset + result.size(), count);
    }

    public PaginatedResponse<EquipementId> getEquipmentId(EquipmentCriteria criteria, Long limit, Long offset, List<Sort.Order> order, List<String> orderby,boolean isOr ,Pair<Boolean, List<Integer>> userPerimeters){
        IntArrayFilter perimetersFilter = new IntArrayFilter();
        if (userPerimeters.getLeft()) {
            criteria.set_perimeters(null);
        } else {
            perimetersFilter.setIn(new ArrayList<>(userPerimeters.getRight()));
            criteria.set_perimeters(perimetersFilter);
        }
        long count = dao.count(criteria,isOr);
        if (count < 1) {
            return new PaginatedResponse<>(List.of(), offset, offset, count);
        }
        List<EquipementId> result = dao.listEquipementId(criteria,limit,offset,order,orderby,isOr);
        return new PaginatedResponse<>(result, offset, offset + result.size(), count);
    }
}
