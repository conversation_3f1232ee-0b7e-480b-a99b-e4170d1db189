package com.bimcoresolutions.product.bimcity.api.plugins.common.equipment;

import com.bimcoresolutions.api.base.api.rest.BaseRest;
import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Log4j2
@RestController
@Api(tags = {"EquipmentSpeRest"})
@RequestMapping("/spe/common/equipment")
public class EquipmentSpeRest extends BaseRest {


    private final EquipmentUtil equipmentUtil;
    private final EquipmentSpeService service;
    private final CustomJwtAuthenticationConverter customJwtAuthenticationConverter;

    public EquipmentSpeRest(EquipmentUtil equipmentUtil, EquipmentSpeService equipmentSpeService, CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        this.equipmentUtil = equipmentUtil;
        this.service = equipmentSpeService;
        this.customJwtAuthenticationConverter = customJwtAuthenticationConverter;
    }

    @GetMapping(value = "", produces = "application/json")
    public ResponseEntity<PaginatedResponse<Equipment>> getEquipment(
            EquipmentCriteria criteria,
            @RequestParam(required = false) Long limit,
            @RequestParam(required = false, defaultValue = "0") Long offset,
            @RequestParam(required = false, defaultValue = "") List<String> orderBy,
            @RequestParam(required = false, defaultValue = "") List<Sort.Direction> order,
            @RequestParam(required = false, defaultValue = "false") boolean isOr
    ) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();
        log.info("UserPerimeters {}", userPerimeters);
        log.debug("{}", criteria);
        List<String> validOrderBys = new ArrayList<>(criteria.mapCriterias().keySet());
        List<Sort.Order> checkedOrderBys = checkOrderBy(orderBy, validOrderBys, order);
        PaginatedResponse<Equipment> ret = service.getEquipement(criteria, limit, offset, checkedOrderBys, orderBy, isOr,userPerimeters);
        return ResponseEntity.ok().body(ret);
    }

    @GetMapping(value = "/id", produces = "application/json")
    public ResponseEntity<PaginatedResponse<EquipementId>> getEquipmentId(
            EquipmentCriteria criteria,
            @RequestParam(required = false) Long limit,
            @RequestParam(required = false, defaultValue = "0") Long offset,
            @RequestParam(required = false, defaultValue = "") List<String> orderBy,
            @RequestParam(required = false, defaultValue = "") List<Sort.Direction> order,
            @RequestParam(required = false, defaultValue = "false") boolean isOr

    ) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();
        log.info("UserPerimeters {}", userPerimeters);
        log.debug("{}", criteria);
        List<String> validOrderBys = new ArrayList<>(criteria.mapCriterias().keySet());
        List<Sort.Order> checkedOrderBys = checkOrderBy(orderBy, validOrderBys, order);
        PaginatedResponse<EquipementId> ret = service.getEquipmentId(criteria, limit, offset, checkedOrderBys, orderBy, isOr,userPerimeters);
        return ResponseEntity.ok().body(ret);
    }

    @GetMapping(value = "/types", produces = "application/json")
    public Set<EquipmentClass> equipmentsInThisProject() {
        return equipmentUtil.getEquipmentClassesInThisProject();
    }

}
