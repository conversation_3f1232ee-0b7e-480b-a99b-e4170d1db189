package com.bimcoresolutions.product.bimcity.api.plugins.common.util;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.filter.PaginatedFilter;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import net.postgis.jdbc.PGgeometry;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;

import javax.sql.DataSource;
import java.sql.*;
import java.time.Instant;
import java.util.*;

import static com.bimcoresolutions.util.model.service.histo.HistoManager.SCHEMA_HISTO;
import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;

@AllArgsConstructor
public abstract class AbstractSpeDao<DTO, FILTER extends PaginatedFilter> {
    static final Logger LOGGER = LogManager.getLogger();
    final WKTReader wktReader = new WKTReader();
    final DataSource dataSource;

    protected abstract String getTemplateCount();

    protected abstract String getTemplateList();

    protected abstract DTO resultsetToObject(ResultSet rs);

    protected abstract String buildFilter(FILTER filter);

    protected abstract String orderBy(FILTER paginatedFilter);

    public int count(FILTER filter) {
        return count(filter, SCHEMA_RT);
    }

    public int countHisto(FILTER filter) {
        return count(filter, SCHEMA_HISTO);
    }

    public List<DTO> list(FILTER filter) {
        return list(filter, SCHEMA_RT);
    }

    public List<DTO> listHisto(FILTER filter) {
        return list(filter, SCHEMA_HISTO);
    }

    public String queryBddCount(FILTER filter, String schema) {

        Map<String, Object> valuesMap = Map.of(
                "filter", buildFilter(filter),
                "schema", schema
        );
        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        return sub.replace(getTemplateCount());
    }

    public String queryBddList(FILTER filter, String schema) {
        Map<String, Object> valuesMap = Map.of(
                "filter", buildFilter(filter),
                "paging", buildPaging(filter),
                "schema", schema
        );
        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        return sub.replace(getTemplateList());
    }

    public List<DTO> list(FILTER filter, String schema) {
        String sqlQuery = queryBddList(filter, schema);
        return exec(sqlQuery);
    }

    @SneakyThrows
    public int count(FILTER filter, String schema) {
        String sqlQuery = queryBddCount(filter, schema);
        return execCount(sqlQuery);
    }

    @SneakyThrows
    protected int execCount(String sqlQuery) {
        LOGGER.debug("{}", sqlQuery);
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement(); ResultSet rs = stmtQuery.executeQuery(sqlQuery)) {
            rs.next();
            return rs.getInt(1);
        }
    }

    public String buildPaging(FILTER paginatedFilter) {
        String res = "";
        if (paginatedFilter.getOrderby() != null && paginatedFilter.getOrder() != null) {
            res += " ORDER BY " + orderBy(paginatedFilter);
        }
        if (paginatedFilter.getOffset() != null) {
            res += " OFFSET " + paginatedFilter.getOffset();
        }
        if (paginatedFilter.getLimit() != null) {
            res += " LIMIT " + paginatedFilter.getLimit();
        }
        return res;
    }


    @SneakyThrows
    protected Geometry nullIfLocation(ResultSet resultSet, String label) {
        if (resultSet.getObject(label) != null) {
            Object object = resultSet.getObject(label);
            StringBuffer sb = new StringBuffer();
            ((PGgeometry) object).getGeometry().outerWKT(sb);
            Geometry geometryObject = wktReader.read(sb.toString());
            return geometryObject;
        }
        return null;
    }

    @SneakyThrows
    protected <T> T nullIfNull(ResultSet resultSet, String label, Class<T> clazz) {
        if (resultSet.getObject(label) != null) {
            return resultSet.getObject(label, clazz);
        }
        return null;
    }

    @SneakyThrows
    protected List<String> emptyIfNull(ResultSet resultSet, String label) {
        if (resultSet.getObject(label) != null) {
            Array a = resultSet.getArray(label);
            String[] nullable = (String[]) a.getArray();
            return Arrays.asList(nullable);
        }
        return List.of();
    }

    @SneakyThrows
    protected Instant nullIfNullDate(ResultSet resultSet, String label) {
        if (containColumn(resultSet, label) && resultSet.getTimestamp(label) != null) {
            return resultSet.getTimestamp(label).toInstant();
        }
        return null;
    }

    @SneakyThrows
    private boolean containColumn(ResultSet resultSet, String label) {
        ResultSetMetaData metaData = resultSet.getMetaData();
        for (int i = 1; i <= metaData.getColumnCount(); i++) {
            if (metaData.getColumnName(i).equalsIgnoreCase(label)) {
                return true;
            }
        }
        return false;
    }

    @SneakyThrows
    List<DTO> exec(String sqlQuery) {
        LOGGER.debug("{}", sqlQuery);
        List<DTO> result = new ArrayList<>();
        ResultSet rs;
        try (Connection connection = dataSource.getConnection(); Statement stmtQuery = connection.createStatement()) {
            rs = stmtQuery.executeQuery(sqlQuery);
            while (rs.next()) {
                result.add(resultsetToObject(rs));
            }
        }
        return result;
    }

    protected List<Integer> getIntegerList(ResultSet rs, String columnName) {
        try {
            java.sql.Array array = rs.getArray(columnName);
            if (array == null) return Collections.emptyList();
            Integer[] integers = (Integer[]) array.getArray();
            return Arrays.asList(integers);
        } catch (Exception e) {
            LOGGER.error("Cannot deserialize perimeters " , e);
            return Collections.emptyList();
        }
    }


}
