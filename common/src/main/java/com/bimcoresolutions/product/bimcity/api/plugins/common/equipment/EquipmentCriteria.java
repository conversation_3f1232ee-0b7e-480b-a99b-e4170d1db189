package com.bimcoresolutions.product.bimcity.api.plugins.common.equipment;

import com.bimcoresolutions.util.base.filter.*;
import lombok.*;
import org.springdoc.core.annotations.ParameterObject;

import java.io.Serializable;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_ID_BIMCORE;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PERIMETERS;

@AllArgsConstructor
@NoArgsConstructor
@Data
@ParameterObject
@Builder
public class EquipmentCriteria implements Serializable, BimCriterias {

    private StringFilter _id_bimcore;
    private IntArrayFilter _perimeters;
    private StringFilter code;
    private StringFilter name;
    private IntegerFilter operatingstate;
    private IntegerFilter status;
    private StringFilter site;

    @Override
    public String table() {
        return null;
    }

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    private Map<String, Filter> criterias;

    @Override
    public Map<String, Filter> mapCriterias() {
        if (criterias == null) {
            Map<String, Filter> map = new LinkedHashMap<>();
            map.put(DB_REALTIME_ID_BIMCORE, _id_bimcore);
            map.put(DB_REALTIME_PERIMETERS,_perimeters);
            map.put("code", code);
            map.put("name", name);
            map.put("operatingstate", operatingstate);
            map.put("site", site);
            map.put("status", status);
            criterias = Collections.unmodifiableMap(map);
        }
        return criterias;
    }

}
