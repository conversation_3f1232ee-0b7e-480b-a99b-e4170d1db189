package com.bimcoresolutions.product.bimcity.api.plugins.common.equipment;

import com.bimcoresolutions.util.base.filter.IntArrayFilter;
import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.product.bimcity.api.plugins.common.group.GroupUtil;
import com.bimcoresolutions.util.base.filter.SpecificationBuilder;
import com.bimcoresolutions.util.base.filter.StringFilter;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Sort;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;

import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.in;

public class EquipmentSpeDaoTest {


    private static QueryFormer qf;
    private static SpecificationBuilder specificationBuilder;
    private static EquipmentSpeDao equipmentSpeDao;

    @BeforeEach
    @SneakyThrows
    public void init() throws IOException {
        qf = new QueryFormer(EDatabaseType.POSTGRESQL, SCHEMA_RT);
        specificationBuilder = new SpecificationBuilder(qf);
        File modelFile = Path.of("./src/test/resources/group/EquipementTest_0_0_1.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("EquipementTest", new Semver("0.0.1"), FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8), new ConfigModel.Filter());
        EquipmentUtil equipmentUtil = new EquipmentUtil(model);
        GroupUtil groupUtil = new GroupUtil(model, equipmentUtil);
        equipmentSpeDao = new EquipmentSpeDao(null, qf, null, specificationBuilder, groupUtil, equipmentUtil, null);
    }

    @Test
    public void create_get_equipements() {

        List<String> orderby = List.of("ASC");
        List<Sort.Order> order = List.of(Sort.Order.asc("name"));
        StringFilter newString = StringFilter.builder().containsIgnoreCase("te").build();
        EquipmentCriteria equipementCriteria = EquipmentCriteria.builder().name(newString).site(newString).build();
        String result = equipmentSpeDao.queryEquipment(equipementCriteria, 500L, 0L, order, orderby, true).toString();

        String expected = """
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Cabinet' as "equipmentType" FROM "realtime"."elem_Cabinet"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Camera' as "equipmentType" FROM "realtime"."elem_Camera"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Controller' as "equipmentType" FROM "realtime"."elem_Controller"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'CountingStation' as "equipmentType" FROM "realtime"."elem_CountingStation"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'DynPanel' as "equipmentType" FROM "realtime"."elem_DynPanel"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'EnvironmentalSensor' as "equipmentType" FROM "realtime"."elem_EnvironmentalSensor"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Intersection' as "equipmentType" FROM "realtime"."elem_Intersection"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'LightingPoint' as "equipmentType" FROM "realtime"."elem_LightingPoint"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'TransformerStation' as "equipmentType" FROM "realtime"."elem_TransformerStation"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'RailCrossing' as "equipmentType" FROM "realtime"."elem_RailCrossing"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Railway' as "equipmentType" FROM "realtime"."elem_Railway"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'RiverStructure' as "equipmentType" FROM "realtime"."elem_RiverStructure"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' ORDER BY "name" ASC LIMIT 500 OFFSET 0;""";

        assertThat(expected).isEqualTo(result);
    }

    @Test
    public void create_get_equipementsID() {

        List<String> orderby = List.of("ASC");
        List<Sort.Order> order = List.of(Sort.Order.asc("name"));
        StringFilter newString = StringFilter.builder().containsIgnoreCase("te").build();
        EquipmentCriteria equipementCriteria = EquipmentCriteria.builder().name(newString).site(newString).build();
        String result = equipmentSpeDao.queryEquipmentId(equipementCriteria, 500L, 0L, order, orderby, true).toString();

        String expected = """
                SELECT "_id_bimcore","code","name",'Cabinet' as "equipmentType" FROM "realtime"."elem_Cabinet"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'Camera' as "equipmentType" FROM "realtime"."elem_Camera"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'Controller' as "equipmentType" FROM "realtime"."elem_Controller"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'CountingStation' as "equipmentType" FROM "realtime"."elem_CountingStation"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'DynPanel' as "equipmentType" FROM "realtime"."elem_DynPanel"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'EnvironmentalSensor' as "equipmentType" FROM "realtime"."elem_EnvironmentalSensor"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'Intersection' as "equipmentType" FROM "realtime"."elem_Intersection"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'LightingPoint' as "equipmentType" FROM "realtime"."elem_LightingPoint"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'TransformerStation' as "equipmentType" FROM "realtime"."elem_TransformerStation"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'RailCrossing' as "equipmentType" FROM "realtime"."elem_RailCrossing"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'Railway' as "equipmentType" FROM "realtime"."elem_Railway"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name",'RiverStructure' as "equipmentType" FROM "realtime"."elem_RiverStructure"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' ORDER BY "name" ASC LIMIT 500 OFFSET 0;""";

        assertThat(expected).isEqualTo(result);
    }

    @Test
    public void create_query_count() {

        StringFilter newString = StringFilter.builder().containsIgnoreCase("te").build();
        EquipmentCriteria equipementCriteria = EquipmentCriteria.builder().name(newString).site(newString).build();
        String result = equipmentSpeDao.queryCount(equipementCriteria, true).toString();

        String expected = """
                SELECT SUM(count) FROM(SELECT COUNT("_id_bimcore") FROM"realtime"."elem_Cabinet"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_Camera"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_Controller"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_CountingStation"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_DynPanel"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_EnvironmentalSensor"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_Intersection"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_LightingPoint"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_TransformerStation"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_RailCrossing"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_Railway"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT COUNT("_id_bimcore") FROM"realtime"."elem_RiverStructure"
                WHERE "name" ilike '%te%'
                 OR "site" ilike '%te%') AS nbrObject;""";

        assertThat(expected).isEqualTo(result);
    }

    @Test
    public void create_get_equipementsPerimeters() {

        List<String> orderby = List.of("ASC");
        List<Sort.Order> order = List.of(Sort.Order.asc("name"));
        StringFilter newString = StringFilter.builder().containsIgnoreCase("te").build();
        EquipmentCriteria equipementCriteria = EquipmentCriteria.builder().name(newString).site(newString).build();
        IntArrayFilter intArrayFilter = new IntArrayFilter();
        intArrayFilter.setIn(List.of(1,2,3));
        equipementCriteria.set_perimeters(intArrayFilter);
        String result = equipmentSpeDao.queryEquipment(equipementCriteria, 500L, 0L, order, orderby, true).toString();

        String expected = """
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Cabinet' as "equipmentType" FROM "realtime"."elem_Cabinet"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Camera' as "equipmentType" FROM "realtime"."elem_Camera"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Controller' as "equipmentType" FROM "realtime"."elem_Controller"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'CountingStation' as "equipmentType" FROM "realtime"."elem_CountingStation"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'DynPanel' as "equipmentType" FROM "realtime"."elem_DynPanel"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'EnvironmentalSensor' as "equipmentType" FROM "realtime"."elem_EnvironmentalSensor"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Intersection' as "equipmentType" FROM "realtime"."elem_Intersection"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'LightingPoint' as "equipmentType" FROM "realtime"."elem_LightingPoint"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'TransformerStation' as "equipmentType" FROM "realtime"."elem_TransformerStation"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'RailCrossing' as "equipmentType" FROM "realtime"."elem_RailCrossing"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'Railway' as "equipmentType" FROM "realtime"."elem_Railway"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' UNION
                SELECT "_id_bimcore","code","name","operatingstate","status","site","status","location",'RiverStructure' as "equipmentType" FROM "realtime"."elem_RiverStructure"
                WHERE "_perimeters"@>ARRAY[1]::integer[] OR "_perimeters"@>ARRAY[2]::integer[] OR "_perimeters"@>ARRAY[3]::integer[]
                 OR "name" ilike '%te%'
                 OR "site" ilike '%te%' ORDER BY "name" ASC LIMIT 500 OFFSET 0;""";

        assertThat(expected).isEqualTo(result);
    }


}
