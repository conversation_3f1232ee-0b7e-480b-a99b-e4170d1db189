package com.bimcoresolutions.product.bimcity.api.plugins.common.equipment;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.api.base.spring.ErrorHandler;
import com.bimcoresolutions.util.base.filter.StringFilter;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;


@ExtendWith(MockitoExtension.class)
public class EquipementSpeRestTest {

    private static EquipmentSpeService equipmentSpeService;
    private static CustomJwtAuthenticationConverter customJwtDecoder;
    static BimCoreModel bimCoreModel = new BimCoreModel("group/EquipementTest_0_0_1.bimcore");
    private static final EquipmentUtil equipmentUtil = new EquipmentUtil(bimCoreModel);
    private static MockMvc mockMvc;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        equipmentSpeService = Mockito.mock(EquipmentSpeService.class);
        customJwtDecoder = Mockito.mock(CustomJwtAuthenticationConverter.class);

        ThreadLocal<Pair<Boolean, List<Integer>>> mockThreadLocal = new ThreadLocal<>();
        mockThreadLocal.set(Pair.of(false, List.of(1, 2, 3)));

        Mockito.when(customJwtDecoder.get_perimeters()).thenReturn(mockThreadLocal);
        EquipmentSpeRest equipmentSpeRest = new EquipmentSpeRest(equipmentUtil, equipmentSpeService, customJwtDecoder);
        mockMvc = MockMvcBuilders.standaloneSetup(equipmentSpeRest).setControllerAdvice(new ErrorHandler()).build();
    }

    @Test
    void get_equipment_attribut_succes() throws Exception {
        List<String> orderby = List.of("ASC");
        List<Sort.Order> order = List.of(Sort.Order.asc("name"));
        StringFilter newString = StringFilter.builder().containsIgnoreCase("test0").build();
        EquipmentCriteria equipementCriteria = EquipmentCriteria.builder().name(newString).site(newString).build();
        PaginatedResponse<Equipment> equipmentList = new PaginatedResponse<>(List.of(), 500L, 500L, 100L);
        Pair<Boolean, List<Integer>> expectedUserPerimeters = Pair.of(false, List.of(1, 2, 3));
        given(equipmentSpeService.getEquipement(equipementCriteria, 500L, 0L, order, orderby, false, expectedUserPerimeters)).willReturn(equipmentList);
        MockHttpServletResponse response = mockMvc.perform(get("/spe/common/equipment/").contentType(APPLICATION_JSON)).andReturn().getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

    @Test
    void get_equipment_id_succes() throws Exception {
        List<String> orderby = List.of("ASC");
        List<Sort.Order> order = List.of(Sort.Order.asc("name"));
        StringFilter newString = StringFilter.builder().containsIgnoreCase("test1").build();
        EquipmentCriteria equipementCriteria = EquipmentCriteria.builder().name(newString).site(newString).build();
        PaginatedResponse<EquipementId> equipmentIdList = new PaginatedResponse<>(List.of(), 0L, 500L, 100L);
        // Update the mock to expect the userPerimeters parameter and use the correct method
        Pair<Boolean, List<Integer>> expectedUserPerimeters = Pair.of(false, List.of(1, 2, 3));
        given(equipmentSpeService.getEquipmentId(equipementCriteria, 500L, 0L, order, orderby, false, expectedUserPerimeters)).willReturn(equipmentIdList);
        MockHttpServletResponse response = mockMvc.perform(get("/spe/common/equipment/id").contentType(APPLICATION_JSON)).andReturn().getResponse();
        assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
    }

}