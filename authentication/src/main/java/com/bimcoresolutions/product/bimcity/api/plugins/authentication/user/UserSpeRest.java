package com.bimcoresolutions.product.bimcity.api.plugins.authentication.user;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.CustomAutowireConfigurer;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;


@RestController
@Api(tags = {"UserSpeRest"})
@RequestMapping("/spe/authentication/user")
public class UserSpeRest {

    private final UserSpeService userSpeService;

    public UserSpeRest(UserSpeService userSpeService) {
        this.userSpeService = userSpeService;
    }

    @GetMapping(value = "/search", produces = "application/json")
    @ApiOperation(value = "usersearch", httpMethod = "GET", notes = "obtenir la liste des users")
    @Secured({"ROLE_admin", "ROLE_User_manage"})
    public EnveloppeGetter<UserDTO> userlist(UserFilter userFilter) {
        return userSpeService.list(userFilter);
    }
}
