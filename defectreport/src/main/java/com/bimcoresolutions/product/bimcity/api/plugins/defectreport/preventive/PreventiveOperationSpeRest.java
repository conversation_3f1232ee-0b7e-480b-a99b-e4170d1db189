package com.bimcoresolutions.product.bimcity.api.plugins.defectreport.preventive;

import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.product.bimcity.api.plugins.defectreport.curative.DefectReportDTO;
import com.bimcoresolutions.product.bimcity.api.plugins.defectreport.curative.DefectReportSpeDao;
import com.bimcoresolutions.product.bimcity.api.plugins.defectreport.curative.DefectReportSpeService;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@Api(tags = { "PreventiveOperationSpeRest" })
@RequestMapping("/spe/defectreport/preventiveoperation")
public class PreventiveOperationSpeRest {

    private final PreventiveOperationSpeService preventiveOperationSpeService;

    private final PreventiveOperationSpeDao preventiveOperationSpeDao;

    public PreventiveOperationSpeRest(PreventiveOperationSpeService preventiveOperationSpeService, PreventiveOperationSpeDao preventiveOperationSpeDao) {
        this.preventiveOperationSpeService = preventiveOperationSpeService;
        this.preventiveOperationSpeDao = preventiveOperationSpeDao;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @ApiOperation(value = "preventiveOperationlist", nickname = "preventivelist", httpMethod = "GET", notes = "obtenir la liste des opérations préventives")
    @Secured({ "ROLE_admin", "ROLE_PreventiveOperation_read" })
    public EnveloppeGetter<PreventiveOperationDTO> preventiveOperationlist(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
           @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "_last_update_date") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("equipmentType") @RequestParam(required = false) Set<String> equipmentType,
            @ModelAttribute("defaulttype") @RequestParam(required = false) String defaulttype,
            @ModelAttribute("equipmentName") @RequestParam(required = false) String equipmentName,
            //@ModelAttribute("source") @RequestParam(required = false) String source,
           @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end,
            @ModelAttribute("status") @RequestParam(required = false) String status
            ) {
        Set<EquipmentClass> eqs;
        if (CollectionUtils.isEmpty(equipmentType)) {
            eqs = preventiveOperationSpeDao.getEquipmentClassesInThisProject();
        } else {
            try {
                eqs = equipmentType.stream().map(EquipmentClass::valueOf).collect(Collectors.toCollection(HashSet::new));
            } catch (IllegalArgumentException ex) {
                throw new WrongArgumentValueException("Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + preventiveOperationSpeDao.getEquipmentClassesInThisProject() + ")");
            }
            if (!preventiveOperationSpeDao.getEquipmentClassesInThisProject().containsAll(eqs)) {
                throw new WrongArgumentValueException(
                        "Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + preventiveOperationSpeDao.getEquipmentClassesInThisProject() + ")");
            }
        }
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("_last_update_date", "name", "source", "equipmentName").contains(orderby)) {
            throw new WrongArgumentValueException("Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : lastupdate,name,source,equipmentName)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        return preventiveOperationSpeService.preventiveOperationList(limit, offset, orderby, order, eqs, defaulttype, equipmentName, start, end, status);
    }

}
