package com.bimcoresolutions.product.bimcity.api.plugins.defectreport.intervention;

import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ASpeDao;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelRelation;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatAndSimpleQuote;

@Repository
@Log4j2
public class InterventionSpeDao extends ASpeDao {
    public static final String INTERVENTION = "Intervention";
    public static final String INTERVENTION_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + INTERVENTION;
    private final BimCoreModel model;
    private final Set<EquipmentClass> equipmentClassesInThisProject = new HashSet<>();
    private final Map<String, BimCoreModelRelation> targetRelName = new HashMap<>();

    public InterventionSpeDao(@Qualifier("dsRealtime") DataSource dataSource, @Qualifier("qfRealtime") QueryFormer qf, ObjectMapper om, FunctionalModelService functionalModelService) {
        super(dataSource, qf, om);
        this.model = functionalModelService.getModel();
        for (BimCoreModelRelation bcr : model.getElementsBimCore().get(INTERVENTION).getRelations().values()) {
            String target = INTERVENTION.equals(bcr.getElementOrigin().getName()) ? bcr.getElementDestination().getName() : bcr.getElementOrigin().getName();
            if (EquipmentClass.getNames().contains(target)) {
                equipmentClassesInThisProject.add(EquipmentClass.valueOf(target));
                if (bcr.isDirect()) {
                    targetRelName.put(target, bcr);
                } else {
                    targetRelName.put(target, bcr.getReverseRelation());
                }
            }
        }
    }

    public Set<EquipmentClass> getEquipmentClassesInThisProject() {
        return equipmentClassesInThisProject;
    }

    public List<InterventionDTO> list(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses, String defaultType,
                                      String equipmentName, Instant start, Instant end, String status) {
        StringBuilder sql = queryList(limit, offset, orderBy, order, equipmentClasses, defaultType, equipmentName, start, end, status);
        try {
            return execQuery(sql.toString(), InterventionDTO.class);
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public int count(Set<EquipmentClass> equipmentClasses, String defaultType, String equipmentName, Instant start, Instant end, String status) {
        StringBuilder sql = queryCount(equipmentClasses, defaultType, equipmentName, start, end, status);
        try {
            return execCount(sql.toString());
        } catch (TechnicalException e) {
            log.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryList(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, Set<EquipmentClass> equipmentClasses,
                                   String defaultType, String equipmentName, Instant start, Instant end, String status) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT");
        sql.append(" a._id_bimcore,");
        sql.append(" a._last_update_date,");

        sql.append(equipmentSelectNameAndType(equipmentClasses));

        sql.append("\nFROM " + qf.addSchema(INTERVENTION_TABLE) + " AS a");

        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));
        sql.append(whereClauses(equipmentClasses, defaultType, equipmentName, start, end, status));
        sql.append("\nORDER BY " + toColumn(orderBy) + " " + order);
        sql.append("\nLIMIT " + limit + " OFFSET " + offset + ";");
        return sql;
    }

    private StringBuilder equipmentSelectNameAndType(Set<EquipmentClass> equipmentClasses) {
        StringBuilder query = new StringBuilder();
        query.append("\n COALESCE(");
        query.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ".name").collect(Collectors.joining(", ")));
        query.append(") as " + qf.addIdentifier("equipmentName") + ",");
        query.append("\n COALESCE(");
        query.append(equipmentClasses.stream().map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ".code").collect(Collectors.joining(", ")));
        query.append(") as " + qf.addIdentifier("equipmentCode") + ",");
        query.append("\n CASE");
        query.append(equipmentClasses.stream().map(e -> "  WHEN " + qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + "." + qf.addIdentifier(DB_REALTIME_ID_BIMCORE) + " is not null THEN " + betweenSimpleQuote(e.name()))
                .collect(Collectors.joining("\n", "\n", "\n  ELSE null\n END\n as " + qf.addIdentifier("equipmentType"))));
        return query;
    }

    private static final String JOIN_TEMPLATE = """
            LEFT JOIN ${rel_table} ON a.${_id_bimcore} = ${rel_table}.${col_intervention}
            LEFT JOIN ${equipment_table} ON ${rel_table}.${col_equipment} = ${equipment_table}.${_id_bimcore}
            """;

    private String equipmentJoins(EquipmentClass equipmentClass) {
        Map<String, String> valuesMap = new HashMap<>();
        valuesMap.put("equipment_table", qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + equipmentClass.name()));
        valuesMap.put("rel_table", qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + targetRelName.get(equipmentClass.name()).getRelationTableName()));

        if (INTERVENTION.equals(targetRelName.get(equipmentClass.name()).getElementOrigin().getName())) {
            valuesMap.put("col_intervention", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
            valuesMap.put("col_equipment", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));
        } else {
            valuesMap.put("col_intervention", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION));
            valuesMap.put("col_equipment", qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN));
        }
        valuesMap.put("_id_bimcore", qf.addIdentifier(DB_REALTIME_ID_BIMCORE));
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        return sub.replace(JOIN_TEMPLATE);
    }

    public StringBuilder queryCount(Set<EquipmentClass> equipmentClasses, String defaultType, String equipmentName, Instant start, Instant end, String status) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append("\nFROM " + qf.addSchema(INTERVENTION_TABLE) + " AS a");
        sql.append(equipmentClasses.stream().map(this::equipmentJoins).collect(Collectors.joining("", "\n", "")));
        sql.append(whereClauses(equipmentClasses, defaultType, equipmentName, start, end, status));
        sql.append(";");
        return sql;
    }

    public StringBuilder whereClauses(Set<EquipmentClass> equipmentClasses, String defaultType, String equipmentName, Instant start, Instant end, String status) {
        StringBuilder sql = new StringBuilder("WHERE 1=1 ");

        if (defaultType != null) {
            sql.append(" AND a.name ILIKE ")
                    .append(qf.formatAndSimpleQuote("%" + defaultType + "%"));
        }

        if (equipmentName != null && equipmentClasses != null && !equipmentClasses.isEmpty()) {
            sql.append(" AND (");
            sql.append(equipmentClasses.stream()
                    .map(e -> qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + e.name()) + ".name ILIKE " + formatAndSimpleQuote("%" + equipmentName + "%"))
                    .collect(Collectors.joining("\n  OR ")));
            sql.append(")");
        }

        if (status != null) {
            sql.append(" AND a.status = ")
               .append(formatAndSimpleQuote(status));
        }

        if (start != null) {
            sql.append(" AND a._last_update_date > '")
                    .append(start)
                    .append("'");
        }

        if (end != null) {
            sql.append(" AND a._last_update_date < '")
                    .append(end)
                    .append("'");
        }

        return sql;

    }

    private String toColumn(String orderBy) {
        if (orderBy.equals("equipmentName")) {
            return qf.addIdentifier(orderBy);
        } else {
            return "a." + qf.addIdentifier(orderBy);
        }
    }

    public List<String> sources() {
        String sql = sourcesQuery();
        try {
            return execQueryToList(sql, ds).stream().map(Object::toString).toList();
        } catch (TechnicalException e) {
            log.error("{}", e.getMessage());
            throw new TechnicalException(e);
        }
    }

    String sourcesQuery() {
        StringBuilder query = new StringBuilder("SELECT DISTINCT(").append(qf.addIdentifier("source"));
        query.append(") FROM ").append(qf.addSchema(INTERVENTION_TABLE));
        query.append(" WHERE ").append(qf.addIdentifier("source")).append(" IS NOT NULL");
        query.append(" ORDER BY ").append(qf.addIdentifier("source")).append(";");

        return query.toString();
    }
}



