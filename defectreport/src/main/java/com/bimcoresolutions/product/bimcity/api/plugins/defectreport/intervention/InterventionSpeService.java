package com.bimcoresolutions.product.bimcity.api.plugins.defectreport.intervention;


import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Service
public class InterventionSpeService {
    private final InterventionSpeDao interventionSpeDao;

    public InterventionSpeService(InterventionSpeDao interventionSpeDao) {
        this.interventionSpeDao = interventionSpeDao;
    }

    public EnveloppeGetter<InterventionDTO> interventionList(long limit, long offset, String orderBy,String order, Set<EquipmentClass> equipmentClasses, String defaultType,
                                                             String equipementName, Instant start, Instant end, String status) {
        List<InterventionDTO> preventiveOperationDTOList = interventionSpeDao.list(limit, offset, orderBy, order, equipmentClasses, defaultType, equipementName,start, end, status);
        if (preventiveOperationDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        EnveloppeGetter<InterventionDTO> toRet = new EnveloppeGetter<>();
        toRet.getItems().addAll(preventiveOperationDTOList);
        int total = interventionSpeDao.count(equipmentClasses, defaultType, equipementName, start, end, status);
        toRet.getPagination().setTotal((long) total);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + preventiveOperationDTOList.size());
        return toRet;
    }
}




