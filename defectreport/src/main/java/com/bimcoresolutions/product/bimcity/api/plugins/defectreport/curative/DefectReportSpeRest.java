package com.bimcoresolutions.product.bimcity.api.plugins.defectreport.curative;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@Api(tags = { "DefectReportSpeRest" })
@RequestMapping("/spe/defectreport/defectreport")
public class DefectReportSpeRest extends JwtDecoder {

    private final DefectReportSpeService defectReportSpeService;

    private final DefectReportSpeDao defectReportSpeDao;

    public DefectReportSpeRest(DefectReportSpeService defectReportSpeService,  DefectReportSpeDao defectReportSpeDao, CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.defectReportSpeService = defectReportSpeService;
        this.defectReportSpeDao = defectReportSpeDao;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @ApiOperation(value = "defectReportlist", nickname = "defectlist", httpMethod = "GET", notes = "obtenir la liste des signalements")
    @Secured({ "ROLE_admin", "ROLE_DefectReport_read" })
    public EnveloppeGetter<DefectReportDTO> defectReportlist(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "_last_update_date") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("equipmentType") @RequestParam(required = false) Set<String> equipmentType,
            @ModelAttribute("defaulttype") @RequestParam(required = false) String defaulttype,
            @ModelAttribute("equipmentName") @RequestParam(required = false) String equipmentName,
            @ModelAttribute("source") @RequestParam(required = false) String source,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end,
            @ModelAttribute("status") @RequestParam(required = false) String status) {
        Set<EquipmentClass> eqs;
        if (CollectionUtils.isEmpty(equipmentType)) {
            eqs = defectReportSpeDao.getEquipmentClassesInThisProject();
        } else {
            try {
                eqs = equipmentType.stream().map(EquipmentClass::valueOf).collect(Collectors.toCollection(HashSet::new));
            } catch (IllegalArgumentException ex) {
                throw new WrongArgumentValueException("Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + defectReportSpeDao.getEquipmentClassesInThisProject() + ")");
            }
            if (!defectReportSpeDao.getEquipmentClassesInThisProject().containsAll(eqs)) {
                throw new WrongArgumentValueException(
                        "Wrong 'equipmentType' parameter given : '" + equipmentType + "'. (Known : " + defectReportSpeDao.getEquipmentClassesInThisProject() + ")");
            }
        }
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("_last_update_date", "name", "source", "equipmentName").contains(orderby)) {
            throw new WrongArgumentValueException("Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : lastupdate,name,source,equipmentName)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        Pair<Boolean, List<Integer>> userPerimeters =  customJwtAuthenticationConverter.get_perimeters().get();
        return defectReportSpeService.defectReportList(limit, offset, orderby, order, eqs, defaulttype, equipmentName, source, start, end, status,userPerimeters);
    }

}
