package com.bimcoresolutions.product.bimcity.api.plugins.defectreport.curative;


import java.time.Instant;
import java.util.List;
import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.Set;

@Service
public class DefectReportSpeService {
    private final DefectReportSpeDao defectReportSpeDao;

    public DefectReportSpeService(DefectReportSpeDao defectReportSpeDao) {
        this.defectReportSpeDao = defectReportSpeDao;
    }

    public EnveloppeGetter<DefectReportDTO> defectReportList(long limit, long offset, String orderBy, String order, Set<EquipmentClass> equipmentClasses, String defaultType,
                                                             String equipementName, String source, Instant start, Instant end, String status, Pair<Boolean, List<Integer>> userPerimeters) {
        List<DefectReportDTO> defectReportDTOList = defectReportSpeDao.list(limit, offset, orderBy, order, equipmentClasses, defaultType, equipementName, source, start, end, status,userPerimeters);
        if (defectReportDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        EnveloppeGetter<DefectReportDTO> toRet = new EnveloppeGetter<>();
        toRet.getItems().addAll(defectReportDTOList);
        int total = defectReportSpeDao.count(equipmentClasses, defaultType, equipementName, source, start, end, status, userPerimeters);
        toRet.getPagination().setTotal((long) total);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + defectReportDTOList.size());
        return toRet;
    }
}




