package com.bimcoresolutions.product.bimcity.api.plugins.defectreport.preventive;


import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.equipment.EquipmentClass;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Set;

@Service
public class PreventiveOperationSpeService {
    private final PreventiveOperationSpeDao preventiveOperationSpeDao;

    public PreventiveOperationSpeService(PreventiveOperationSpeDao preventiveOperationSpeDao) {
        this.preventiveOperationSpeDao = preventiveOperationSpeDao;
    }

    public EnveloppeGetter<PreventiveOperationDTO> preventiveOperationList(long limit, long offset, String orderBy, String order, Set<EquipmentClass> equipmentClasses, String defaultType,
                                                                           String equipementName, Instant start, Instant end, String status) {
        List<PreventiveOperationDTO> preventiveOperationDTOList = preventiveOperationSpeDao.list(limit, offset, orderBy, order, equipmentClasses, defaultType, equipementName, start, end, status);
        if (preventiveOperationDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        EnveloppeGetter<PreventiveOperationDTO> toRet = new EnveloppeGetter<>();
        toRet.getItems().addAll(preventiveOperationDTOList);
        int total = preventiveOperationSpeDao.count(equipmentClasses, defaultType, equipementName, start, end, status);
        toRet.getPagination().setTotal((long) total);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + preventiveOperationDTOList.size());
        return toRet;
    }
}




