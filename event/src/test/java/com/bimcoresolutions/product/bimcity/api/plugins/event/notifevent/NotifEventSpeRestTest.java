package com.bimcoresolutions.product.bimcity.api.plugins.event.notifevent;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.api.base.spring.ErrorHandler;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(MockitoExtension.class)
public class NotifEventSpeRestTest {
    private static final Semver VERSION_MODEL = new Semver("0.3.1");
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);
    private static MockMvc mockMvc;
    private static NotifEventSpeService notifEventSpeService;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/notifEvent_0_3_1.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("Rouen", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        notifEventSpeService = Mockito.mock(NotifEventSpeService.class);
        CustomJwtAuthenticationConverter jwtDecoder = Mockito.mock(CustomJwtAuthenticationConverter.class);
        ThreadLocal<Pair<Boolean, List<Integer>>> mockThreadLocal = new ThreadLocal<>();
        mockThreadLocal.set(Pair.of(true, List.of(1,2,3)));
        when(jwtDecoder.get_perimeters()).thenReturn(mockThreadLocal);

        NotifEventSpeRest notifEventSpeRest = new NotifEventSpeRest(notifEventSpeService,jwtDecoder);
        mockMvc = MockMvcBuilders.standaloneSetup(notifEventSpeRest).setControllerAdvice(new ErrorHandler()).build();
    }


    @Nested
    class RealTime {

        private final String ENDPOINT_REALTIME = "list";
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));


        @Test
        void get_notifEventList_success() throws Exception {
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(notifEventSpeService.notifEventList(10L, 0L, "lastupdate", "desc", null, null, null, null, null, null,userPerimeters))
                    .willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(
                            get("/spe/event/notifevent/" + ENDPOINT_REALTIME)
                                    .contentType(APPLICATION_JSON))
                    .andReturn().getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(notifEventSpeService).notifEventList(10L, 0L, "lastupdate", "desc", null, null, null, null, null, null,userPerimeters);
        }


        @Test
        void notifEventList_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void notifEventList_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void notifEventList_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "orderby=random")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'orderby' parameter given : 'random'. (Possible values : name,source,category,apparitiondate,status,lastupdate)");
        }

        @Test
        void notifEventList_null_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "orderby=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'orderby' parameter given : 'null'. (Possible values : name,source,category,apparitiondate,status,lastupdate)");
        }

        @Test
        void notifEventList_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "order=random")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'random'. (Possible values : asc/desc)");
        }

        @Test
        void notifEventList_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void notifEventList_null_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=null")).isInstanceOf(WrongArgumentValueException.class).hasMessageContaining(
                    "Wrong value for field 'status' given : 'null'. (Possible values : " + ENotifEventStatus.nameValues() + ")");
        }

        @Test
        void notifEventList_wrong_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining(
                            "Wrong value for field 'status' given : 'randomFalseValue'. (Possible values : " + ENotifEventStatus.nameValues() + ")");
        }
    }

    Exception get_with_paramException(String endpoint, String param) throws Exception {
        return mockMvc.perform(get("/spe/event/notifevent/" + endpoint + "?" + param).contentType(MediaType.APPLICATION_JSON))
                .andReturn()
                .getResolvedException();
    }

}
