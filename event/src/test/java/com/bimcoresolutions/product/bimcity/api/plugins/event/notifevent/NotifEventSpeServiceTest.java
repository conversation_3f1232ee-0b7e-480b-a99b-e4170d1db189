package com.bimcoresolutions.product.bimcity.api.plugins.event.notifevent;

import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotifEventSpeServiceTest {

    @InjectMocks
    NotifEventSpeService notifEventSpeService;

    @Mock
    NotifEventSpeDao notifEventSpeDao;

    @Test
    void notifEventList_success() {
        //given
        NotifEventDTO model = new NotifEventDTO();
        model.setStatus(ENotifEventStatus.ToApprove.toString());
        List<NotifEventDTO> models = List.of(model);
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        EnveloppeGetter expected = new EnveloppeGetter();
        EnveloppeGetter.Pagination pagination = new EnveloppeGetter.Pagination();
        pagination.setDebut(0L);
        pagination.setFin(1L);
        pagination.setTotal(1L);

        expected.setPagination(pagination);
        expected.getItems().addAll(models);

        //mock
        when(notifEventSpeDao.notifEventBddList(10L, 0L, "apparitiondate", "desc", ENotifEventStatus.ToApprove, null, null, null, null, null,userPerimeters))
                .thenReturn(models);
        when(notifEventSpeDao.count(null, null, null, null, null, ENotifEventStatus.ToApprove,userPerimeters))
                .thenReturn(1);

        //when
        EnveloppeGetter result = notifEventSpeService.notifEventList(10L, 0L, "apparitiondate", "desc", ENotifEventStatus.ToApprove, null, null, null, null, null,userPerimeters);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

}