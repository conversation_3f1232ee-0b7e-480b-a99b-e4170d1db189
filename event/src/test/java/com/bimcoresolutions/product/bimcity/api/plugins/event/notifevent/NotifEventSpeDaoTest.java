package com.bimcoresolutions.product.bimcity.api.plugins.event.notifevent;

import com.bimcoresolutions.util.model.conf.ConfigModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vdurmont.semver4j.Semver;
import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.sql.DataSource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;

import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static org.assertj.core.api.Assertions.assertThat;

public class NotifEventSpeDaoTest {
    public static final QueryFormer REALTIME = new QueryFormer(POSTGRESQL, SCHEMA_RT);
    public static final ObjectMapper om = new ObjectMapper().findAndRegisterModules();
    private static final Semver VERSION_MODEL = new Semver("0.3.1");
    private static final FunctionalModelService FUNCTIONAL_MODEL_SERVICE = Mockito.mock(FunctionalModelService.class);
    private static NotifEventSpeDao notifEventSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        File modelFile = Path.of("./src/test/resources/notifEvent_0_3_1.bimcore").toFile();
        BimCoreModel model = FunctionalModelService.parseModel("Rouen", VERSION_MODEL, FileUtils.readFileToString(modelFile, StandardCharsets.UTF_8),
                new ConfigModel.Filter());
        model.validate();
        Mockito.doReturn(model).when(FUNCTIONAL_MODEL_SERVICE).getModel();
        notifEventSpeDao = new NotifEventSpeDao(Mockito.mock(DataSource.class), REALTIME, om);
    }

    @Nested
    class RealTime {

        @Test
        void queryList_status_toapprove() {
            Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
            String expected = """
                    SELECT _id_bimcore, apparitiondate, status, source, category, name, 
                    _perimeters FROM "realtime"."elem_NotifEvent" 
                    AS a WHERE 1=1 AND a.status = 'ToApprove' 
                    ORDER BY a._last_update_date desc LIMIT 10 OFFSET 0
                    """;
            StringBuilder result = notifEventSpeDao.queryBddList(10L, 0L, "_last_update_date", "desc", ENotifEventStatus.ToApprove, null, null, null, null, null,userPerimeters);
            assertThat(result.toString()).isEqualTo(StringUtils.normalizeSpace(expected));
        }

    }
}