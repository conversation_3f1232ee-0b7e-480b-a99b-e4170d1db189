package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EventSpeServiceTest {

    @InjectMocks
    EventSpeService eventSpeService;

    @Mock
    EventSpeDao eventSpeDao;
    Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

    @Test
    void eventList_success() {
        //given
        EventRealtimeDTO model = new EventRealtimeDTO();
        model.set_id_bimcore("randomId");
        List<EventRealtimeDTO> models = List.of(model);


        EnveloppeGetter expected = new EnveloppeGetter();
        EnveloppeGetter.Pagination pagination = new EnveloppeGetter.Pagination();
        pagination.setDebut(0L);
        pagination.setFin(1L);
        pagination.setTotal(1L);

        expected.setPagination(pagination);
        expected.getItems().addAll(models);

        //mock
        when(eventSpeDao.listRealtime(10L, 0L, EventRealtimeOrderby.start, "desc", EventRealtimeStatus.ToStart, null, null, null, null,userPerimeters))
                .thenReturn(models);
        when(eventSpeDao.countRealtime(EventRealtimeStatus.ToStart, null, null, null, null,userPerimeters))
                .thenReturn(1);

        //when
        EnveloppeGetter<EventRealtimeDTO> result = eventSpeService.listRealtime(10L, 0L, EventRealtimeOrderby.start, "desc", EventRealtimeStatus.ToStart, null, null, null, null,userPerimeters);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void eventListHisto_success() {
        //given
        EventHistoDTO model = new EventHistoDTO();
        model.set_id_bimcore("randomId");
        List<EventHistoDTO> models = List.of(model);

        EnveloppeGetter expected = new EnveloppeGetter();
        EnveloppeGetter.Pagination pagination = new EnveloppeGetter.Pagination();
        pagination.setDebut(0L);
        pagination.setFin(1L);
        pagination.setTotal(1L);

        expected.setPagination(pagination);
        expected.getItems().addAll(models);

        //mock
        when(eventSpeDao.listHisto(10L, 0L, EventHistoOrderby.start, "desc", EventHistoStatus.InProgress, null, null, null, null, null,userPerimeters))
                .thenReturn(models);
        when(eventSpeDao.countHisto(EventHistoStatus.InProgress, null, null, null, null, null,userPerimeters))
                .thenReturn(1);

        //when
        EnveloppeGetter<EventHistoDTO> result = eventSpeService.listHisto(10L, 0L, EventHistoOrderby.start, "desc", EventHistoStatus.InProgress, null, null, null, null, null,userPerimeters);

        //then
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

}