package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import javax.sql.DataSource;
import java.time.Instant;
import java.util.List;

import static com.bimcoresolutions.util.model.service.histo.HistoManager.SCHEMA_HISTO;
import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;
import static com.bimcoresolutions.product.bimcity.api.plugins.event.event.EventSpeDao.CLASS_EVENT;
import static com.bimcoresolutions.product.bimcity.api.plugins.event.event.EventSpeDao.EVENT_TABLE;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PREFIX_ELEMTABLE;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static org.assertj.core.api.Assertions.assertThat;

class EventSpeDaoTest {

    public static final QueryFormer HISTO = new QueryFormer(POSTGRESQL, SCHEMA_HISTO);
    public static final QueryFormer REALTIME = new QueryFormer(POSTGRESQL, SCHEMA_RT);
    public static final ObjectMapper om = new ObjectMapper().findAndRegisterModules();
    private static EventSpeDao eventSpeDao;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        eventSpeDao = new EventSpeDao(Mockito.mock(DataSource.class), REALTIME, Mockito.mock(DataSource.class), HISTO, om);
    }

    @Nested
    class RealTime {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

        @Test
        void queryList() {
            String expected = """
                    SELECT _id_bimcore, _perimeters, category, priority, label, status, location, COALESCE(realstartdate, estimatedstartdate) AS start, COALESCE(realenddate, estimatedenddate) AS end
                    FROM "realtime"."elem_Event"
                    WHERE 1=1
                     AND status = 'InProgress' AND realenddate IS NULL AND estimatedenddate <= NOW()
                     AND label ILIKE '%randomLabel%'
                     AND category = 'randomCategory'
                     AND tsrange(realstartdate, realenddate) && tsrange('2022-05-01T13:16:00Z', '2022-05-01T14:16:00Z')
                    ORDER BY COALESCE(realstartdate, estimatedstartdate) desc
                    LIMIT 10 OFFSET 0;""";
            StringBuilder result = eventSpeDao.queryListRealtime(10L, 0L, EventRealtimeOrderby.start, "desc", EventRealtimeStatus.ToEnd, "randomLabel", "randomCategory",
                    Instant.parse("2022-05-01T13:16:00Z"), Instant.parse("2022-05-01T14:16:00.00Z"),userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryCount() {
            String expected = """
                    SELECT COUNT(*)
                    FROM "realtime"."elem_Event"
                    WHERE 1=1
                     AND status = 'InProgress' AND realenddate IS NULL AND estimatedenddate <= NOW()
                     AND label ILIKE '%randomLabel%'
                     AND category = 'randomCategory'
                     AND tsrange(realstartdate, realenddate) && tsrange('2022-05-01T13:16:00Z', '2022-05-01T14:16:00Z');""";
            StringBuilder result = eventSpeDao.queryCountRealtime(EventRealtimeStatus.ToEnd, "randomLabel", "randomCategory", Instant.parse("2022-05-01T13:16:00Z"),
                    Instant.parse("2022-05-01T14:16:00.00Z"),userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }
    }

    @Nested
    class Histo {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

        @Test
        void queryList() {
            String expected = """
                    SELECT _id_bimcore, _perimeters, _creation_origin, code, category, priority, label, site, status, comment, creator, COALESCE(realstartdate, estimatedstartdate) AS start, COALESCE(realenddate, estimatedenddate) AS end, _creation_date AS timestamp
                    FROM "histo"."elem_Event"
                    WHERE 1=1
                     AND status = 'Finished'
                     AND site ILIKE '%randomSite%'
                     AND label ILIKE '%randomLabel%'
                     AND category = 'randomCategory'
                     AND COALESCE(realstartdate, estimatedstartdate) > '2022-05-01T13:16:00Z'
                     AND COALESCE(realenddate, estimatedenddate) < '2022-05-01T14:16:00Z'
                    ORDER BY COALESCE(realstartdate, estimatedstartdate) desc
                    LIMIT 10 OFFSET 0;""";
            StringBuilder result = eventSpeDao.queryListHisto(10L, 0L, EventHistoOrderby.start, "desc", EventHistoStatus.Finished, "randomSite", "randomLabel", "randomCategory",
                    Instant.parse("2022-05-01T13:16:00Z"), Instant.parse("2022-05-01T14:16:00.00Z"),userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }

        @Test
        void queryCount() {
            String expected = """
                    SELECT COUNT(*)
                    FROM "histo"."elem_Event"
                    WHERE 1=1
                     AND status = 'Finished'
                     AND site ILIKE '%randomSite%'
                     AND label ILIKE '%randomLabel%'
                     AND category = 'randomCategory'
                     AND COALESCE(realstartdate, estimatedstartdate) > '2022-05-01T13:16:00Z'
                     AND COALESCE(realenddate, estimatedenddate) < '2022-05-01T14:16:00Z';""";
            StringBuilder result = eventSpeDao.queryCountHisto(EventHistoStatus.Finished, "randomSite", "randomLabel", "randomCategory", Instant.parse("2022-05-01T13:16:00Z"),
                    Instant.parse("2022-05-01T14:16:00.00Z"),userPerimeters);
            assertThat(result.toString()).isEqualTo(expected);
        }
    }

    @Test
    void build_sources_query() {
        String query = eventSpeDao.sourcesQuery();
        String expected = "SELECT DISTINCT(\"source\") FROM \"" + REALTIME.getSchema() + "\".\"" + EVENT_TABLE + "\" WHERE \"source\" IS NOT NULL ORDER BY \"source\";";
        assertThat(query).isEqualTo(StringUtils.normalizeSpace(expected));
    }

    @Nested
    class CategoriesInQuery {

        @Test
        void Realtime() {
            String query = eventSpeDao.categoriesInQuery(true);
            String expected = "SELECT DISTINCT(\"category\") FROM \"" + REALTIME.getSchema() + "\".\"" + DB_REALTIME_PREFIX_ELEMTABLE + CLASS_EVENT + "\" WHERE \"category\" IS NOT NULL ORDER BY \"category\";";
            assertThat(query).isEqualTo(StringUtils.normalizeSpace(expected));
        }

        @Test
        void Histo() {
            String query = eventSpeDao.categoriesInQuery(false);
            String expected = "SELECT DISTINCT(\"category\") FROM \"" + HISTO.getSchema() + "\".\"" + DB_REALTIME_PREFIX_ELEMTABLE + CLASS_EVENT + "\" WHERE \"category\" IS NOT NULL ORDER BY \"category\";";

            assertThat(query).isEqualTo(StringUtils.normalizeSpace(expected));
        }
    }

}