package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.spring.ErrorHandler;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.tuple.Pair;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.http.MediaType.APPLICATION_JSON;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@ExtendWith(MockitoExtension.class)
class EventSpeRestTest {

    private static MockMvc mockMvc;
    private static EventSpeRest eventSpeRest;
    private static EventSpeService eventSpeService;

    @BeforeAll
    @SneakyThrows
    public static void init() {
        eventSpeService = Mockito.mock(EventSpeService.class);
        CustomJwtAuthenticationConverter jwtDecoder = Mockito.mock(CustomJwtAuthenticationConverter.class);

        // Mock the get_perimeters() method to return a valid ThreadLocal
        ThreadLocal<Pair<Boolean, List<Integer>>> mockThreadLocal = new ThreadLocal<>();
        mockThreadLocal.set(Pair.of(true,  List.of(1,2,3)));
        when(jwtDecoder.get_perimeters()).thenReturn(mockThreadLocal);

        eventSpeRest = new EventSpeRest(eventSpeService,jwtDecoder);
        mockMvc = MockMvcBuilders.standaloneSetup(eventSpeRest).setControllerAdvice(new ErrorHandler()).build();
    }

    @Nested
    class RealTime {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));
        private final String ENDPOINT_REALTIME = "list";
        @Test
        void get_eventList_success() throws Exception {
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(eventSpeService.listRealtime(10L, 0L, EventRealtimeOrderby.start, "desc", EventRealtimeStatus.ToStart, null, null, null, null,userPerimeters))
                    .willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(get("/spe/event/event/" + ENDPOINT_REALTIME + "?status=ToStart").contentType(APPLICATION_JSON))
                    .andReturn()
                    .getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(eventSpeService).listRealtime(10L, 0L, EventRealtimeOrderby.start, "desc", EventRealtimeStatus.ToStart, null, null, null, null,userPerimeters);
        }

        @Test
        void eventList_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=ToStart&limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void eventList_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=ToStart&offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void eventList_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=ToStart&order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void eventList_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=ToStart&orderby=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'orderby' parameter given : 'randomFalseValue'. (Possible values :");
        }

        @Test
        void eventList_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=ToStart&order=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
        }

        @Test
        void eventList_null_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'status' parameter given : 'null'. (Possible values :");
        }

        @Test
        void eventList_wrong_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'status' parameter given : 'randomFalseValue'. (Possible values :");
        }

        @Test
        void eventList_status_new_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=ToStart&orderby=RandomValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'orderby' parameter given : 'RandomValue'. (Possible values :");
        }

        @Test
        void eventList_status_inprogress_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_REALTIME, "status=ToStart&orderby=RandomValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'orderby' parameter given : 'RandomValue'. (Possible values :");
        }
    }

    @Nested
    class Histo {
        Pair<Boolean, List<Integer>> userPerimeters =Pair.of(true, List.of(1, 2, 3));

        private final String ENDPOINT_HISTO = "listHisto";

        @Test
        void get_eventListHisto_success() throws Exception {
            EnveloppeGetter enveloppeGetter = new EnveloppeGetter();
            enveloppeGetter.setPagination(new EnveloppeGetter.Pagination());
            given(eventSpeService.listHisto(10L, 0L, EventHistoOrderby.start, "desc", null, null, null, null, null, null,userPerimeters))
                    .willReturn(enveloppeGetter);

            MockHttpServletResponse response = mockMvc.perform(get("/spe/event/event/" + ENDPOINT_HISTO + "?").contentType(APPLICATION_JSON))
                    .andReturn()
                    .getResponse();

            assertThat(response.getStatus()).isEqualTo(HttpStatus.OK.value());
            verify(eventSpeService).listHisto(10L, 0L, EventHistoOrderby.start, "desc", null, null, null, null, null, null,userPerimeters);
        }

        @Test
        void eventListHisto_negative_limit() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "limit=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'limit' parameter given : '-1'. (Must be strictly positive)");
        }

        @Test
        void eventListHisto_negative_offset() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "offset=-1")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'offset' parameter given : '-1'. (Must be positive)");
        }

        @Test
        void eventListHisto_null_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "order=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'null'. (Possible values : asc/desc)");
        }

        @Test
        void eventListHisto_wrong_orderby() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "orderby=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'orderby' parameter given : 'randomFalseValue'. (Possible values :");
        }

        @Test
        void eventListHisto_wrong_order() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "order=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'order' parameter given : 'randomFalseValue'. (Possible values : asc/desc)");
        }

        @Test
        void eventListHisto_null_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "status=null")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'status' parameter given : 'null'. (Possible values :");
        }

        @Test
        void eventListHisto_wrong_status() throws Exception {
            assertThat(get_with_paramException(ENDPOINT_HISTO, "status=randomFalseValue")).isInstanceOf(WrongArgumentValueException.class)
                    .hasMessageContaining("Wrong 'status' parameter given : 'randomFalseValue'. (Possible values :");
        }
    }

    Exception get_with_paramException(String endpoint, String param) throws Exception {
        return mockMvc.perform(get("/spe/event/event/" + endpoint + "?" + param).contentType(MediaType.APPLICATION_JSON))
                .andReturn()
                .getResolvedException();
    }

}