package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

@Service
public class EventSpeService {
    private final EventSpeDao eventSpeDao;

    public EventSpeService(EventSpeDao eventSpeDao) {
        this.eventSpeDao = eventSpeDao;
    }

    public EnveloppeGetter<EventRealtimeDTO> listRealtime(long limit, long offset, EventRealtimeOrderby orderBy, String order, EventRealtimeStatus status, String label,
            String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<EventRealtimeDTO> eventDTOList = eventSpeDao.listRealtime(limit, offset, orderBy, order, status, label, category, start, end, userPerimeters);
        if (eventDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = eventSpeDao.countRealtime(status, label, category, start, end, userPerimeters);
        return buildEnveloppeGetter(offset, eventDTOList, total);
    }

    public int countRealtime(EventRealtimeStatus status, String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        return eventSpeDao.countRealtime(status, label, category, start, end, userPerimeters);
    }

    public EnveloppeGetter<EventHistoDTO> listHisto(long limit, long offset, EventHistoOrderby orderBy, String order, EventHistoStatus status, String site, String label, String category,
            Instant start, Instant end,  Pair<Boolean, List<Integer>> userPerimeters) {
        List<EventHistoDTO> eventDTOList = eventSpeDao.listHisto(limit, offset, orderBy, order, status, site, label, category, start, end, userPerimeters);
        if (eventDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }

        int total = eventSpeDao.countHisto(status, site, label, category, start, end, userPerimeters);
        return buildEnveloppeGetter(offset, eventDTOList, total);
    }

    public int countHisto(EventHistoStatus status, String site, String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        return eventSpeDao.countHisto(status, site, label, category, start, end, userPerimeters);
    }

    private <T extends ModelSpe> EnveloppeGetter<T> buildEnveloppeGetter(Long offset, List<T> resultat, int total) {
        EnveloppeGetter<T> toRet = new EnveloppeGetter<>();
        toRet.getPagination().setTotal((long) total);
        toRet.setItems(resultat);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + resultat.size());
        return toRet;
    }

    public List<String> sources() {
        return eventSpeDao.sources();
    }

    public List<String> categoriesIn(boolean inRealtime) {
        return eventSpeDao.categoriesIn(inRealtime);
    }

}
