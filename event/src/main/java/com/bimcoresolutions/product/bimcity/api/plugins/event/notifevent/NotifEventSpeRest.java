package com.bimcoresolutions.product.bimcity.api.plugins.event.notifevent;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

@RestController
@Api(tags = {"NotifEventSpeRest"})
@RequestMapping("/spe/event/notifevent")
public class NotifEventSpeRest extends JwtDecoder {

    private final NotifEventSpeService notifEventSpeService;

    public NotifEventSpeRest(NotifEventSpeService notifEventSpeService, CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.notifEventSpeService = notifEventSpeService;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @ApiOperation(value = "notifeventlist", nickname = "notifeventlist", httpMethod = "GET", notes = "obtenir la liste des évènements")
    @Secured({"ROLE_admin", "ROLE_NotifEvent_show", "ROLE_NotifEvent_read"})
    public EnveloppeGetter<NotifEventDTO> list(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "lastupdate") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("status") @RequestParam(required = false) ENotifEventStatus status,
            @ModelAttribute("name") @RequestParam(required = false) String name,
            @ModelAttribute("source") @RequestParam(required = false) String source,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        Pair<Boolean, List<Integer>> userPerimeters = customJwtAuthenticationConverter.get_perimeters().get();

        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
        if (!List.of("name", "source", "apparitiondate", "status", "category", "lastupdate", "category").contains(orderby)) {
            throw new WrongArgumentValueException(
                    "Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : name,source,category,apparitiondate,status,lastupdate)");
        }

        return notifEventSpeService.notifEventList(limit, offset, orderby, order, status, name, source, category, start, end, userPerimeters);
    }

    //TODO : possible fuite de mémoire (L'instance ce créée plusieur fois)
    @InitBinder
    public void notifEventStatusBinder(WebDataBinder dataBinder) {
        dataBinder.registerCustomEditor(ENotifEventStatus.class, new NotifEventStatusEditor());
    }


    @GetMapping(value = "/sources", produces = "application/json")
    @ApiOperation(value = "sourceslist", nickname = "sourceslist", httpMethod = "GET", notes = "obtenir la liste des sources des notifications")
    @Secured({"ROLE_admin", "ROLE_NotifEvent_read"})
    public List sourcesList() {
        return notifEventSpeService.sourcesList();
    }

    @GetMapping(value = "/types", produces = "application/json")
    @ApiOperation(value = "typeslist", nickname = "typeslist", httpMethod = "GET", notes = "obtenir la liste des types des notifications")
    @Secured({"ROLE_admin", "ROLE_NotifEvent_read"})
    public List<NotifEventDTO> typesList() {
        return notifEventSpeService.typesList();
    }

}