package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import com.bimcoresolutions.util.base.serialisation.GeometrySerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import org.locationtech.jts.geom.Geometry;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EventRealtimeDTO extends ModelSpe {
    private String _id_bimcore;
    private List<Integer> _perimeters;
    private String category;
    private Integer priority;
    private String label;
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant start;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant end;
    @JsonSerialize(using = GeometrySerializer.class)
    private Geometry location;
}
