package com.bimcoresolutions.product.bimcity.api.plugins.event.notifevent;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class NotifEventDTO extends ModelSpe {

    private String _id_bimcore;
    private List<Integer> _perimeters;
    private String source;
    private String name;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant apparitiondate;
    private String status;
    private String category;

}
