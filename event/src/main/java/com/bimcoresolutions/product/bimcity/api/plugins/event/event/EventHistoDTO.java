package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ModelSpe;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EventHistoDTO extends ModelSpe {
    private String _id_bimcore;
    private List<Integer> _perimeters;
    private String _creation_origin;
    private String code;
    private String category;
    private Integer priority;
    private String site;
    private String label;
    private String status;
    private String comment;
    private String creator;
    private String source;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant start;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant end;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant timestamp;

}
