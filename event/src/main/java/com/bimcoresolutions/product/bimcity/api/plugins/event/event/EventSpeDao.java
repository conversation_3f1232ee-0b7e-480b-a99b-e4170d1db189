package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ASpeDao;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.histo.StaticVariables.DB_HISTO_PREFIX_ELEMTABLE;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PREFIX_ELEMTABLE;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatValueToSQL;

@Repository
public class EventSpeDao extends ASpeDao {

    private static final Logger LOGGER = LogManager.getLogger();
    public static final String CLASS_EVENT = "Event";
    public static final String EVENT_TABLE = DB_REALTIME_PREFIX_ELEMTABLE + CLASS_EVENT;

    protected final DataSource dsh;
    protected final QueryFormer qfh;

    public EventSpeDao(@Qualifier("dsRealtime") DataSource dsr, @Qualifier("qfRealtime") QueryFormer qfr, @Qualifier("dsHisto") DataSource dsh,
                       @Qualifier("qfHisto") QueryFormer qfh, ObjectMapper om) {
        super(dsr, qfr, om);
        this.dsh = dsh;
        this.qfh = qfh;
    }

    public List<EventRealtimeDTO> listRealtime(@NonNull Long limit, @NonNull Long offset, @NonNull EventRealtimeOrderby orderBy, @NonNull String order, EventRealtimeStatus status,
                                               String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryListRealtime(limit, offset, orderBy, order, status, label, category, start, end, userPerimeters);
        try {
            return execQuery(sql.toString(), EventRealtimeDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public int countRealtime(EventRealtimeStatus status, String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryCountRealtime(status, label, category, start, end, userPerimeters);
        try {
            return execCount(sql.toString());
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryListRealtime(@NonNull Long limit, @NonNull Long offset, @NonNull EventRealtimeOrderby orderBy, @NonNull String order, EventRealtimeStatus status,
                                           String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT");
        sql.append(" _id_bimcore,");
        sql.append(" _perimeters,");
        sql.append(" category,");
        sql.append(" priority,");
        sql.append(" label,");
        sql.append(" status,");
        sql.append(" location,");
        sql.append(" COALESCE(realstartdate, estimatedstartdate) AS start,");
        sql.append(" COALESCE(realenddate, estimatedenddate) AS end");

        sql.append("\nFROM " + qf.addSchema(EVENT_TABLE));

        sql.append(whereClausesRealtime(status, label, category, start, end,userPerimeters));
        sql.append("\nORDER BY " + orderByRealtime(status, orderBy) + " " + order);
        sql.append("\nLIMIT " + limit + " OFFSET " + offset + ";");
        return sql;
    }

    private String orderByRealtime(EventRealtimeStatus status, EventRealtimeOrderby orderBy) {
        if (orderBy == EventRealtimeOrderby.start) {
            return switch (status) {
                case Upcoming, ToEnd, Finished -> "COALESCE(realstartdate, estimatedstartdate)";
                case ToStart -> "estimatedstartdate";
                case InProgress -> "realstartdate";
            };
        } else if (orderBy == EventRealtimeOrderby.end) {
            return switch (status) {
                case Upcoming, ToStart, InProgress, Finished -> "COALESCE(realenddate, estimatedenddate)";
                case ToEnd -> "estimatedenddate";
            };
        } else {
            return orderBy.name();
        }
    }

    public StringBuilder queryCountRealtime(EventRealtimeStatus status, String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append("\nFROM " + qf.addSchema(EVENT_TABLE));
        sql.append(whereClausesRealtime(status, label, category, start, end,userPerimeters));
        sql.append(";");
        return sql;
    }

    public StringBuilder whereClausesRealtime(EventRealtimeStatus status, String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("\nWHERE 1=1");
        switch (status) {
            case InProgress ->
                    sql.append("\n AND status = 'InProgress' AND (estimatedenddate IS NULL OR estimatedenddate > NOW())");
            case ToStart ->
                    sql.append("\n AND status = 'NotStarted' AND realstartdate IS NULL AND estimatedstartdate <= NOW()");
            case Upcoming ->
                    sql.append("\n AND status = 'NotStarted' AND COALESCE(realstartdate, estimatedstartdate) > NOW()");
            case ToEnd ->
                    sql.append("\n AND status = 'InProgress' AND realenddate IS NULL AND estimatedenddate <= NOW()");
            case Finished -> sql.append("\n AND (status = 'Finished' or status='Canceled')");
        }
        if (label != null) {
            sql.append("\n AND label ILIKE " + betweenSimpleQuote(formatValueToSQL("%" + label + "%")));
        }
        if (category != null) {
            sql.append("\n AND category = " + betweenSimpleQuote(formatValueToSQL(category)));
        }
        if (start != null || end != null) {
            switch (status) {
                case InProgress ->
                        sql.append("\n AND tsrange(realstartdate, realenddate) && tsrange('" + start + "', '" + end + "')");
                case ToStart ->
                        sql.append("\n AND tsrange(estimatedstartdate, realenddate) && tsrange('" + start + "', '" + end + "')");
                case Upcoming ->
                        sql.append("\n AND tsrange(COALESCE(realstartdate, estimatedstartdate), realenddate) && tsrange('" + start + "', '" + end + "')");
                case ToEnd ->
                        sql.append("\n AND tsrange(realstartdate, realenddate) && tsrange('" + start + "', '" + end + "')");
                case Finished ->
                        sql.append("\n AND tsrange(COALESCE(realstartdate, estimatedstartdate), COALESCE(realenddate, estimatedenddate)) && tsrange('" + start + "', '" + end + "')");
            }
        }
        if(!userPerimeters.getLeft()){
            sql.append(" AND _perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }

    public List<EventHistoDTO> listHisto(@NonNull Long limit, @NonNull Long offset, @NonNull EventHistoOrderby orderBy, @NonNull String order, EventHistoStatus status,
                                         String site, String label, String category, Instant start, Instant end  , Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryListHisto(limit, offset, orderBy, order, status, site, label, category, start, end, userPerimeters);
        try {
            return execQuery(sql.toString(), dsh, EventHistoDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public int countHisto(EventHistoStatus status, String site, String label, String category, Instant start, Instant end,Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryCountHisto(status, site, label, category, start, end, userPerimeters);
        try {
            return execCount(sql.toString(), dsh);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryListHisto(@NonNull Long limit, @NonNull Long offset, @NonNull EventHistoOrderby orderBy, @NonNull String order, EventHistoStatus status, String site, String label,
                                        String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT");
        sql.append(" _id_bimcore,");
        sql.append(" _perimeters,");
        sql.append(" _creation_origin,");
        sql.append(" code,");
        sql.append(" category,");
        sql.append(" priority,");
        sql.append(" label,");
        sql.append(" site,");
        sql.append(" status,");
        sql.append(" comment,");
        sql.append(" creator,");
        sql.append(" COALESCE(realstartdate, estimatedstartdate) AS start,");
        sql.append(" COALESCE(realenddate, estimatedenddate) AS end,");
        sql.append(" _creation_date AS timestamp");

        sql.append("\nFROM " + qfh.addSchema(EVENT_TABLE));

        sql.append(whereClausesHisto(status, site, label, category, start, end,userPerimeters));
        sql.append("\nORDER BY " + orderByHisto(orderBy) + " " + order);
        sql.append("\nLIMIT " + limit + " OFFSET " + offset + ";");
        return sql;
    }

    private String orderByHisto(EventHistoOrderby orderBy) {
        if (orderBy == EventHistoOrderby.start) {
            return "COALESCE(realstartdate, estimatedstartdate)";
        } else if (orderBy == EventHistoOrderby.end) {
            return "COALESCE(realenddate, estimatedenddate)";
        } else if (orderBy == EventHistoOrderby.timestamp) {
            return "_creation_date";
        } else {
            return orderBy.name();
        }
    }

    public StringBuilder queryCountHisto(EventHistoStatus status, String site, String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append("\nFROM " + qfh.addSchema(EVENT_TABLE));
        sql.append(whereClausesHisto(status, site, label, category, start, end, userPerimeters));
        sql.append(";");
        return sql;
    }

    public StringBuilder whereClausesHisto(EventHistoStatus status, String site, String label, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("\nWHERE 1=1");
        if (status != null) {
            sql.append("\n AND status = " + betweenSimpleQuote(formatValueToSQL(status.name())));
        }
        if (site != null) {
            sql.append("\n AND site ILIKE " + betweenSimpleQuote(formatValueToSQL("%" + site + "%")));
        }
        if (label != null) {
            sql.append("\n AND label ILIKE " + betweenSimpleQuote(formatValueToSQL("%" + label + "%")));
        }
        if (category != null) {
            sql.append("\n AND category = " + betweenSimpleQuote(formatValueToSQL(category)));
        }
        if (start != null) {
            sql.append("\n AND COALESCE(realstartdate, estimatedstartdate) > '" + start + "'");
        }
        if (end != null) {
            sql.append("\n AND COALESCE(realenddate, estimatedenddate) < '" + end + "'");
        }
        if (!userPerimeters.getLeft()) {
            sql.append(" AND a._perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }

    public List<String> sources() {
        String sql = sourcesQuery();
        try {
            return execQueryToList(sql.toString(), ds).stream().map(Object::toString).toList();
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    String sourcesQuery() {
        StringBuilder query = new StringBuilder("SELECT DISTINCT(").append(qf.addIdentifier("source"));
        query.append(") FROM ").append(qf.addSchema(EVENT_TABLE));
        query.append(" WHERE ").append(qf.addIdentifier("source")).append(" IS NOT NULL");
        query.append(" ORDER BY ").append(qf.addIdentifier("source")).append(";");

        return query.toString();
    }

    public List<String> categoriesIn(boolean inRealtime) {
        String query = categoriesInQuery(inRealtime);
        try {
            if (inRealtime) {
                return execQueryToList(query, ds).stream().map(Object::toString).toList();
            } else {
                return execQueryToList(query, dsh).stream().map(Object::toString).toList();
            }
        } catch (TechnicalException e) {
            LOGGER.error("{}", e.getMessage());
            throw new TechnicalException(e);
        }
    }

    String categoriesInQuery(boolean inRealtime) {
        QueryFormer qf_;
        String prefix;
        if (inRealtime) {
            qf_ = qf;
            prefix = DB_REALTIME_PREFIX_ELEMTABLE;
        } else {
            qf_ = qfh;
            prefix = DB_HISTO_PREFIX_ELEMTABLE;
        }

        StringBuilder query = new StringBuilder("SELECT DISTINCT(").append(qf_.addIdentifier("category"));
        query.append(") FROM ").append(qf_.addSchema(prefix + CLASS_EVENT));
        query.append(" WHERE ").append(qf.addIdentifier("category")).append(" IS NOT NULL");
        query.append(" ORDER BY ").append(qf_.addIdentifier("category")).append(";");

        return query.toString();
    }
}
