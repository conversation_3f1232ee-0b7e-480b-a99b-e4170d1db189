package com.bimcoresolutions.product.bimcity.api.plugins.event.notifevent;

import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.ASpeDao;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.sql.DataSource;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PREFIX_ELEMTABLE;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatValueToSQL;

@Repository
public class NotifEventSpeDao extends ASpeDao {
    private static final Logger LOGGER = LogManager.getLogger();


    public NotifEventSpeDao(@Qualifier("dsRealtime") DataSource dataSource, @Qualifier("qfRealtime") QueryFormer qf, ObjectMapper om) {
        super(dataSource, qf, om);
    }

    public List<NotifEventDTO> notifEventBddList(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, ENotifEventStatus status, String name, String source, String category,
                                                 Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryBddList(limit, offset, orderBy, order, status, name, source, category, start, end, userPerimeters);
        try {
            return execQuery(sql.toString(), NotifEventDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public List sourcesList() {
        StringBuilder sql = querySources();
        try {
            return execQueryToList(sql.toString(), ds);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder querySources() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct a.source");
        sql.append(" FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + "NotifEvent")).append(" AS a");
        return sql;
    }

    public List<NotifEventDTO> typesList() {
        StringBuilder sql = queryTypes();
        try {
            return execQuery(sql.toString(), NotifEventDTO.class);
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryTypes() {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT distinct a.category, a.source");
        sql.append(" FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + "NotifEvent")).append(" AS a");
        sql.append(" WHERE a.category is NOT NULL;");
        return sql;
    }

    public int count(String name, String source, String category, Instant start, Instant end, ENotifEventStatus status, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = queryBddCount(name, source, category, start, end, status, userPerimeters);
        try {
            return execCount(sql.toString());
        } catch (TechnicalException e) {
            LOGGER.error("{}", sql);
            throw new TechnicalException(e);
        }
    }

    public StringBuilder queryBddList(@NonNull Long limit, @NonNull Long offset, @NonNull String orderBy, @NonNull String order, ENotifEventStatus status, String name, String source, String category, Instant start,
                                      Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        if (orderBy.equals("lastupdate")) {
            orderBy = "_last_update_date";
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT _id_bimcore, apparitiondate, status, source, category, name, _perimeters");
        sql.append(" FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + "NotifEvent")).append(" AS a");
        sql.append(whereclauses(name, source, category, start, end, status, userPerimeters));
        sql.append(" ORDER BY a.").append(orderBy).append(" ").append(order);
        sql.append(" LIMIT ").append(limit).append(" OFFSET ").append(offset);
        return sql;
    }

    public StringBuilder queryBddCount(String name, String source, String category, Instant start, Instant end, ENotifEventStatus status, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*)");
        sql.append(" FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + "NotifEvent")).append(" AS a");
        sql.append(whereclauses(name, source, category, start, end, status, userPerimeters));
        return sql;
    }

    public StringBuilder whereclauses(String name, String source, String category, Instant start, Instant end, ENotifEventStatus status, Pair<Boolean, List<Integer>> userPerimeters) {
        StringBuilder sql = new StringBuilder();
        sql.append(" WHERE 1=1");
        if (name != null) {
            sql.append(" AND a.name = ").append(betweenSimpleQuote(formatValueToSQL(name)));
        }
        if (source != null) {
            sql.append(" AND a.source = ").append(betweenSimpleQuote(formatValueToSQL(source)));
        }
        if (category != null) {
            sql.append(" AND a.category = ").append(betweenSimpleQuote(formatValueToSQL(category)));
        }
        if (start != null) {
            sql.append(" AND a.apparitiondate > '").append(start).append("'");
        }
        if (end != null) {
            sql.append(" AND a.apparitiondate < '").append(end).append("'");
        }
        if (status != null) {
            sql.append(" AND a.status = ").append(betweenSimpleQuote(formatValueToSQL(status.toString())));
        }
        if (!userPerimeters.getLeft()) {
            sql.append(" AND a._perimeters && ARRAY[")
               .append(userPerimeters.getRight().stream().map(Object::toString).collect(Collectors.joining(",")))
               .append("]::integer[]");
        }
        return sql;
    }
}
