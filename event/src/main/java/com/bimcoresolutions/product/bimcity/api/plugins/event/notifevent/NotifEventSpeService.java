package com.bimcoresolutions.product.bimcity.api.plugins.event.notifevent;

import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;

@Service
@AllArgsConstructor
public class NotifEventSpeService {

    @Autowired
    private NotifEventSpeDao notfiEventDao;

    public EnveloppeGetter<NotifEventDTO> notifEventList(long limit, long offset, String orderBy, String order, ENotifEventStatus status, String name, String source, String category, Instant start, Instant end, Pair<Boolean, List<Integer>> userPerimeters) {
        List<NotifEventDTO> eventDTOList = notfiEventDao.notifEventBddList(limit, offset, orderBy, order, status, name, source, category, start, end, userPerimeters);

        if (eventDTOList.isEmpty()) {
            throw new NoEntityFoundException();
        }
        int total = notfiEventDao.count(name, source, category, start, end, status, userPerimeters);
        return buildEnveloppeGetter(offset, eventDTOList, total);
    }

    private EnveloppeGetter<NotifEventDTO> buildEnveloppeGetter(Long offset, List<NotifEventDTO> resultat, int total) {
        EnveloppeGetter<NotifEventDTO> toRet = new EnveloppeGetter<>();

        toRet.getItems().addAll(resultat);
        toRet.getPagination().setTotal((long) total);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + resultat.size());
        return toRet;
    }

    public List sourcesList() {
        List sourcesList = notfiEventDao.sourcesList();
        if (sourcesList.isEmpty()) {
            throw new NoEntityFoundException();
        }
        return sourcesList;
    }

    public List<NotifEventDTO> typesList() {
        List<NotifEventDTO> sourcesList = notfiEventDao.typesList();
        if (sourcesList.isEmpty()) {
            throw new NoEntityFoundException();
        }
        return sourcesList;
    }
}