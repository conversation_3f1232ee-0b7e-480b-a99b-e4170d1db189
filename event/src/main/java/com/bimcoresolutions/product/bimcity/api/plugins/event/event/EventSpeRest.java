package com.bimcoresolutions.product.bimcity.api.plugins.event.event;

import com.bimcoresolutions.api.base.security.keycloack.CustomJwtAuthenticationConverter;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.product.bimcity.api.plugins.common.util.JwtDecoder;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

@RestController
@Api(tags = {"EventSpeRest"})
@RequestMapping("/spe/event/event")
public class EventSpeRest extends JwtDecoder {

    private final EventSpeService eventSpeService;

    public EventSpeRest(EventSpeService eventSpeService,CustomJwtAuthenticationConverter customJwtAuthenticationConverter) {
        super(customJwtAuthenticationConverter);
        this.eventSpeService = eventSpeService;
    }

    @GetMapping(value = "/list", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Event_show", "ROLE_Event_read"})
    public EnveloppeGetter<EventRealtimeDTO> eventList(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "start") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("status") @RequestParam(required = true) String status,
            @ModelAttribute("label") @RequestParam(required = false) String label,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("creator") @RequestParam(required = false) String creator,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        EventRealtimeStatus s;
        Pair<Boolean, List<Integer>> userPerimeters =  customJwtAuthenticationConverter.get_perimeters().get();
        if (!EventRealtimeStatus.nameValues().contains(status)) {
            throw new WrongArgumentValueException(
                    "Wrong 'status' parameter given : '" + status + "'. (Possible values : " + EventRealtimeStatus.nameValues() + ")");
        } else {
            s = EventRealtimeStatus.valueOf(status);
        }

        checkLimitOffsetOrder(limit, offset, order);

        EventRealtimeOrderby orderBy;
        if (!EventRealtimeOrderby.nameValues().contains(orderby)) {
            throw new WrongArgumentValueException(
                    "Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : " + EventRealtimeOrderby.nameValues() + ")");
        } else {
            orderBy = EventRealtimeOrderby.valueOf(orderby);
        }

        return eventSpeService.listRealtime(limit, offset, orderBy, order, s, label, category, start, end, userPerimeters);
    }

    @GetMapping(value = "/count", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Event_show", "ROLE_Event_read"})
    public int eventCount(
            @ModelAttribute("status") @RequestParam(required = true) String status,
            @ModelAttribute("label") @RequestParam(required = false) String label,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("creator") @RequestParam(required = false) String creator,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        EventRealtimeStatus s;
        Pair<Boolean, List<Integer>> userPerimeters =  customJwtAuthenticationConverter.get_perimeters().get();
        if (!EventRealtimeStatus.nameValues().contains(status)) {
            throw new WrongArgumentValueException(
                    "Wrong 'status' parameter given : '" + status + "'. (Possible values : " + EventRealtimeStatus.nameValues() + ")");
        } else {
            s = EventRealtimeStatus.valueOf(status);
        }

        return eventSpeService.countRealtime(s, label, category, start, end, userPerimeters);
    }

    @GetMapping(value = "/listHisto", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Event_show", "ROLE_Event_read"})
    public EnveloppeGetter<EventHistoDTO> eventListHisto(
            @ModelAttribute("limit") @RequestParam(required = false, defaultValue = "10") Long limit,
            @ModelAttribute("offset") @RequestParam(required = false, defaultValue = "0") Long offset,
            @ModelAttribute("orderby") @RequestParam(required = false, defaultValue = "start") String orderby,
            @ModelAttribute("order") @RequestParam(required = false, defaultValue = "desc") String order,
            @ModelAttribute("site") @RequestParam(required = false) String site,
            @ModelAttribute("status") @RequestParam(required = false) String status,
            @ModelAttribute("label") @RequestParam(required = false) String label,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        EventHistoStatus s;
        Pair<Boolean, List<Integer>> userPerimeters =  customJwtAuthenticationConverter.get_perimeters().get();
        if (status == null) {
            s = null;
        } else if (!EventHistoStatus.nameValues().contains(status)) {
            throw new WrongArgumentValueException(
                    "Wrong 'status' parameter given : '" + status + "'. (Possible values : " + EventHistoStatus.nameValues() + ")");
        } else {
            s = EventHistoStatus.valueOf(status);
        }

        checkLimitOffsetOrder(limit, offset, order);

        EventHistoOrderby orderBy;
        if (!EventHistoOrderby.nameValues().contains(orderby)) {
            throw new WrongArgumentValueException(
                    "Wrong 'orderby' parameter given : '" + orderby + "'. (Possible values : " + EventHistoOrderby.nameValues() + ")");
        } else {
            orderBy = EventHistoOrderby.valueOf(orderby);
        }

        return eventSpeService.listHisto(limit, offset, orderBy, order, s, site, label, category, start, end,userPerimeters);
    }

    @GetMapping(value = "/countHisto", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Event_show", "ROLE_Event_read"})
    public int eventCountHisto(
            @ModelAttribute("status") @RequestParam(required = false) String status,
            @ModelAttribute("site") @RequestParam(required = false) String site,
            @ModelAttribute("label") @RequestParam(required = false) String label,
            @ModelAttribute("category") @RequestParam(required = false) String category,
            @ModelAttribute("start") @RequestParam(required = false) Instant start,
            @ModelAttribute("end") @RequestParam(required = false) Instant end) {
        EventHistoStatus s;
        Pair<Boolean, List<Integer>> userPerimeters =  customJwtAuthenticationConverter.get_perimeters().get();
        if (status == null) {
            s = null;
        } else if (!EventHistoStatus.nameValues().contains(status)) {
            throw new WrongArgumentValueException(
                    "Wrong 'status' parameter given : '" + status + "'. (Possible values : " + EventHistoStatus.nameValues() + ")");
        } else {
            s = EventHistoStatus.valueOf(status);
        }

        return eventSpeService.countHisto(s, site, label, category, start, end,userPerimeters);
    }

    private void checkLimitOffsetOrder(Long limit, Long offset, String order) {
        if (limit < 1) {
            throw new WrongArgumentValueException("Wrong 'limit' parameter given : '" + limit + "'. (Must be strictly positive)");
        }
        if (offset < 0) {
            throw new WrongArgumentValueException("Wrong 'offset' parameter given : '" + offset + "'. (Must be positive)");
        }
        if (!List.of("asc", "desc").contains(order.toLowerCase())) {
            throw new WrongArgumentValueException("Wrong 'order' parameter given : '" + order + "'. (Possible values : asc/desc)");
        }
    }

    @GetMapping(value = "/sources", produces = "application/json")
    @ApiOperation(value = "sourceslist", nickname = "sourceslist", httpMethod = "GET", notes = "obtenir la liste des sources des évènements")
    @Secured({"ROLE_admin", "ROLE_Event_read"})
    public List<String> getSources() {
        return eventSpeService.sources();
    }

    @GetMapping(value = "/categories/realtime", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Event_read"})
    public List<String> categoriesInRealtime() {
        return eventSpeService.categoriesIn(true);
    }

    @GetMapping(value = "/categories/history", produces = "application/json")
    @Secured({"ROLE_admin", "ROLE_Event_read"})
    public List<String> categoriesInHistory() {
        return eventSpeService.categoriesIn(false);
    }
}

