spring:
  application:
    name: perimeters-plugin-test
  
  # Use H2 in-memory database for testing
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # JPA configuration for testing
  jpa:
    database: h2
    hibernate:
      ddl-auto: create-drop
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
    show-sql: true
    open-in-view: false

# Logging configuration for testing
logging:
  level:
    com.bimcoresolutions: DEBUG
    org.springframework: WARN
    org.hibernate: WARN
