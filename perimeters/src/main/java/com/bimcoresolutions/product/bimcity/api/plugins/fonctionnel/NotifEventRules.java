package com.bimcoresolutions.product.bimcity.api.plugins.fonctionnel;

import com.bimcoresolutions.api.base.modules.updatemetier.PerimeterComputer;
import com.bimcoresolutions.product.bimcity.api.generated.server.model.NotifEvent;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.EntityBody;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.Set;

@Log4j2
@Component
public class NotifEventRules extends PerimeterComputer<NotifEvent> {

    @Override
    public Set<Integer> computeOnCreate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        return Set.of(0);
    }

    @Override
    public Set<Integer> computeOnUpdate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        return Set.of(0);
    }

}
