package com.bimcoresolutions.product.bimcity.api.plugins.utils;

public enum EquipmentClass {
    Cabinet,
    Camera,
    Controller,
    CountingStation,
    DynPanel,
    EnvironmentalSensor,
    Intersection,
    LightingPoint,
    Meter,
    TransformerStation,
    RailCrossing,
    Railway,
    Parking,
    RiverStructure,
    ChargingPoint,
    ChargingStation;

    public static boolean contains(String value) {
        for (EquipmentClass equipmentClass : EquipmentClass.values()) {
            if (equipmentClass.name().equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }
}