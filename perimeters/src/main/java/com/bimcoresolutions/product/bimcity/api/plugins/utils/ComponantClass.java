package com.bimcoresolutions.product.bimcity.api.plugins.utils;

public enum ComponantClass {
    Source,
    Ballast,
    Crosse,
    Lantern,
    Support;

    public static boolean contains(String value) {
        for (ComponantClass equipmentClass : ComponantClass.values()) {
            if (equipmentClass.name().equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }
}