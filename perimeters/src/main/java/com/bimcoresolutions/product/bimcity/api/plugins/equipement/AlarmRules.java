package com.bimcoresolutions.product.bimcity.api.plugins.equipement;

import com.bimcoresolutions.api.base.modules.updatemetier.PerimeterComputer;
import com.bimcoresolutions.product.bimcity.api.generated.server.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.plugins.utils.EquipmentClass;
import com.bimcoresolutions.product.bimcity.api.plugins.utils.GenericGetPerimeters;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.EntityBody;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class AlarmRules extends PerimeterComputer<Alarm> {

    private final GenericGetPerimeters genericGetPerimeters;
    @Autowired
    public AlarmRules(GenericGetPerimeters genericGetPerimeters) {
        this.genericGetPerimeters = genericGetPerimeters;
    }

    @Override
    public Set<Integer> computeOnCreate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        return getPerimeters(cmRequestv2, entityBody);
    }

    @Override
    public Set<Integer> computeOnUpdate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        return getPerimeters(cmRequestv2, entityBody);
    }

    private Set<Integer> getPerimeters(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        Set<Integer> result = new HashSet<>();
        Map<String, Map<String, Set<String>>> getRelatedIdsFromRequest = genericGetPerimeters.getEquipementIdsByClasseMetier(cmRequestv2, entityBody.getId(), "Alarm");
        if(!getRelatedIdsFromRequest.isEmpty()){
            if (getRelatedIdsFromRequest.get("Alarm").size() == 1) {
                getRelatedIdsFromRequest.forEach((i, map) -> map.forEach((classmet , set)->{
                    if(EquipmentClass.contains(classmet)){
                        result.addAll(genericGetPerimeters.getPerimetersFromDB(set, classmet));
                    }
                }));
                return result.isEmpty() ? Set.of(0) : result;
            }}
        return Set.of();
    }

}
