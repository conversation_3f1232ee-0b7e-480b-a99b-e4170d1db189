package com.bimcoresolutions.product.bimcity.api.plugins.equipement;

import com.bimcoresolutions.api.base.modules.updatemetier.PerimeterComputer;
import com.bimcoresolutions.product.bimcity.api.generated.server.dao.LightingPointDao;
import com.bimcoresolutions.product.bimcity.api.generated.server.model.Ballast;
import com.bimcoresolutions.product.bimcity.api.generated.server.model.LightingPoint;
import com.bimcoresolutions.product.bimcity.api.plugins.utils.EquipmentClass;
import com.bimcoresolutions.product.bimcity.api.plugins.utils.GenericGetPerimeters;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.EntityBody;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.perimeters.CMPerimeters;
import io.grpc.xds.shaded.io.envoyproxy.envoy.type.matcher.ListStringMatcher;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PERIMETERS;

@Log4j2
@Component
public class LightingPointRules extends PerimeterComputer<LightingPoint>  {

    private final GenericGetPerimeters genericGetPerimeters;
    private final LightingPointDao lightingPointDao;

    @Autowired
    public LightingPointRules(GenericGetPerimeters genericGetPerimeters, LightingPointDao lightingPointDao) {
        this.genericGetPerimeters = genericGetPerimeters;
        this.lightingPointDao = lightingPointDao;
    }

    @Override
    public Set<Integer> computeOnCreate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        return getPerimeters(cmRequestv2, entityBody);
    }

    @Override
    public void computeOnUpdate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        entityBody.getContent().put(DB_REALTIME_PERIMETERS,getPerimeters(cmRequestv2, entityBody)) ;
    }

    @Override
    public void computeOnUpdatePerimeter(CMPerimeters cmPerimeters,List<String> ids){
        getRelationsOfEquipement(cmPerimeters, ids);
    }

    
    private Map<String, List<String>> getRelationsOfEquipement(CMPerimeters cmPerimeters, List<String> ids) {
        ids.forEach(id -> {
            LightingPoint LP = lightingPointDao.getId(id);
            LP.
        });

        
    }


    private Set<Integer> getPerimeters(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        Set<Integer> result = new HashSet<>();
        Map<String, Map<String, Set<String>>> getRelatedIdsFromRequest = genericGetPerimeters.getEquipementIdsByClasseMetier(cmRequestv2, entityBody.getId(), "Ballast");
        getRelatedIdsFromRequest.forEach((i, map) -> map.forEach((classmet , set)->{
            if(EquipmentClass.contains(classmet)){
                result.addAll(genericGetPerimeters.getPerimetersFromDB(set, classmet));
            }
        }));
        return result.isEmpty() ? Set.of(0) : result;
    }
}
