package com.bimcoresolutions.product.bimcity.api.plugins.utils;

import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;

@Log4j2
@Component
public class GenericGetPerimeters {

    public final EntityManager entityManager;
    public final QueryFormer queryFormer;
    public final FunctionalModelService functionalModelService;

    @Autowired
    public GenericGetPerimeters(EntityManager entityManager,
                               @Qualifier("qfRealtime") QueryFormer queryFormer,FunctionalModelService functionalModelService) {
        this.entityManager = entityManager;
        this.queryFormer = queryFormer;
        this.functionalModelService = functionalModelService;
    }

    public Map<String, Map<String, Set<String>>> getEquipementIdsByClasseMetier(CMRequestv2 cmRequestv2, String id, String classeMetier) {
        if (cmRequestv2.getCommitMetier() == null || cmRequestv2.getCommitMetier().getUpdRel() == null || id == null || classeMetier == null) {
            return Map.of();
        }
        Map<String, Map<String, Set<String>>> result = new HashMap<>();

        cmRequestv2.getCommitMetier().getUpdRel().entrySet()
                .stream()
                .filter(entry -> entry.getKey().startsWith(classeMetier))
                .forEach(entry -> {
                    String relationKey = entry.getKey();
                    String equipmentType = extractEquipmentTypeFromRelationKey(relationKey);
                    String properEquipmentClassName = functionalModelService.getModel()
                                                                            .getElementsBimCore()
                                                                            .get(classeMetier)
                                                                            .getRelations()
                                                                            .get(classeMetier + "_" + equipmentType).getElementDestination()
                                                                            .getParentTypeCode();

                    entry.getValue().stream()
                            .filter(relation -> id.equals(relation.getOrigin()) && relation.getDestinations() != null)
                            .forEach(relation -> relation.getDestinations().forEach(dest -> {
                                if (dest != null && dest.get_id_bimcore() != null) {
                                    result.computeIfAbsent(classeMetier, k -> new HashMap<>())
                                          .computeIfAbsent(properEquipmentClassName, k -> new HashSet<>())
                                          .add(dest.get_id_bimcore());
                                }
                            }));
                });
        return result;
    }

    public Set<Integer> getPerimetersFromDB(Set<String> id, String classeMetier) {
        if (id == null || classeMetier == null || id.isEmpty()) {
            return Set.of();
        }
        try {
            String tableName = DB_REALTIME_PREFIX_ELEMTABLE + classeMetier;
            String fullTableName = queryFormer.addSchema(tableName);

            String sql = "SELECT " + queryFormer.addIdentifier(DB_REALTIME_PERIMETERS) +
                    " FROM " + fullTableName +
                    " WHERE " + queryFormer.addIdentifier(DB_REALTIME_ID_BIMCORE) + " IN (:ids)";

            Query query = entityManager.createNativeQuery(sql);
            query.setParameter("ids", id);
            List<Object> results = query.getResultList();
            Set<Integer> perimeters = new HashSet<>();
            for (Object perimetersObj : results) {
                if (perimetersObj != null) {
                    perimeters.addAll(parsePerimetersFromDatabaseResult(perimetersObj));
                }
            }
            return perimeters;
        } catch (Exception e) {
            log.error("Error getting perimeters from equipment {} for classeMetier {} equipment accepted {}", id, classeMetier,EquipmentClass.values(), e);
            return Set.of();
        }
    }

    public Set<Integer> parsePerimetersFromDatabaseResult(Object perimetersObj) {
        if (perimetersObj == null) {
            throw new IllegalArgumentException("perimetersObj is null");
        }
        if (perimetersObj instanceof Integer[] integerArray) {
            Set<Integer> perimeters = new HashSet<>();
            for (Integer element : integerArray) {
                if (element != null) {
                    perimeters.add(element);
                }
            }
            return perimeters;
        }
        throw new IllegalStateException("Unexpected type for perimetersObj: " + perimetersObj.getClass().getName());
    }

    private String extractEquipmentTypeFromRelationKey(String relationKey) {
        if (relationKey == null || !relationKey.contains("_")) {
            return relationKey;
        }
        return relationKey.substring(relationKey.indexOf("_") + 1);
    }


}
