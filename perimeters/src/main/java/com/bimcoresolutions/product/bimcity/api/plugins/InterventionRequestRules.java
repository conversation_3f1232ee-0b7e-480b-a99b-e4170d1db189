package com.bimcoresolutions.product.bimcity.api.plugins;

import com.bimcoresolutions.api.base.modules.updatemetier.PerimeterComputer;
import com.bimcoresolutions.product.bimcity.api.generated.server.model.InterventionRequest;
import com.bimcoresolutions.product.bimcity.api.plugins.utils.EquipmentClass;
import com.bimcoresolutions.product.bimcity.api.plugins.utils.GenericGetPerimeters;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.EntityBody;
import jakarta.persistence.Query;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PREFIX_RELTABLE;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_REL_ID_BIMCORE_ORIGIN;

@Log4j2
@Component
public class InterventionRequestRules extends PerimeterComputer<InterventionRequest> {

    private final GenericGetPerimeters genericGetPerimeters;

    @Autowired
    public InterventionRequestRules(GenericGetPerimeters genericGetPerimeters) {
        this.genericGetPerimeters = genericGetPerimeters;
    }

    @Override
    @SneakyThrows
    public Set<Integer> computeOnCreate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        return getPerimeters(cmRequestv2, entityBody);
    }

    @Override
    @SneakyThrows
    public Set<Integer> computeOnUpdate(CMRequestv2 cmRequestv2, EntityBody entityBody)  {
        return getPerimeters(cmRequestv2, entityBody);
    }

    private Set<Integer> getPerimeters(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        Set<Integer> result = new HashSet<>();
        Map<String, Map<String, Set<String>>> getRelatedIdsFromRequest = genericGetPerimeters.getEquipementIdsByClasseMetier(cmRequestv2, entityBody.getId(), "InterventionRequest");
        getRelatedIdsFromRequest
                .forEach((i, map) ->
                map.forEach((classmet , set)->
                result.addAll(getPerimetersFromEquipementIR(set, classmet))));
        return result;
    }

    public Set<Integer> getPerimetersFromEquipementIR(Set<String> idEquipement, String classeMetier) {
        if (idEquipement == null || idEquipement.isEmpty() || classeMetier == null ||
                (!EquipmentClass.contains(classeMetier) && !classeMetier.equalsIgnoreCase("Report"))) {
            return Set.of();
        }

        String tableName = DB_REALTIME_PREFIX_ELEMTABLE + classeMetier;
        String fullTableName = genericGetPerimeters.queryFormer.addSchema(tableName);

        String sql = "SELECT " + genericGetPerimeters.queryFormer.addIdentifier(DB_REALTIME_PERIMETERS) +
                " FROM " + fullTableName +
                " WHERE " + genericGetPerimeters.queryFormer.addIdentifier(DB_REALTIME_ID_BIMCORE) + " IN :ids";

        Query query = genericGetPerimeters.entityManager.createNativeQuery(sql);
        query.setParameter("ids", idEquipement);
        List<Object> results = query.getResultList();

        Set<Integer> allPerimeters = new HashSet<>();
        for (Object result : results) {
            if (result != null) {
                allPerimeters.addAll(genericGetPerimeters.parsePerimetersFromDatabaseResult(result));
            }
        }

        return allPerimeters;
    }

}
