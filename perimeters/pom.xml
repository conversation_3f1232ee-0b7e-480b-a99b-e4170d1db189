<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bimcoresolutions.product.bimcity.api</groupId>
        <artifactId>bimcity-api-plugins</artifactId>
        <version>3.4.1-SNAPSHOT</version>
    </parent>

    <groupId>com.bimcoresolutions.product.bimcity.api.plugins</groupId>
    <artifactId>perimeters</artifactId>
    <name>perimeters</name>

    <properties>
        <bimapibase.version>4.2.1-SNAPSHOT</bimapibase.version>
        <cityapp-api-generated.version>2.4.1-SNAPSHOT</cityapp-api-generated.version>
    </properties>

    <dependencies>
        <!-- BIMCORE -->
        <dependency>
            <groupId>com.bimcoresolutions.api</groupId>
            <artifactId>api-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bimcoresolutions.product.cityapp.api.generated</groupId>
            <artifactId>cityapp-api-generated-server</artifactId>
            <version>${cityapp-api-generated.version}</version>
        </dependency>

        <!-- JPA and Database dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bimcoresolutions.util</groupId>
            <artifactId>generator-maven-plugin</artifactId>
            <version>4.3.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>nexus.read</id>
            <url>https://nxs.factory.showroom.aixom.tech/repository/bim-maven/</url>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
