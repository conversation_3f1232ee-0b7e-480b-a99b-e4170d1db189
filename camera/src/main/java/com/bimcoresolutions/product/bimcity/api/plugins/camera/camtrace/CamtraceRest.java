package com.bimcoresolutions.product.bimcity.api.plugins.camera.camtrace;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@RestController
@Api(tags = {"CamtraceRest"})
@RequestMapping("/spe/camera/camtrace")
public class CamtraceRest {
    private final CamtraceService camtraceService;
    public CamtraceRest(CamtraceService camtraceService) {
        this.camtraceService = camtraceService;
    }

    @GetMapping(value = "/screens", produces = APPLICATION_JSON_VALUE)
    @ApiOperation(value = "passive screens", httpMethod = "GET", notes = "obtenir la liste des écrans passif camtrace")
    @Secured({"ROLE_admin", "ROLE_User_manage"})
    public String passiveScreenList() {
        return camtraceService.passiveScreenList();
    }

    @GetMapping(value = "/servers", produces = APPLICATION_JSON_VALUE)
    @ApiOperation(value = "servers", httpMethod = "GET", notes = "obtenir la liste des servers camtrace")
    @Secured({"ROLE_admin", "ROLE_User_manage"})
    public String serversList() {
        return camtraceService.serversList();
    }

    @GetMapping(value = "/groups", produces = APPLICATION_JSON_VALUE)
    @ApiOperation(value = "groups", httpMethod = "GET", notes = "obtenir la liste des mosaïque camtrace")
    @Secured({"ROLE_admin", "ROLE_User_manage"})
    public String groupsList() {
        return camtraceService.groupsList();
    }

    @GetMapping(value = "/cameras", produces = APPLICATION_JSON_VALUE)
    @ApiOperation(value = "cameras", httpMethod = "GET", notes = "obtenir la liste des caméras camtrace")
    @Secured({"ROLE_admin", "ROLE_User_manage"})
    public String camerasList() {
        return camtraceService.camerasList();
    }

    @GetMapping(value = "/cameras/{id}", produces = APPLICATION_JSON_VALUE)
    @ApiOperation(value = "cameras/{id}", httpMethod = "GET", notes = "obtenir la caméra camtrace")
    @Secured({"ROLE_admin", "ROLE_User_manage"})
    public String cameraById(@PathVariable String id) {
        return camtraceService.cameraById(id);
    }

    @GetMapping(value = "/screens/{id}", produces = APPLICATION_JSON_VALUE)
    @ApiOperation(value = "screens/{id}", httpMethod = "GET", notes = "obtenir l'écran passif camtrace'")
    @Secured({"ROLE_admin", "ROLE_User_manage"})
    public String screenById(@PathVariable String id) {
        return camtraceService.screenById(id);
    }
}
