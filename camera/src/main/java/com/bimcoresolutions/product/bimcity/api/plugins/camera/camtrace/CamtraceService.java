package com.bimcoresolutions.product.bimcity.api.plugins.camera.camtrace;

import com.bimcoresolutions.util.base.configuration.util.exceptions.InitializationException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

import static org.springframework.http.HttpMethod.GET;

@Service
public class CamtraceService {
    private final RestTemplate restTemplate = new RestTemplate();
    private final HttpHeaders basicAuthHeaders;
    private final String url;

    public CamtraceService(@Value("${plugins.camera.camtrace.url}") String url, @Value("${plugins.camera.camtrace.login}") String login,
                           @Value("${plugins.camera.camtrace.password}") String password) {
        this.url = Optional.ofNullable(url).orElseThrow(() -> new InitializationException("configuration missing at plugins.camera.camtrace.url"));
        String l = Optional.ofNullable(login).orElseThrow(() -> new InitializationException("configuration missing at plugins.camera.camtrace.login"));
        String p = Optional.ofNullable(password).orElseThrow(() -> new InitializationException("configuration missing at plugins.camera.camtrace.password"));
        basicAuthHeaders = new HttpHeaders();
        basicAuthHeaders.setBasicAuth(l, p);
    }

    public String passiveScreenList() {
        ResponseEntity<String> response = restTemplate.exchange(url + "screens", GET, new HttpEntity<>(basicAuthHeaders), String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        } else {
            return "Error occur while trying to contact Camtrace API";
        }
    }

    public String serversList() {
        ResponseEntity<String> response = restTemplate.exchange(url + "servers", GET, new HttpEntity<>(basicAuthHeaders), String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        } else {
            return "Error occur while trying to contact Camtrace API";
        }
    }

    public String groupsList() {
        ResponseEntity<String> response = restTemplate.exchange(url + "groups", GET, new HttpEntity<>(basicAuthHeaders), String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        } else {
            return "Error occur while trying to contact Camtrace API";
        }
    }

    public String camerasList() {
        ResponseEntity<String> response = restTemplate.exchange(url + "cameras", GET, new HttpEntity<>(basicAuthHeaders), String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        } else {
            return "Error occur while trying to contact Camtrace API";
        }
    }

    public String cameraById(String id) {
        ResponseEntity<String> response = restTemplate.exchange(url + "cameras/" + id, GET, new HttpEntity<>(basicAuthHeaders), String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        } else {
            return "Error occur while trying to contact Camtrace API";
        }
    }

    public String screenById(String id) {
        ResponseEntity<String> response = restTemplate.exchange(url + "screens/" + id, GET, new HttpEntity<>(basicAuthHeaders), String.class);
        if (response.getStatusCode().is2xxSuccessful()) {
            return response.getBody();
        } else {
            return "Error occur while trying to contact Camtrace API";
        }
    }
}
