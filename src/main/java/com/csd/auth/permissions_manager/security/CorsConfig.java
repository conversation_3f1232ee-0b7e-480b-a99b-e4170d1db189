package com.csd.auth.permissions_manager.security;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@EnableWebMvc
@ConfigurationProperties(prefix = "web.cors")
@Getter
@Setter
public class CorsConfig implements WebMvcConfigurer {

  private String allowedOrigins;
  private String allowedMethods;

  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/**").allowedOrigins(allowedOrigins.split(",")).allowedMethods(allowedMethods.split(","));
  }
}
