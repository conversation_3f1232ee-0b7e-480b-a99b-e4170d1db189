package com.bimcoresolutions.feeder.base.plugins.sigharopa.model.railway;

import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.SigHaropaCollection;
import lombok.*;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RailwayCollection extends SigHaropaCollection {
    String type;
    Map<String, Object> crs;
    List<RailwaySig> features;
}
