package com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructure.state;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RiverStructureState {
    private String id;
    private String name;
    private Integer openState;
    private Boolean lockState;
    private String comment;
    private Long lastupdate;
}

