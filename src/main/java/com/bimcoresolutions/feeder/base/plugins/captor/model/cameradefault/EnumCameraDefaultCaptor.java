package com.bimcoresolutions.feeder.base.plugins.captor.model.cameradefault;

import java.util.Arrays;
import java.util.Objects;

public enum EnumCameraDefaultCaptor {

    DEFAULT("0", ""),
    AUTHENTICATION_FAILED("AUTHENTICATION_FAILED", "Echec de l'authentification"),
    BAD_VIDEO("BAD_VIDEO", "Mauvaise video"),
    CAMERA_MOTION("CAMERA_MOTION", "Mouvement caméra"),
    CERTIFICATE_ERROR("CERTIFICATE_ERROR", "Erreur de certificat"),
    CERTIFICATE_VALIDITY_WARNING("CERTIFICATE_VALIDITY_WARNING", "Avertissement relatif à la validité du certificat"),
    COMM_ERROR("COMM_ERROR", "Erreur de communication"),
    CONFIGURATION("CONFIGURATION", "Configuration"),
    CONFIGURATION_CHANGE_FAILED("CONFIGURATION_CHANGE_FAILED", "Modification de configuration échouée"),
    DB_SYNC_ERROR("DB_SYNC_ERROR", "Erreur de synchronisation de base de données"),
    DETECTOR_ERROR("DETECTOR_ERROR", "Erreur détecteur"),
    DOWNLOAD_FAILED("DOWNLOAD_FAILED", "Erreur d'enregistrement"),
    INPUT_1("INPUT_1", "Entrée 1"),
    INTEGRATION_ERROR("INTEGRATION_ERROR", "Erreur d'intégration"),
    INTERNAL_ERROR("INTERNAL_ERROR", "Erreur interne"),
    LAST_MOVIE_FAILED("LAST_MOVIE_FAILED", "Dernière séquence vidéo échouée"),
    LAST_PREVIEW_FAILED("LAST_PREVIEW_FAILED", "Dernière image échouée"),
    LICENSE_INVALID("LICENSE_INVALID", "Licence non valide"),
    MEMORY_TOO_LOW("MEMORY_TOO_LOW", "Trop peu de mémoire"),
    NO_VIDEO("NO_VIDEO", "Absence de signal vidéo"),
    PLUGIN_LOST_CONNECTION("PLUGIN_LOST_CONNECTION", "Connexion plug-in perdue"),
    POWERFAILURE("POWERFAILURE", "Panne de courant"),
    REBOOT("REBOOT", "Réinitialiser"),
    REDUNDANT("REDUNDANT", "Redondant"),
    SCENARIO_EVENT("SCENARIO_EVENT", "Evenement de scénario"),
    SERVERS_OUT_OF_TIME_SYNC("SERVERS_OUT_OF_TIME_SYNC", "Horloge des serveurs non synchronisés"),
    TEMPERATURE("TEMPERATURE", "Température"),
    TOO_MANY_SERVERS_DOWN("TOO_MANY_SERVERS_DOWN", "Trop de serveur arrêtés"),
    UNKNOWN_CONFIGURATION("UNKNOWN_CONFIGURATION", "Configuration inconnue"),
    USER_LOGGED_ON("USER_LOGGED_ON", "Utilisateur connecté"),
    VIRTUAL_IP_CHANGED("VIRTUAL_IP_CHANGED", "Adresse IP virtuelle modifiée");

    private final String value;
    private final String label;

    EnumCameraDefaultCaptor(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValeur() {
        return value;
    }

    public String getLabel() {
        return label;
    }


    public static EnumCameraDefaultCaptor getByValue(String value) {
        return Arrays.stream(EnumCameraDefaultCaptor.values())
                .filter(e -> Objects.equals(e.value, value)).findFirst()
                .orElse(DEFAULT);
    }
}
