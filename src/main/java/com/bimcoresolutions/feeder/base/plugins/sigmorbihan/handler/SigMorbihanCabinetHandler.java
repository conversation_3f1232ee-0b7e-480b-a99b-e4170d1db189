package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.SigMorbihanUtils;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.cabinet.CabinetSig;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.cabinet.CabinetSigCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.bimcoresolutions.feeder.base.plugins.sigmorbihan.SigMorbihanUtils.MORBIHANSIG;

@Log4j2
@Component
public class SigMorbihanCabinetHandler extends SigMorbihanHandler {
    public static final int MAX_SIZE = 100;
    private final CabinetApiClient cabinetApiClient;
    private final SigMorbihanUtils sigMorbihanUtils;

    public SigMorbihanCabinetHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, CabinetApiClient cabinetApiClient, SigMorbihanUtils sigMorbihanUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.cabinetApiClient = cabinetApiClient;
        this.sigMorbihanUtils = sigMorbihanUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "cabinet";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        CabinetSigCollection input = objectMapper.readValue(fileBody, CabinetSigCollection.class);
        List<CabinetSig> comingCabinets = input.getFeatures();
        int cabinetsCount = input.getFeaturesCount();
        int createdCount = 0;
        int updatedCount = 0;
        int deletedCount = 0;
        do {
            List<CabinetSig> slicedCabinets = comingCabinets.stream()
                    .limit(MAX_SIZE)
                    .toList();
            List<String> idsSig = new ArrayList<>();
            slicedCabinets.forEach(cabinet -> idsSig.add(cabinet.getPrimaryKey()));
            List<Cabinet> cabinets = cabinetApiClient.getCabinetsByExternalIds(MORBIHANSIG, idsSig);
            CrudOperation<Cabinet> crudOperation = treatCabinets(slicedCabinets, cabinets);

            bimApiClient.addMissingIds(crudOperation.getCreate());

            if (!crudOperation.isEmpty()) {
                cmSender.commit(crudOperation);
            }
            createdCount += crudOperation.getCreate().size();
            updatedCount += crudOperation.getUpdate().size();
            deletedCount += crudOperation.getDelete().size();
            comingCabinets.removeAll(slicedCabinets);
            log.info("Processed {} of {} cabinets: {} created, {} updated, {} deleted", cabinetsCount - comingCabinets.size(), cabinetsCount, createdCount, updatedCount, deletedCount);
        } while (!comingCabinets.isEmpty());
    }

    public CrudOperation<Cabinet> treatCabinets(List<CabinetSig> slicedCabinets, List<Cabinet> cabinets) {
        CrudOperation<Cabinet> crudOperation = new CrudOperation<>();
        Set<Cabinet> toCreate = crudOperation.getCreate();
        Set<Cabinet> toUpdate = crudOperation.getUpdate();
        Set<Cabinet> toDelete = crudOperation.getDelete();
        for (CabinetSig cabinetSig : slicedCabinets) {
            boolean isDeleted = Objects.equals(cabinetSig.getDeleted(), "1");
            Optional<Cabinet> dpCabinet = cabinets.stream()
                    .filter(cabinet -> cabinet.getExternalids()
                            .get(MORBIHANSIG)
                            .equals(cabinetSig.getPrimaryKey()))
                    .findFirst();
            if (!isDeleted) {
                if (dpCabinet.isEmpty()) {
                    toCreate.add(sigMorbihanUtils.createCabinet(cabinetSig));
                } else {
                    toUpdate.add(sigMorbihanUtils.updateCabinet(cabinetSig, dpCabinet.get()));
                }
            } else {
                dpCabinet.ifPresent(toDelete::add);
            }
        }
        return crudOperation;
    }
}
