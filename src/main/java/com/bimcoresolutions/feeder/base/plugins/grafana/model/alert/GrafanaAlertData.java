package com.bimcoresolutions.feeder.base.plugins.grafana.model.alert;

import com.bimcoresolutions.feeder.base.plugins.grafana.model.GrafanaCollection;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GrafanaAlertData extends GrafanaCollection {
    private List<GrafanaAlertGroup> groups;
    private String status;
}
