package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.transformerstation.TransformerStationCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.transformerstation.TransformerStationSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.TransformerStationGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.TransformerStation;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.TransformerStationCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;


@Log4j2
@Component
public class SigHaropaTransformerStationHandler extends SigHaropaHandler {
    private final TransformerStationGenFeignClient transformerStationGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaTransformerStationHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, TransformerStationGenFeignClient transformerStationGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.transformerStationGenFeignClient = transformerStationGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "transformerstation";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        TransformerStationCollection input = objectMapper.readValue(fileBody, TransformerStationCollection.class);
        List<TransformerStationSig> comingTransformerStations = input.getFeatures();
        List<TransformerStation> transformerStation = transformerStationGenFeignClient.get(new TransformerStationCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigTransformerStations(transformerStation, comingTransformerStations);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(TransformerStation.class));
        }
    }

    public CrudOperation<Model> treatSigTransformerStations(List<TransformerStation> existingTransformerStations, List<TransformerStationSig> comingTransformerStations) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (TransformerStationSig transformerStationSig : comingTransformerStations) {
            Optional<TransformerStation> optionalTransformerStation = existingTransformerStations.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + transformerStationSig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalTransformerStation.isEmpty()) {
                toAdd.add(sigHaropaUtils.createTransformerStation(transformerStationSig));
            } else {
                optionalTransformerStation.ifPresent(cabinet -> toUpdate.add(sigHaropaUtils.updateTransformerStation(transformerStationSig, cabinet)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.transformerStationsToRemove(comingTransformerStations, existingTransformerStations));

        log.info("SigHaropaService, treatSigTransformerStations results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }
}
