package com.bimcoresolutions.feeder.base.plugins.camtrace.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CameraApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.camtrace.model.CamtraceAlarm;
import com.bimcoresolutions.feeder.base.plugins.camtrace.model.CamtraceAlert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Camera;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.CMUpdRelBodyv2;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NonNull;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.concat;
import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.makeLotsOfRelations;
import static java.util.stream.Collectors.toMap;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

@Log4j2
@Component
public class CamtraceAlertHandler extends DataPlatformHandler {

    public static final String CAMTRACE = "camtrace";
    private final CameraApiClient cameraApiClient;
    private final AlarmApiClient alarmApiClient;
    private static final TypeReference<List<CamtraceAlert>> typereferenceCamtraceAlert = new TypeReference<>() {
    };

    public CamtraceAlertHandler(BimApiClient bimApiClient, CameraApiClient cameraApiClient, ObjectMapper objectMapper, CMSender cmSender, AlarmApiClient alarmApiClient) {
        super(bimApiClient, objectMapper, cmSender);
        this.cameraApiClient = cameraApiClient;
        this.alarmApiClient = alarmApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return "camtrace_alert_v0";
    }

    public static String buildCode(Long id, String streamName) {
        return CAMTRACE + "_" + id + "_" + streamName;
    }

    public static String buildName(Long id, String streamName) {
        return id + "_" + streamName;
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<CamtraceAlert> incomingAlerts = objectMapper.readValue(fileBody, typereferenceCamtraceAlert);

        Set<String> cameraCodes = incomingAlerts.stream().map(alert -> CamtraceCameraHandler.buildCode(alert.getId())).collect(Collectors.toSet());
        List<Camera> existingCameras = isEmpty(cameraCodes) ? List.of() : cameraApiClient.getCameraByCode(cameraCodes);

        List<Alarm> existingAlarms = isEmpty(existingCameras) ? List.of() :
                alarmApiClient.getAlarmByIds(existingCameras.stream().flatMap(cam -> cam.getAlarms().stream())
                        .distinct()
                        .toList());

        CrudOperation<Alarm> crudOperation = treatAlert(incomingAlerts, existingAlarms, existingCameras);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            CrudOperation<Alarm> crud = crudOperation.get(Alarm.class);
            Map<String, List<CMUpdRelBodyv2>> relations = Map.of("Alarm_camera",
                    makeLotsOfRelations(concat(crud.getCreate(), crud.getUpdate()), existingCameras, Alarm::getCamera, Camera::getCode, List.of()));
            cmSender.commitWithRelations(crud, relations);
        }
    }

    public static CrudOperation<Alarm> treatAlert(List<CamtraceAlert> incomingAlerts, List<Alarm> existingAlarms, List<Camera> existingCameras) {
        Set<Alarm> toAdd = new HashSet<>();
        Set<Alarm> toUpdate = new HashSet<>();
        int turnedOff = 0;

        Map<String, CamtraceAlarm> incomingAlarms = incomingAlerts.stream()
                .map(CamtraceAlert::toCamtraceAlarms)
                .flatMap(m -> m.entrySet().stream())
                .collect(toMap(Map.Entry::getKey, Map.Entry::getValue));

        // créé ou update les alarm présentes
        for (Map.Entry<String, CamtraceAlarm> alarm : incomingAlarms.entrySet()) {
            Optional<Alarm> optionalAlarm = existingAlarms.stream()
                    .filter(a -> a.getCode().equals(alarm.getKey()))
                    .findFirst();
            Optional<String> optionalCamera = existingCameras.stream()
                    .filter(c -> c.getCode().equals(CamtraceCameraHandler.buildCode(alarm.getValue().getAlarmId())))
                    .map(Model::get_id_bimcore)
                    .findFirst();

            optionalCamera.ifPresentOrElse(
                    existingCameraForThisAlarm -> optionalAlarm.ifPresentOrElse(
                            lastImage -> toUpdate.add(buildAlarm(alarm.getValue(), lastImage, existingCameraForThisAlarm)),
                            () -> toAdd.add(buildAlarm(alarm.getValue(), null, existingCameraForThisAlarm))
                    ),
                    () -> log.warn("No camera found for {}", alarm.getValue())
            );
        }

        // desactive les alarms plus remontées par camtrace
        existingAlarms.stream()
                .filter(Alarm::getPresence)
                .filter(onAlarm -> !incomingAlarms.containsKey(onAlarm.getCode()))
                .map(CamtraceAlertHandler::turnOffAlarm)
                .forEach(toUpdate::add);

        log.info("{} : {} added, {} updated, {} turned off", CamtraceAlertHandler.class.getSimpleName(), toAdd.size(), toUpdate.size(), turnedOff);
        return new CrudOperation<>(toAdd, toUpdate, new HashSet<>());
    }

    public static Alarm buildAlarm(CamtraceAlarm alarm, Alarm lastImage, @NonNull String idBimCamera) {
        Alarm.AlarmBuilder builder = lastImage != null ? lastImage.toBuilder() : Alarm.builder()
                .code(buildCode(alarm.getAlarmId(), alarm.getStreamName()))
                .name(buildName(alarm.getAlarmId(), alarm.getAlarmName()))
                .source(CAMTRACE)
                .externalids(recomputeExternalIdsCamtrace(alarm.getAlarmId(), lastImage));

        return builder
                .lastupdate(new Date())
                .presence(true)
                .camera(List.of(idBimCamera))
                .build();
    }

    public static Alarm turnOffAlarm(Alarm lastImage) {
        return lastImage.toBuilder()
                .lastupdate(new Date())
                .presence(false)
                .build();
    }

    private static Map<String, Object> recomputeExternalIdsCamtrace(Long externalId, Alarm lastImage) {
        Map<String, Object> externalIds = new HashMap<>();
        if (lastImage != null) {
            externalIds.putAll(lastImage.getExternalids());
        }
        externalIds.put(CAMTRACE, externalId);
        return externalIds;
    }

}
