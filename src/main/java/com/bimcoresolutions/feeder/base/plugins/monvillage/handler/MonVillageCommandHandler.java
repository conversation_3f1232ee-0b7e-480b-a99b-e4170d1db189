package com.bimcoresolutions.feeder.base.plugins.monvillage.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.PacmanClient;
import com.bimcoresolutions.feeder.base.client.bimcity.NotifEventApiClient;
import com.bimcoresolutions.feeder.base.client.pacman.ActionType;
import com.bimcoresolutions.feeder.base.client.pacman.PacManAction;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.monvillage.MonVillageUtils;
import com.bimcoresolutions.feeder.base.plugins.monvillage.model.DataplatformMonVillageCommand;
import com.bimcoresolutions.feeder.base.plugins.monvillage.model.TalqCabinetActionTemplate;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.NotifEvent;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Log4j2
@Component
public class MonVillageCommandHandler extends DataPlatformHandler {
    private final Map<Integer, List<TalqCabinetActionTemplate>> hardCodedCommands;

    public static final String MONVILLAGE = "MonVillage";
    private static final TypeReference<List<DataplatformMonVillageCommand>> typeRef = new TypeReference<>() {
    };
    private static final String HANDLED_ENTITY_NAME = "monvillage_command_v0";

    private final PacmanClient pacmanClient;
    private final NotifEventApiClient notifEventApiClient;
    private final MonVillageUtils monVillageUtils;

    public MonVillageCommandHandler(BimApiClient bimApiClient, NotifEventApiClient notifEventApiClient,
                                    PacmanClient pacmanClient, ObjectMapper objectMapper, CMSender cmSender,
                                    MonVillageUtils monVillageUtils,
                                    @Value("${plugins.monvillage.commands}") String commands) {
        super(bimApiClient, objectMapper, cmSender);
        this.notifEventApiClient = notifEventApiClient;
        this.monVillageUtils = monVillageUtils;
        this.pacmanClient = pacmanClient;
        Map<Integer, List<TalqCabinetActionTemplate>> cmds = null;
        if(commands != null) {
            try {
                cmds = objectMapper.readValue(commands, new TypeReference<>() {});
            } catch(Exception ex) {
                log.error("Error processing commands={}", commands, ex);
            }
        }
        hardCodedCommands = cmds != null ? Map.copyOf(cmds) : Map.of();
        log.info("MonVillage cmds loaded nb={}", hardCodedCommands.size());
    }

    @Override
    public String getHandledEntityName() {
        return HANDLED_ENTITY_NAME;
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        try {
            List<DataplatformMonVillageCommand> commandList = objectMapper.readValue(fileBody, typeRef);
            List<String> uuids = commandList.stream()
                    .map(DataplatformMonVillageCommand::getUuid_command)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();

            List<NotifEvent> existingNotifEvents = notifEventApiClient.getNotifEventByExternalIds(MONVILLAGE, uuids);
            Map<String, NotifEvent> existingMap = existingNotifEvents.stream()
                    .collect(Collectors.toMap(
                            notifEvent -> notifEvent.getExternalids().get(MONVILLAGE).toString(),
                            notifEvent -> notifEvent
                    ));

            treatSlice(commandList, existingMap, 100);

        } catch (JsonProcessingException e) {
            log.error("Error processing JSON: ", e);
            throw e;
        } catch (Exception e) {
            log.error("Unhandled exception: ", e);
            throw new RuntimeException(e);
        }
    }

    private void treatSlice(List<DataplatformMonVillageCommand> incoming, Map<String, NotifEvent> existingMap, int limit) {
        List<List<DataplatformMonVillageCommand>> sliced = ListUtils.partition(incoming, limit);
        sliced.forEach(slice -> {
            try {
                treatSlicedNotif(slice, existingMap);
            } catch (Exception e) {
                if (limit > 1) {
                    treatSlice(slice, existingMap, limit < 10 ? 1 : limit / 10);
                } else {
                    log.error("Error processing slice: {}", slice, e);
                }
            }
        });
    }

    @SneakyThrows
    public void treatSlicedNotif(List<DataplatformMonVillageCommand> incoming, Map<String, NotifEvent> existingMap) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();

        for (DataplatformMonVillageCommand command : incoming) {
            NotifEvent existing = existingMap.get(command.getUuid_command());
            List<String> actionId = new ArrayList<>();
            if (existing == null) {
                List<TalqCabinetActionTemplate> hardCodedCommand = hardCodedCommands.get(command.getId_scenario());
                if (hardCodedCommand == null) {
                    log.warn("Couldn't find a command for idScenario={}", command.getId_scenario());
                } else {
                    for(TalqCabinetActionTemplate cmd : hardCodedCommand) {
                        PacManAction<TalqCabinetActionTemplate> action = PacManAction.<TalqCabinetActionTemplate>builder()
                                .type(ActionType.Standalone)
                                .template("Talq_Cabinet")
                                .settings(cmd)
                                .build();
                        try {
                            log.info("Sending PACMAN cmd={}", objectMapper.writeValueAsString(cmd));
                            actionId.add(pacmanClient.action(action));
                        } catch (Exception ex) {
                            log.error("Error while sending command idScenarion={}, to PACMAN", command.getId_scenario(), ex);
                            actionId.add("PACMAN Error");
                        }
                    }
                }
                toAdd.add(monVillageUtils.createNotifEvent(command, actionId));
            } else {
                log.info("Command already exists uuid : {}, command : {}", command.getUuid_command(), command);
            }
        }

        log.info("treatNotifEvent results - added: {}, updated: {}, deleted: {}", toAdd.size(), toUpdate.size(), toDelete.size());

        CrudOperation<Model> crudOp = new CrudOperation<>(toAdd, toUpdate, toDelete);
        if (!crudOp.isEmpty()) {
            bimApiClient.addMissingIds(crudOp.getCreate());
            cmSender.commit(crudOp.get(NotifEvent.class));
        }
    }
}
