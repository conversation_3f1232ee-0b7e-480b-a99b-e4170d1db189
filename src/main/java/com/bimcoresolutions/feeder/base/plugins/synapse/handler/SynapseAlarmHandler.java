package com.bimcoresolutions.feeder.base.plugins.synapse.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.SynapseClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CameraApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.synapse.model.asset.*;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Camera;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.CMUpdRelBodyv2;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.*;

import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.concat;
import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.makeLotsOfRelations;

@Component
@ConditionalOnProperty(prefix = "input-queue", name = "synapse")
public class SynapseAlarmHandler extends SynapseHandler {
    public static final String SYNAPSE = "synapse";
    public static final String ACTIA = "actia";
    private static final String ALARM_ERROR_LABEL = "Erreur de communication";
    private static final String ALARM_DEFAULT_SUFFIX = "error";
    private final CameraApiClient cameraApiClient;
    private final AlarmApiClient alarmApiClient;
    private final SynapseClient synapseClient;


    public SynapseAlarmHandler(BimApiClient bimApiClient, CameraApiClient cameraApiClient, SynapseClient synapseClient,
                               ObjectMapper objectMapper, CMSender cmSender, AlarmApiClient alarmApiClient) {
        super(bimApiClient, objectMapper, cmSender);
        this.cameraApiClient = cameraApiClient;
        this.alarmApiClient = alarmApiClient;
        this.synapseClient = synapseClient;
    }

    @Override
    public String getHandledEntityName() {
        return "SynapseAlarms";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {

        ScanAlarmCollection input = objectMapper.readValue(fileBody, ScanAlarmCollection.class);
        List<ScanAlarm> comingAlarmsWithIdsBim = buildCamerasByIdsSynapses(input.getAlarms());
        List<String> ids = comingAlarmsWithIdsBim.stream().map(ScanAlarm::getIdSynapse).toList();
        List<Alarm> alarms = alarmApiClient.getAlarmByExternalIds(SYNAPSE, ids);
        List<Camera> cameras = cameraApiClient.getCameraByExternalId(SYNAPSE, ids);

        CrudOperation<Model> crudOperation = treatSynapseAlarm(comingAlarmsWithIdsBim, cameras, alarms);
        bimApiClient.addMissingIds(crudOperation.getCreate());


        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Camera.class));
            CrudOperation<Alarm> crud = crudOperation.get(Alarm.class);
            Map<String, List<CMUpdRelBodyv2>> relations = Map.of(
                    "Alarm_camera", makeLotsOfRelations(concat(crud.getCreate(),
                                    crud.getUpdate()), List.of(), Alarm::getCamera, Camera::getCode,
                            cameras));
            cmSender.commitWithRelations(crudOperation, relations);
        }
    }

    public CrudOperation<Model> treatSynapseAlarm(List<ScanAlarm> comingAlarmsWithIdsBim, List<Camera> cameras, List<Alarm> alarms) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();
        comingAlarmsWithIdsBim.forEach(alarm -> {
            Optional<Camera> optionalCamera = cameras.stream()
                    .filter(c -> c.getExternalids().containsKey(SYNAPSE) && c.getExternalids().get(SYNAPSE)
                            .toString().equals(alarm.getIdSynapse())).findAny();
            optionalCamera.ifPresent(c -> createOrUpdateSynapseAlarm(alarm, c, alarms, toAdd, toUpdate));
        });
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

    public void createOrUpdateSynapseAlarm(ScanAlarm scanAlarm, Camera existingCamera, List<Alarm> alarms,
                                           Set<Model> toAdd, Set<Model> toUpdate) {

        List<Alarm> alarmsList = alarms.stream()
                .filter(a -> a.getCode().equals(existingCamera.getExternalids().get(SYNAPSE).toString() + ALARM_DEFAULT_SUFFIX))
                .toList();

        if (scanAlarm.getErrorCode() != null) {
            EnumSynapseAsset synapseError = EnumSynapseAsset.getByValue(String.valueOf(scanAlarm.getErrorCode()));

            if (synapseError.getValeur().equals("-1")) {
                toUpdate.add(existingCamera.toBuilder().status(-1).build());
                if (alarmsList.isEmpty()) {
                    toAdd.add(createSynapseAlarm(scanAlarm, synapseError, existingCamera));
                } else {
                    alarmsList.forEach(alarm -> {
                        Alarm newCalculated = updateSynapseAlarm(synapseError, alarm);
                        if (!newCalculated.getPresence().equals(alarm.getPresence())) {
                            toUpdate.add(newCalculated);
                        }
                    });
                }
            } else {
                toUpdate.add(existingCamera.toBuilder().status(1).build());
                if (!alarmsList.isEmpty()) {
                    alarmsList.forEach(alarm -> {
                        Alarm newCalculated = updateSynapseAlarm(synapseError, alarm);
                        if (!newCalculated.getPresence().equals(alarm.getPresence())) {
                            toUpdate.add(newCalculated);
                        }
                    });
                }
            }
        }
    }

    private Alarm createSynapseAlarm(ScanAlarm scanAlarm, EnumSynapseAsset synapseError, Camera camera) {
        String name = ALARM_ERROR_LABEL;
        if (synapseError.getErrorCode() != null && !synapseError.getErrorCode().isEmpty()
                && !scanAlarm.getDescription().isEmpty()) {
            name = scanAlarm.getDescription();
        }
        return new Alarm().toBuilder()
                .code(camera.getExternalids().get(SYNAPSE).toString() + ALARM_DEFAULT_SUFFIX)
                .name(name)
                .metier(Map.of())
                .camera(List.of(camera.get_id_bimcore()))
                .lastupdate(new Date())
                .presence(synapseError.getValeur().equals("-1"))
                .externalids(camera.getExternalids())
                .source(ACTIA)
                .build();
    }

    private Alarm updateSynapseAlarm(EnumSynapseAsset synapseError, Alarm alarm) {
        return alarm.toBuilder()
                .lastupdate(new Date())
                .presence(synapseError.getValeur().equals("-1"))
                .build();
    }

    private List<ScanAlarm> buildCamerasByIdsSynapses(List<ScanAlarm> comingAlarms) {
        List<SynapseAsset> cameraSynapses = synapseClient.getAllCamerasSynapse();
        List<ScanAlarm> toReturn = new ArrayList<>();
        for (ScanAlarm alarm : comingAlarms) {
            for (SynapseAsset synapseAsset : cameraSynapses) {
                if (!ObjectUtils.isEmpty(alarm.getId()) && alarm.getId().equals(synapseAsset.getName())) {
                    alarm.setIdSynapse(synapseAsset.getId().toString());
                    toReturn.add(alarm);
                    break;
                }
            }
        }
        return toReturn;
    }

}
