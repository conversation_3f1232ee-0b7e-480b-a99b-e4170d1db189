package com.bimcoresolutions.feeder.base.plugins.monvillage.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TalqCabinetActionTemplate {
    private String code;
    private String provider;
    private Boolean cmd;
    private Integer dureeMinutes;
    private Map<String, TalqCabinetActionTemplateRelais> relais;
}
