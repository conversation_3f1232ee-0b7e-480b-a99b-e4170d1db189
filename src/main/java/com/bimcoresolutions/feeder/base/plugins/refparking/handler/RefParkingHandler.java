package com.bimcoresolutions.feeder.base.plugins.refparking.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ParkingApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.refparking.RefParkingService;
import com.bimcoresolutions.feeder.base.plugins.refparking.model.DataplatformRefParking;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Parking;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Log4j2
@Component
public class RefParkingHandler extends DataPlatformHandler {
    private static final String PARKING = "parking";
    private static final TypeReference<List<DataplatformRefParking>> TYPEREFERENCE_REF_PARKING = new TypeReference<>() {
    };
    private static final String HANDLED_ENTITY_NAME = "generic_ref-parking_v0";
    private final ParkingApiClient parkingApiClient;
    private final RefParkingService refParkingService;

    protected RefParkingHandler(BimApiClient bimApiClient, ParkingApiClient parkingApiClient, RefParkingService refParkingService, ObjectMapper objectMapper, CMSender cmSender) {
        super(bimApiClient, objectMapper, cmSender);
        this.parkingApiClient = parkingApiClient;
        this.refParkingService = refParkingService;
    }

    @Override
    public String getHandledEntityName() {
        return HANDLED_ENTITY_NAME;
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        try {
            List<DataplatformRefParking> refParking = objectMapper.readValue(fileBody, TYPEREFERENCE_REF_PARKING);
            List<String> incomingRefParkingCodes = refParking.stream()
                    .map(DataplatformRefParking::getParkingRef)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();

            List<Parking> existingParkings = parkingApiClient.getParkingByExternalIds(PARKING, incomingRefParkingCodes);
            CrudOperation<Model> crudOperationRefParking = refParkingService.treatParking(existingParkings, refParking);
            bimApiClient.addMissingIds(crudOperationRefParking.getCreate());

            if (!crudOperationRefParking.isEmpty()) {
                cmSender.commit(crudOperationRefParking);
            }
        } catch (JsonProcessingException e) {
            log.error("Error processing JSON in handle method: ", e);
            throw e;
        } catch (Exception e) {
            log.error("Unhandled exception in handle method: ", e);
            throw new RuntimeException(e);
        }
    }
}
