package com.bimcoresolutions.feeder.base.plugins.connectorstate.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ConnectorStateApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.connectorstate.ConnectorStateService;
import com.bimcoresolutions.feeder.base.plugins.connectorstate.model.InputConnectorState;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.ConnectorState;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class DefaultConnectorStateHandler extends ConnectorStateHandler {

    private final ConnectorStateService connectorStateService;
    private final ConnectorStateApiClient connectorStateApiClient;

    public DefaultConnectorStateHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, ConnectorStateService connectorStateService,
            ConnectorStateApiClient connectorStateApiClient) {
        super(bimApiClient, objectMapper, cmSender);
        this.connectorStateService = connectorStateService;
        this.connectorStateApiClient = connectorStateApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return "default";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        InputConnectorState input = objectMapper.readValue(fileBody, InputConnectorState.class);
        List<ConnectorState> connectorStates = connectorStateApiClient.getConnectorStateByCode(input.getConnectorName());

        CrudOperation<Model> crudOperation = connectorStateService.treatConnectorState(connectorStates, input);

        if (!crudOperation.isEmpty()) {
            bimApiClient.addMissingIds(crudOperation.getCreate());
            cmSender.commit(crudOperation);
        }
    }

}
