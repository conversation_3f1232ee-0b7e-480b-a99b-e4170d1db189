package com.bimcoresolutions.feeder.base.plugins.synapse.model.asset;

public enum EnumSynapseAsset {
    NEUTRAL("1", "OK"),
    DISCONNECTED("-1", "KO"),
    DEFAULT("0", "");

    private final String value;
    private final String errorCode;

    EnumSynapseAsset(String value, String errorCode) {
        this.value = value;
        this.errorCode = errorCode;
    }

    public String getValeur() {
        return value;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public static EnumSynapseAsset getByValue(String value) {
        for (EnumSynapseAsset e : EnumSynapseAsset.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return EnumSynapseAsset.DEFAULT;
    }
}
