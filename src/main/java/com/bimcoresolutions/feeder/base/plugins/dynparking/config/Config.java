package com.bimcoresolutions.feeder.base.plugins.dynparking.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Data
@ConfigurationProperties(prefix = "plugins.dynparking")
public class Config {

    private List<PjdConfig> pjds = new ArrayList<>();

}

