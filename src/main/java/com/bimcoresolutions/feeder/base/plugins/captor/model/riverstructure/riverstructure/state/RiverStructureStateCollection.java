package com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructure.state;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RiverStructureStateCollection extends CaptorCollection {
    private List<RiverStructureState> items;
    private String typeObject;
}

