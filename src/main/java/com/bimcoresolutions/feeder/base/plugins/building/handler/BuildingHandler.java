package com.bimcoresolutions.feeder.base.plugins.building.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.BuildingApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.MeterApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.building.model.BuildingDP;
import com.bimcoresolutions.feeder.base.plugins.building.util.BuildingUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Building;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Meter;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.bimcoresolutions.feeder.base.plugins.building.util.BuildingUtils.BUILDING_ID;
import static com.bimcoresolutions.feeder.base.plugins.building.util.BuildingUtils.METER_ID;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

@Log4j2
@Component
public class BuildingHandler extends DataPlatformHandler {

    private final BuildingApiClient buildingApiClient;
    private final BuildingUtils buildingUtils;
    private final MeterApiClient meterApiClient;

    protected BuildingHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, BuildingApiClient buildingApiClient, BuildingUtils buildingUtils, MeterApiClient meterApiClient) {
        super(bimApiClient, objectMapper, cmSender);
        this.buildingApiClient = buildingApiClient;
        this.buildingUtils = buildingUtils;
        this.meterApiClient = meterApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return "generic_building_v1";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<BuildingDP> incomingBuildings = objectMapper.readValue(fileBody, new TypeReference<>() {
        });

        List<String> incomingBuildingsCodes = incomingBuildings.stream().map(BuildingDP::getCode).toList();
        List<String> incomingBuildingRelatedMetersIds = incomingBuildings.stream().filter(b -> !Objects.equals(b.getExternalids().get(METER_ID).toString(), "")).map(b -> b.getExternalids().get(METER_ID).toString()).distinct().toList();
        List<Building> existingBuildings = isEmpty(incomingBuildingsCodes) ? List.of() : buildingApiClient.getBuildingsByExternalIds(BUILDING_ID, incomingBuildingsCodes);
        List<Meter> relatedMeters = isEmpty(incomingBuildingsCodes) ? List.of() : meterApiClient.getMetersByExternalIds(METER_ID, incomingBuildingRelatedMetersIds);

        CrudOperation<Model> crudOperation = treatBuildings(incomingBuildings, existingBuildings);

        bimApiClient.addMissingIds(crudOperation.getCreate());

        if (!crudOperation.isEmpty()) {
            CrudOperation<Building> buildingCrudOperation = crudOperation.get(Building.class);
            buildingApiClient.updateBuildingAndRelations(buildingCrudOperation, relatedMeters);
        }
    }

    public CrudOperation<Model> treatBuildings(List<BuildingDP> items, List<Building> existingBuildings) {
        Set<Model> toAdd = new HashSet<>(), toUpdate = new HashSet<>(), toDelete;
        List<String> existingCodes = existingBuildings.stream().map(Building::getCode).toList();

        for (BuildingDP buildingDP : items) {
            if (existingCodes.contains(buildingDP.getCode())) {
                buildingUtils.updateBuilding(buildingDP, existingBuildings.stream().filter(b -> buildingDP.getCode().equals(b.getCode())).toList().getFirst(), toUpdate);
            } else {
                buildingUtils.createBuilding(buildingDP, toAdd, toUpdate);
            }
        }
        toDelete = new HashSet<>(buildingUtils.buildingsToRemove(new HashSet<>(items), new HashSet<>(existingBuildings)));

        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }
}
