package com.bimcoresolutions.feeder.base.plugins.captor.model.cameraevent;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CameraEvent {
    private Long event;
    private Long camera;
    @JsonProperty("cameraName")
    private String cameraName;
    @JsonProperty("groupeName")
    private String groupeName;
    private Long start_time;
    private Long end_time;
    private String type;
    private Integer confidence;
    private String comment;
    private Long updated_at;
}
