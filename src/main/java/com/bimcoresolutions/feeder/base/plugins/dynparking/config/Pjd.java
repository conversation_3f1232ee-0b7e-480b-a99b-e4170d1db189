package com.bimcoresolutions.feeder.base.plugins.dynparking.config;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
public class Pjd {
    private String id;
    private final List<Parking> parkings = new ArrayList<>();

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Pjd{");
        sb.append("id='").append(id).append('\'');
        sb.append(", parkings=").append(parkings.stream().map(Parking::getId).toList());
        sb.append('}');
        return sb.toString();
    }
}
