package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.cabinet;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CabinetSig {
    String id;
    String geom;

    @JsonProperty("cle_primaire")
    String primaryKey;

    @JsonProperty("gestionnaire")
    String manager;

    @JsonProperty("commune")
    String city;

    @JsonProperty("code_armoire")
    String nameAsset;

    @JsonProperty("adresse")
    String street;

    @JsonProperty("date_pose")
    String installationDate;

    @JsonProperty("telegestion_proprio")
    String owner;

    @JsonProperty("nb_depart")
    String circuitQuantity;

    @JsonProperty("pdl")
    String deliveryPoint;

    @JsonProperty("puissance_souscrite")
    String puissanceSouscrite;

    @JsonProperty("puissance_mesuree")
    String puissanceMesuree;

    @JsonProperty("modele_horloge")
    String modeleHorloge;

    @JsonProperty("marque_horloge")
    String marqueHorloge;

    String departs;

    @JsonProperty("date_fin_abonnement_com")
    String dateFinAbonnementCom;

    @JsonProperty("date_fin_abonnement_supervision")
    String dateFinAbonnementSupervision;

    @JsonProperty("datemodification")
    String dateModification;

    @JsonProperty("gi_deleted")
    String deleted;

    @JsonProperty("adresse_telegestion")
    String telegestionaddress;

    @JsonProperty("code_gestionnaire")
    String codeGestionnaire;

    Boolean telegestion;

    @JsonProperty("regles_commande")
    String reglesCommande;

    String epsg;
}
