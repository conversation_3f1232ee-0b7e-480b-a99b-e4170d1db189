package com.bimcoresolutions.feeder.base.plugins.axiansia.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlertApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.axiansia.AxiansiaUtils;
import com.bimcoresolutions.feeder.base.plugins.axiansia.model.EnumAxiansia;
import com.bimcoresolutions.feeder.base.plugins.axiansia.model.InputAxiansIa;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.CMUpdRelBodyv2;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.concat;
import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.makeLotsOfRelations;

@Log4j2
@Component
public class AxiansiaHandler extends DataPlatformHandler {

    private static final TypeReference<List<InputAxiansIa>> typereferenceAxiansIa = new TypeReference<>() {
    };
    private final AlertApiClient alertApiClient;
    private final AxiansiaUtils axiansiaUtils;
    private final LightingPointApiClient lightingPointApiClient;

    public AxiansiaHandler(CMSender cmSender, BimApiClient bimApiClient, ObjectMapper objectMapper, AlertApiClient alertApiClient, AxiansiaUtils axiansiaUtils,
            LightingPointApiClient lightingPointApiClient) {
        super(bimApiClient, objectMapper, cmSender);
        this.alertApiClient = alertApiClient;
        this.axiansiaUtils = axiansiaUtils;
        this.lightingPointApiClient = lightingPointApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return "axiansia_dataia_v1";
    }

    @Override
    public void handle(String filebody) throws JsonProcessingException {
        List<InputAxiansIa> axiansAlert = objectMapper.readValue(filebody, typereferenceAxiansIa);

        List<Alert> existingAlerts = alertApiClient.getAlertByNotClosedAndSameCategory(EnumAxiansia.failure.getName());

        List<String> lampNames = axiansAlert.stream()
                .map(InputAxiansIa::getName)
                .distinct()
                .toList();
        List<LightingPoint> lpsKnown = lightingPointApiClient.getLightingPointByLamps(lampNames);
        CrudOperation<Model> crudOperation = treatAlerts(existingAlerts, lpsKnown, axiansAlert);
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(LightingPoint.class));

            Map<String, List<CMUpdRelBodyv2>> relations = new HashMap<>();

            CrudOperation<Alert> crudAlert = crudOperation.get(Alert.class);
            relations.put("Alert_lightingpoint", makeLotsOfRelations(concat(crudAlert.getCreate(), crudAlert.getUpdate()), List.of(), Alert::getLightingpoint, LightingPoint::getCode, lpsKnown));

            cmSender.commitWithRelations(crudOperation, relations);
        }
    }

    public CrudOperation<Model> treatAlerts(List<Alert> existingAlerts, List<LightingPoint> lpsKnown,
                                                  List<InputAxiansIa> incomingAxiansIaAlerts) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();

        for(InputAxiansIa inputAxiansIa : incomingAxiansIaAlerts) {
            String comment = axiansiaUtils.buildComments(inputAxiansIa);
            if(comment == null) {
                continue;
            }
            // Si on ne connait pas le PL, on ignore
            Optional<LightingPoint> optLp = lpsKnown.stream().filter(lightingPoint -> lightingPoint.getLamps().containsKey(inputAxiansIa.getName())).findFirst();
            if (optLp.isEmpty()) {
                continue;
            }
            LightingPoint lp = optLp.get();
            Optional<Alert> optionalAlertFromAlerts = existingAlerts.stream()
                    .filter(alert -> alert.getLightingpoint().contains(lp.get_id_bimcore()))
                    .findFirst();
            if(optionalAlertFromAlerts.isEmpty()) {
                toAdd.add(axiansiaUtils.createAlert(inputAxiansIa, EnumAxiansia.failure, lp, comment));
                bimApiClient.addMissingIds(toAdd);
            }else{
                optionalAlertFromAlerts.ifPresent(alert -> toUpdate.add(axiansiaUtils.updateAlert(inputAxiansIa, lp, alert, comment)));
            }
        }
        return new CrudOperation<>(toAdd,toUpdate,toDelete);
    }
}
