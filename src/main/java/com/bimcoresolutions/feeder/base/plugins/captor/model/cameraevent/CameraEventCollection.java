package com.bimcoresolutions.feeder.base.plugins.captor.model.cameraevent;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CameraEventCollection extends CaptorCollection {
    private List<CameraEvent> items;
    private String typeObject;
}
