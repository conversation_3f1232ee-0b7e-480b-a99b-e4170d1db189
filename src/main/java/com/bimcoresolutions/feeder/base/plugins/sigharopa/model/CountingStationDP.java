package com.bimcoresolutions.feeder.base.plugins.sigharopa.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Geometry;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CountingStationDP {
    private String id;
    private String id_sig;
    private String site;
    private String name;
    private String sens;
    private String model;
    private String type;
    private Geometry geometry;
    private String id_zone;
    private Date installationdate;
}

