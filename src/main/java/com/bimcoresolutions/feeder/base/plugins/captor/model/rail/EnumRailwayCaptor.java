package com.bimcoresolutions.feeder.base.plugins.captor.model.rail;

import java.util.Arrays;
import java.util.Objects;

public enum EnumRailwayCaptor {

    DEFAULT("0", ""),
    CHUTE_INTEMPESTIVE_1("1", "Chute intempestive - CDV"),
    ZONE_CHUTEE_AP_CIRCULATION_2("2", "Zone chutée après circulation"),
    JOINT_DEFAILLANT_3("3", "Joint défaillant - CDV"),
    SEQUENCE_ANORMALE_4("4", "Séquence anormale - CDV"),
    RISQUE_DESHUNTAGE_5("5", "Risque de deshuntage"),
    RISQUE_DESHUNTAGE_BRANCHE_DEVIEE_6("6", "Risque déshuntage branche déviée"),
    RISQUE_DESHUNTAGE_BRANCHE_DIRECT_7("7", "Risque déshuntage branche direct"),
    CHUTE_INTEMP_ZONE_COURTE_SUP_6S_8("8", "Chute intemp zone courte PN > 6s"),
    OCCUPATION_PROLONGEE_9("9", "Occupation prolongée - CDV"),
    CHUTE_INTEMP_ZONE_COURTE_SUP_3S_10("10", "Chute intemp zone courte PN > 3s"),
    CHUTE_INTEMPESTIVE_11("11", "Chute intempestive - CDV"),
    ZONE_CHUTEE_AP_CIRCULATION_12("12", "Zone chutée après circulation"),
    SEQUENCE_ANORMALE_13("13", "Séquence anormale - CDV"),
    CHUTE_INTEMPESTIVE_ZONE_COURTE_14("14", "Chute intempestive zone courte"),
    OCCUPATION_PROLONGEE_15("15", "Occupation prolongée - CDV");

    private final String value;
    private final String label;

    EnumRailwayCaptor(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValeur() {
        return value;
    }

    public String getErrorCode() {
        return label;
    }


    public static EnumRailwayCaptor getByValue(String value) {
        return Arrays.stream(EnumRailwayCaptor.values())
                .filter(e -> Objects.equals(e.value, value)).findFirst()
                .orElse(DEFAULT);
    }
}
