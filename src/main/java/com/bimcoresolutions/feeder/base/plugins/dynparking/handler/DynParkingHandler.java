package com.bimcoresolutions.feeder.base.plugins.dynparking.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.dynparking.config.*;
import com.bimcoresolutions.feeder.base.plugins.dynparking.model.DataplatformDynParking;
import com.bimcoresolutions.project.bimsocle.util.bimutil.configuration.util.exceptions.InitializationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.hc.client5.http.classic.methods.HttpPut;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.core5.http.HttpResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.util.*;

@Log4j2
@Component
public class DynParkingHandler extends DataPlatformHandler {
    private static final TypeReference<List<DataplatformDynParking>> TYPEREFERENCE_DYN_PARKING = new TypeReference<>() {
    };
    private static final String HANDLED_ENTITY_NAME = "generic_dyn-parking_v0";
    private static final Integer LINE_NUMBER_PJD = 7;
    private final String url;
    private final Map<String, Pjd> pjdById = new HashMap<>();
    private final Map<String, Parking> parkingById = new HashMap<>();

    public DynParkingHandler(BimApiClient bimApiClient,
                             ObjectMapper objectMapper,
                             CMSender cmSender,
                             @Value("${plugins.segur.pmv.url}") String url,
                             Config pjds) {
        super(bimApiClient, objectMapper, cmSender);
        initMap(pjds);
        this.url = Optional.ofNullable(url).orElseThrow(() -> new InitializationException("configuration missing at plugins.segur.pmv.url"));
    }

    public void initMap(Config pjds) {

        for (PjdConfig pjdConfig : pjds.getPjds()) {
            Pjd newPjd = new Pjd(pjdConfig.getId());
            Optional.ofNullable(pjdConfig.getParkings()).ifPresent(parkings -> {
                for (String parkingId : parkings) {
                    Parking parking = parkingById.getOrDefault(parkingId, new Parking(parkingId));
                    parking.getPjds().add(newPjd);
                    newPjd.getParkings().add(parking);
                    parkingById.put(parkingId, parking);
                }
                pjdById.put(pjdConfig.getId(), newPjd);
            });
        }
    }

    @Override
    public String getHandledEntityName() {
        return HANDLED_ENTITY_NAME;
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {

        try {
            List<DataplatformDynParking> dynParkings = objectMapper.readValue(fileBody, TYPEREFERENCE_DYN_PARKING);
            for (DataplatformDynParking dynparking : dynParkings) {
                Optional.ofNullable(parkingById.get(dynparking.getParkingRef()))
                        .ifPresent(x -> x.setValue(dynparking.getTotalFreeSpaces().toString()));
            }
            commandPjd();

        } catch (JsonProcessingException e) {
            log.error("Error processing JSON in handle method: ", e);
            throw e;
        } catch (Exception e) {
            log.error("Unhandled exception in handle method: ", e);
            throw new RuntimeException(e);
        }
    }

    public void commandPjd() {

        for (Pjd pjd : this.pjdById.values()) {
            StringBuilder messageBuilder = new StringBuilder();
            List<Parking> parkings = pjd.getParkings();

            for (Parking parking : parkings) {
                String value = parking.getValue();
                if (value != null) {
                    messageBuilder.append(value);
                    int padding = LINE_NUMBER_PJD - value.length();
                    if (padding > 0) {
                        messageBuilder.append(" ".repeat(padding));
                    }
                } else {
                    messageBuilder.append(" ".repeat(LINE_NUMBER_PJD));
                }
            }
            sendToSegurApi(pjd.getId(), messageBuilder.toString());
        }

    }

    public void sendToSegurApi(String id, String message) {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(url)
                .queryParam("numTerrain", id)
                .queryParam("message", message)
                .queryParam("duration", "5")
                .queryParam("numPage", "1")
                .queryParam("typeAffichage", "1")
                .queryParam("enable", "true");
        String query = uriBuilder.build(false).encode().toString();
        try (CloseableHttpClient client = httpClientBuilder.build()) {
            HttpResponse httpResponse = client.execute(new HttpPut(query));
            boolean status = HttpStatus.valueOf(httpResponse.getCode()).is2xxSuccessful();
            if (!status) {
                log.error("Query {}",query);
                log.error("Url {} message pmv : {} id pmv : {}",url, message, id);
                log.error("Problème API Segur code HTTP : {} message : {}", httpResponse.getCode(), httpResponse.getReasonPhrase());
            }
        } catch (IOException e) {
            log.error("Error while trying HTTP PUT request on segur intersection, the API may be unreachable : {}", e);
            throw new RuntimeException(e);
        }
    }
}

