package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.railway.RailwayCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.railway.RailwaySig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.RailwayGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Railway;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailwayCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaRailWayHandler extends SigHaropaHandler {
    private final RailwayGenFeignClient railwayGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaRailWayHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, RailwayGenFeignClient railwayGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.railwayGenFeignClient = railwayGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "railway";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        RailwayCollection input = objectMapper.readValue(fileBody, RailwayCollection.class);
        List<RailwaySig> comingRailways = input.getFeatures();
        List<Railway> railways = railwayGenFeignClient.get(new RailwayCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigRailWays(railways, comingRailways);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Railway.class));
        }
    }

    public CrudOperation<Model> treatSigRailWays(List<Railway> existingRailWay, List<RailwaySig> comingRailway) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (RailwaySig railwaySig : comingRailway) {
            Optional<Railway> optionalRailway = existingRailWay.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + railwaySig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalRailway.isEmpty()) {
                toAdd.add(sigHaropaUtils.createRailway(railwaySig));
            } else {
                optionalRailway.ifPresent(railway -> toUpdate.add(sigHaropaUtils.updateRailway(railwaySig, railway)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.railwaysToRemove(comingRailway, existingRailWay));

        log.info("SigHaropaService, treatSigRailways results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
