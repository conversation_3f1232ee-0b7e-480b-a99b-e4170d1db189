package com.bimcoresolutions.feeder.base.plugins.tellmycity.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ReportApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.connectorstate.ConnectorStateService;
import com.bimcoresolutions.feeder.base.plugins.tellmycity.TellMyCityUtils;
import com.bimcoresolutions.feeder.base.plugins.tellmycity.model.DataplatformTellMyCityCitizenAlert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Report;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.bimcoresolutions.feeder.base.plugins.connectorstate.model.ConnectorStateStatus.ok;

@Log4j2
@Component
public class TellMyCityCitizenAlertHandler extends DataPlatformHandler {

    public static final String TELLMYCITY = "tellmycity";
    private static final TypeReference<List<DataplatformTellMyCityCitizenAlert>> typereferenceTellMyCityCitizenAlert = new TypeReference<>() {
    };
    private final ReportApiClient reportApiClient;
    private final TellMyCityUtils tellMyCityUtils;
    private final ConnectorStateService connectorStateService;

    public TellMyCityCitizenAlertHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, ReportApiClient reportApiClient, TellMyCityUtils tellMyCityUtils,
            ConnectorStateService connectorStateService) {
        super(bimApiClient, objectMapper, cmSender);
        this.reportApiClient = reportApiClient;
        this.tellMyCityUtils = tellMyCityUtils;
        this.connectorStateService = connectorStateService;
    }

    @Override
    public String getHandledEntityName() {
        return "tellmycity_citizenalert_v0";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<DataplatformTellMyCityCitizenAlert> tellMyCityCitizenAlerts = objectMapper.readValue(fileBody, typereferenceTellMyCityCitizenAlert);
        List<String> incomingTellMyCityCitizenAlertsCodes = tellMyCityCitizenAlerts.stream()
                .map(DataplatformTellMyCityCitizenAlert::getSourcecode)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        connectorStateService.handleConnectorState(ok, TELLMYCITY.toUpperCase());
        handleReports(incomingTellMyCityCitizenAlertsCodes, tellMyCityCitizenAlerts);
    }

    private void handleReports(List<String> incomingTellMyCityCitizenAlertsCodes, List<DataplatformTellMyCityCitizenAlert> tellMyCityCitizenAlerts) {
        List<Report> existingReports = reportApiClient.getReportByExternalIds(TELLMYCITY, incomingTellMyCityCitizenAlertsCodes);
        CrudOperation<Model> crudOperationTellMyCity = treatReport(existingReports, tellMyCityCitizenAlerts);
        bimApiClient.addMissingIds(crudOperationTellMyCity.getCreate());

        if (!crudOperationTellMyCity.isEmpty()) {
            cmSender.commit(crudOperationTellMyCity.get(Report.class));
        }
    }

    public CrudOperation<Model> treatReport(List<Report> existingReports, List<DataplatformTellMyCityCitizenAlert> incomingCitizenAlerts) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();

        for (DataplatformTellMyCityCitizenAlert tellMyCityCitizenAlert : incomingCitizenAlerts) {
            Optional<Report> optionalReportFromReports = existingReports.stream()
                    .filter(a -> a.getCode().equals(tellMyCityCitizenAlert.getSourcecode()))
                    .findFirst();

            if (optionalReportFromReports.isEmpty()) {
                toAdd.add(tellMyCityUtils.createReport(tellMyCityCitizenAlert));
            } else {
                optionalReportFromReports.ifPresent(report -> toUpdate.add(tellMyCityUtils.updateReport(report, tellMyCityCitizenAlert)));
            }
        }

        log.info("TellMyCityRuleService, treatReport results : {} - added - {} updated", toAdd.size(), toUpdate.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
