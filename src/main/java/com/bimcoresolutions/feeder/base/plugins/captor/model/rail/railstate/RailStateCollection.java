package com.bimcoresolutions.feeder.base.plugins.captor.model.rail.railstate;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RailStateCollection extends CaptorCollection {
    private List<RailState> items;
    private String typeObject;
}

