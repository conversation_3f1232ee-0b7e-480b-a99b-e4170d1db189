package com.bimcoresolutions.feeder.base.plugins.captor.model.rail;

import java.util.Arrays;
import java.util.Objects;

public enum EnumRailCrossingCaptor {

    DEFAULT("0", ""),
    FERMETURE_BARRIERE_ENTREE_1("1", "Fermeture Barrière entrée - PN"),
    EXTINCTION_PLUSIEURS_FEUX_2("2", "Extinction plusieurs feux - PN"),
    BRIS_BARRIERE_ENTREE_FEUX_OK_3("3", "<PERSON><PERSON> barrière entrée + feux OK - PN"),
    BRIS_BARRIERE_ENTREE_FEUX_ETEINT_4("4", "Bris barrière entree+feux éteint - PN"),
    BRIS_BARRIERE_SANS_INFO_FEUX_5("5", "Bris barrière sans info sur feux - PN"),
    FERMETURE_PROLONGEE_6("6", "Fermeture prolongée du PN"),
    FERMETURE_BARRIERE_PN_TROP_COURT_7("7", "Fermeture barrière PN trop court - PN"),
    EXTINCTION_FEU_8("8", "Extinction d'un feu - PN"),
    ANNONCE_INTEMPESTIVE_9("9", "Annonce intempestive PN"),
    DEFAUT_MECANISME_BARRIERES_10("10", "Défaut mécanisme barrières PN"),
    ANOMALIE_MECANISME_BARRIERES_11("11", "Anomalie mécanisme barrières PN"),
    SOULEVEMENT_BARRIERE_12("12", "Soulèvement de barrière PN"),
    KLPN_13("13", "KLPN"),
    INHIBITION_FERM_PROLONGEE_14("14", "Inhibition Ferm. Prolongee PN");

    private final String value;
    private final String label;

    EnumRailCrossingCaptor(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValeur() {
        return value;
    }

    public String getLabel() {
        return label;
    }


    public static EnumRailCrossingCaptor getByValue(String value) {
        return Arrays.stream(EnumRailCrossingCaptor.values())
                .filter(e -> Objects.equals(e.value, value)).findFirst()
                .orElse(DEFAULT);
    }
}
