package com.bimcoresolutions.feeder.base.plugins.captor.model.cameradefault;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CameraDefaultCollection extends CaptorCollection {
    private List<CameraDefault> items;
    private String typeObject;
}
