package com.bimcoresolutions.feeder.base.plugins.sogelink.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.EventApiClient;
import com.bimcoresolutions.feeder.base.models.event.EventCategory;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.sogelink.model.DataplatformSogelinkEvent;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Event;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.simplify.TopologyPreservingSimplifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.*;

import static com.bimcoresolutions.feeder.base.plugins.PluginUtils.replaceStringNullWithEmpty;

@Log4j2
@Component
public class SogelinkEventHandler extends DataPlatformHandler {

    public static final String SOGELINK = "sogelink";
    private static final TypeReference<List<DataplatformSogelinkEvent>> typereferenceSogelinkEvent = new TypeReference<>() {
    };
    private final EventApiClient eventApiClient;

    public SogelinkEventHandler(BimApiClient bimApiClient, EventApiClient eventApiClient, ObjectMapper objectMapper, CMSender cmSender) {
        super(bimApiClient, objectMapper, cmSender);
        this.eventApiClient = eventApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return "sogelink_event_v0";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<DataplatformSogelinkEvent> sogelinkEvents = objectMapper.readValue(fileBody, typereferenceSogelinkEvent);
        List<String> sogelinkEventCodes = sogelinkEvents.stream()
                .map(DataplatformSogelinkEvent::getIdchantier)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        handleEvents(sogelinkEventCodes, sogelinkEvents);
    }

    private void handleEvents(List<String> sogelinkEventCodes, List<DataplatformSogelinkEvent> sogelinkEvents) {
        List<Event> existingEvents = eventApiClient.getEventByExternalIds(SOGELINK, sogelinkEventCodes);
        List<String> existingEventCategoryTravaux = eventApiClient.getEventCategoriesByLabel("Travaux");

        String eventCategoryNameTravaux;
        if (CollectionUtils.isEmpty(existingEventCategoryTravaux)) {
            eventCategoryNameTravaux = "Travaux";
            eventApiClient.createEventCategory(List.of(createEventCategoryTravaux()));
        } else {
            eventCategoryNameTravaux = existingEventCategoryTravaux.stream().findFirst().get();
        }

        CrudOperation<Model> crudOperationSogelink = treatEvent(existingEvents, sogelinkEvents, eventCategoryNameTravaux);
        bimApiClient.addMissingIds(crudOperationSogelink.getCreate());

        if (!crudOperationSogelink.isEmpty()) {
            cmSender.commit(crudOperationSogelink);
        }
    }

    public CrudOperation<Model> treatEvent(List<Event> existingEvents, List<DataplatformSogelinkEvent> incomingSogelinkEvents, String eventCategoryNameTravaux) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();

        for (DataplatformSogelinkEvent sogelinkEvent : incomingSogelinkEvents) {
            Optional<Event> optionalEventFromEvents = existingEvents.stream()
                    .filter(a -> a.getCode().equals(sogelinkEvent.getIdchantier()))
                    .findFirst();

            if (optionalEventFromEvents.isEmpty()) {
                toAdd.add(createEvent(sogelinkEvent, eventCategoryNameTravaux));
            } else {
                optionalEventFromEvents.ifPresent(event -> toUpdate.add(updateEvent(event, sogelinkEvent, eventCategoryNameTravaux)));
            }
        }

        log.info("SogelinkService, treatEvent results : {} - added - {} updated", toAdd.size(), toUpdate.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

    private Event createEvent(DataplatformSogelinkEvent dataplatformSogelinkEvent, String eventCategoryName) {
        String value = dataplatformSogelinkEvent.getIdchantier();
        Map<String, Object> eventMetiers = Map.of("metiers", List.of("Eclairage Public"));

        Event event = new Event().toBuilder()
                .code(replaceStringNullWithEmpty(value))
                .location(getSimplifiedGeometryFromString(dataplatformSogelinkEvent.getGeometry()))
                .source(SOGELINK)
                .metier(eventMetiers)
                .label(replaceStringNullWithEmpty(dataplatformSogelinkEvent.getNumconsultation()) + " " + replaceStringNullWithEmpty(dataplatformSogelinkEvent.getTypemodelelibelle()))
                .site(replaceStringNullWithEmpty(dataplatformSogelinkEvent.getAdresse()))
                .comment(replaceStringNullWithEmpty(dataplatformSogelinkEvent.getDescriptionChantier()))
                .category(eventCategoryName)
                .status(ZonedDateTime.now().isAfter(dataplatformSogelinkEvent.getDatedebut()) ? "InProgress" : "NotStarted")
                .priority(3)
                .creator(dataplatformSogelinkEvent.getEmetteursociete())
                .creationdate(Date.from(dataplatformSogelinkEvent.getDatecreation().toInstant()))
                .realstartdate(Date.from(dataplatformSogelinkEvent.getDatedebut().toInstant()))
                .realenddate(Date.from(dataplatformSogelinkEvent.getDatefin().toInstant()))
                .build();
        if (value != null) {
            Map<String, Object> ids = new HashMap<>();
            ids.put(SOGELINK, replaceStringNullWithEmpty(value));
            event.setExternalids(ids);
        }
        return event;
    }

    private Event updateEvent(Event existingEvent, DataplatformSogelinkEvent dataplatformSogelinkEvent, String eventCategoryName) {
        String value = dataplatformSogelinkEvent.getIdchantier();
        Map<String, Object> eventMetiers = Map.of("metiers", List.of("Eclairage Public"));
        Event event = existingEvent.toBuilder()
                .location(getSimplifiedGeometryFromString(dataplatformSogelinkEvent.getGeometry()))
                .metier(eventMetiers)
                .label(replaceStringNullWithEmpty(dataplatformSogelinkEvent.getNumconsultation()) + " " + replaceStringNullWithEmpty(dataplatformSogelinkEvent.getTypemodelelibelle()))
                .site(replaceStringNullWithEmpty(dataplatformSogelinkEvent.getAdresse()))
                .comment(replaceStringNullWithEmpty(dataplatformSogelinkEvent.getDescriptionChantier()))
                .category(eventCategoryName)
                .creator(dataplatformSogelinkEvent.getEmetteursociete())
                .creationdate(Date.from(dataplatformSogelinkEvent.getDatecreation().toInstant()))
                .realstartdate(Date.from(dataplatformSogelinkEvent.getDatedebut().toInstant()))
                .realenddate(Date.from(dataplatformSogelinkEvent.getDatefin().toInstant()))
                .build();

        if (value != null) {
            Map<String, Object> ids = new HashMap<>();
            ids.put(SOGELINK, replaceStringNullWithEmpty(value));
            event.setExternalids(ids);
        }
        return event;
    }

    private EventCategory createEventCategoryTravaux() {
        return EventCategory.builder()
                .label("Travaux")
                .criticality(3)
                .logo("mdi-vlc")
                .build();
    }

    @SneakyThrows
    private Geometry getSimplifiedGeometryFromString(String coordinatesString) {
        if (StringUtils.isEmpty(coordinatesString)) {
            return null;
        }
        return TopologyPreservingSimplifier.simplify(new WKTReader().read(coordinatesString), 0.00005);
    }

}
