package com.bimcoresolutions.feeder.base.plugins.camtrace.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CameraApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.camtrace.model.DataplatformCamtraceCamera;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Camera;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

import static org.apache.commons.collections4.CollectionUtils.isEmpty;

@Log4j2
@Component
public class CamtraceCameraHandler extends DataPlatformHandler {

    public static final String CAMTRACE = "camtrace";

    private final CameraApiClient cameraApiClient;
    private static final TypeReference<List<DataplatformCamtraceCamera>> typereferenceCamtraceCamera = new TypeReference<>() {
    };

    protected CamtraceCameraHandler(BimApiClient bimApiClient, CameraApiClient cameraApiClient, ObjectMapper objectMapper, CMSender cmSender) {
        super(bimApiClient, objectMapper, cmSender);
        this.cameraApiClient = cameraApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return "camtrace_camera_v0";
    }

    public static String buildCode(Long id) {
        return CAMTRACE + "_" + id;  // TODO : à modifier selon la spec SAGA car c'est SAGA le référentiel
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<DataplatformCamtraceCamera> incomingCameras = objectMapper.readValue(fileBody, typereferenceCamtraceCamera);

        List<String> cameraCodes = incomingCameras.stream()
                .filter(it -> it.getId() != null)
                .map(it -> buildCode(it.getId()))
                .distinct()
                .toList();
        List<Camera> existingCameras = isEmpty(incomingCameras) ? List.of() : cameraApiClient.getCameraByCode(cameraCodes);

        CrudOperation<Camera> crudOperation = treatCameras(incomingCameras, existingCameras);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Camera.class));
        }
    }

    public CrudOperation<Camera> treatCameras(List<DataplatformCamtraceCamera> comingCameras, List<Camera> existingCameras) {
        Set<Camera> toAdd = new HashSet<>();
        Set<Camera> toUpdate = new HashSet<>();

        for (DataplatformCamtraceCamera cameraSig : comingCameras) {
            Optional<Camera> optionalCamera = existingCameras.stream()
                    .filter(a -> a.getCode().equals(buildCode(cameraSig.getId())))
                    .findFirst();

            optionalCamera.ifPresentOrElse(
                    camera -> toUpdate.add(buildCamera(cameraSig, camera)),
                    () -> toAdd.add(buildCamera(cameraSig, null))
            );
        }

        log.info("{} : {} added, {} updated", this.getClass().getSimpleName(), toAdd.size(), toUpdate.size());
        return new CrudOperation<>(toAdd, toUpdate, new HashSet<>());
    }

    public static Camera buildCamera(DataplatformCamtraceCamera newImage, Camera lastImage) {
        Camera.CameraBuilder builder = lastImage != null ? lastImage.toBuilder() : Camera.builder()
                .code(buildCode(newImage.getId()))
                .name(newImage.getName())
                .inhibition(false)
                .forced(1)
                .externalids(recomputeExternalIdsCamtrace(newImage.getId(), lastImage));

        return builder
                .status(mapStatus(newImage.getStatus()))
                .controllable(newImage.isPtzAllowed())
                .build();
    }

    private static Integer mapStatus(Integer status) {
        return switch (status) {
            case -2 -> -2;
            case -1 -> -1;
            case 0 -> 0;
            case 1 -> 2;
            case 2 -> 1;
            default -> status;
        };
    }

    private static Map<String, Object> recomputeExternalIdsCamtrace(Long externalId, Camera lastImage) {
        Map<String, Object> externalIds = new HashMap<>();
        if (lastImage != null) {
            externalIds.putAll(lastImage.getExternalids());
        }
        externalIds.put(CAMTRACE, externalId);
        return externalIds;
    }

}
