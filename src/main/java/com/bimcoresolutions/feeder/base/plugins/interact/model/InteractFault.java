package com.bimcoresolutions.feeder.base.plugins.interact.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder(toBuilder = true)
public class InteractFault {
    private String severity;

    @JsonProperty("error_key")
    private String errorKey;

    @JsonProperty("category_key")
    private String categoryKey;

    @JsonProperty("is_active")
    private Boolean isActive;

    @JsonProperty("closed_timestamp")
    private Instant closedTimestamp;

    @JsonProperty("external_asset_id")
    private String externalAssetId;

    @JsonProperty("creation_timestamp")
    private Instant creationTimestamp;

    @JsonProperty("element_id")
    private String elementId;

}
