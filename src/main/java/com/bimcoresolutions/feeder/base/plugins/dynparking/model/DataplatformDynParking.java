package com.bimcoresolutions.feeder.base.plugins.dynparking.model;

import com.bimcoresolutions.project.bimsocle.util.bimutil.serialisation.GeometryDeserializer;
import com.bimcoresolutions.project.bimsocle.util.bimutil.serialisation.GeometrySerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import org.locationtech.jts.geom.Geometry;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Getter
@Setter
@Builder(toBuilder = true)
public class DataplatformDynParking {
    private String parkingRef;
    private Map<String, Object> occupancies;
    private Integer totalFreeSpaces;
    private String parkingStatus;
}
