package com.bimcoresolutions.feeder.base.plugins.sigharopa.service;

import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.riverstructure.RiverStructureSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RiverStructure;
import com.bimcoresolutions.util.base.model.Model;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.util.*;

@Log4j2
@Service
public class SigHaropaRiverStructureService {
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaRiverStructureService(SigHaropaUtils sigHaropaUtils) {
        this.sigHaropaUtils = sigHaropaUtils;
    }

    public CrudOperation<Model> treatSigRiverStructures(List<RiverStructure> existingRiverStructures, List<RiverStructureSig> comingRiverStructures, String type) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (RiverStructureSig riverStructureSig : comingRiverStructures) {
            Optional<RiverStructure> optionalRiverStructure = existingRiverStructures.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + riverStructureSig.getProperties().get("id_sig") + type))
                    .findFirst();

            if (optionalRiverStructure.isEmpty()) {
                toAdd.add(sigHaropaUtils.createRiverStructure(riverStructureSig, type));
            } else {
                optionalRiverStructure.ifPresent(riverStructure -> toUpdate.add(sigHaropaUtils.updateRiverStructure(riverStructureSig, riverStructure)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.riverStructuresToRemove(comingRiverStructures, existingRiverStructures, type));

        log.info("SigHaropaService, treatSigRiverStructures results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }
}
