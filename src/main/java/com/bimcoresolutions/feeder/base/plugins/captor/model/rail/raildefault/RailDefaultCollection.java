package com.bimcoresolutions.feeder.base.plugins.captor.model.rail.raildefault;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RailDefaultCollection extends CaptorCollection {
    private List<RailDefault> items;
    private String typeObject;
}

