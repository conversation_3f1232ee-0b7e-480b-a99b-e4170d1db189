package com.bimcoresolutions.feeder.base.plugins.grafana.model.alert;

import com.bimcoresolutions.feeder.base.plugins.grafana.model.GrafanaCollection;
import lombok.*;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GrafanaAlertCollection extends GrafanaCollection {
    private GrafanaAlertData data;
    private String status;
}
