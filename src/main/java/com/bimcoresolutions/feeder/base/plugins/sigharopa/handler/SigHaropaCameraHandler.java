package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.camera.CameraCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.camera.CameraSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.CameraGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Camera;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CameraCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaCameraHandler extends SigHaropaHandler {
    private final CameraGenFeignClient cameraGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaCameraHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, CameraGenFeignClient cameraGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.cameraGenFeignClient = cameraGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "camera";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        CameraCollection input = objectMapper.readValue(fileBody, CameraCollection.class);
        List<CameraSig> comingCameras = input.getFeatures();
        List<Camera> cameras = cameraGenFeignClient.get(new CameraCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigCameras(cameras, comingCameras);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Camera.class));
        }
    }

    public CrudOperation<Model> treatSigCameras(List<Camera> existingCameras, List<CameraSig> comingCameras) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (CameraSig cameraSig : comingCameras) {
            Optional<Camera> optionalCamera = existingCameras.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + cameraSig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalCamera.isEmpty()) {
                toAdd.add(sigHaropaUtils.createCamera(cameraSig));
            } else {
                optionalCamera.ifPresent(camera -> toUpdate.add(sigHaropaUtils.updateCamera(cameraSig, camera)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.camerasToRemove(comingCameras, existingCameras));

        log.info("SigHaropaService, treatSigCameras results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
