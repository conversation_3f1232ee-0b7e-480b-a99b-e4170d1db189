package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.riverstructure.RiverStructureCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.riverstructure.RiverStructureSig;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.service.SigHaropaRiverStructureService;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.RiverStructureGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RiverStructure;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RiverStructureCriteria;
import com.bimcoresolutions.util.base.filter.StringFilter;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SigHaropaRiverStructureEcluseHandler extends SigHaropaHandler {
    private final RiverStructureGenFeignClient riverStructureGenFeignClient;
    private final SigHaropaRiverStructureService sigHaropaRiverStructureService;

    public SigHaropaRiverStructureEcluseHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, RiverStructureGenFeignClient riverStructureGenFeignClient, SigHaropaRiverStructureService sigHaropaRiverStructureService) {
        super(bimApiClient, objectMapper, cmSender);
        this.riverStructureGenFeignClient = riverStructureGenFeignClient;
        this.sigHaropaRiverStructureService = sigHaropaRiverStructureService;
    }

    @Override
    public String getHandledEntityName() {
        return "ecluse";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        RiverStructureCollection input = objectMapper.readValue(fileBody, RiverStructureCollection.class);
        List<RiverStructureSig> comingRiverStructures = input.getFeatures();
        comingRiverStructures.forEach(comingRiverStructure -> comingRiverStructure.setTypeObject(input.getTypeObject()));
        List<RiverStructure> riverStructures = getAllRiverStructureByType();
        CrudOperation<Model> crudOperation = sigHaropaRiverStructureService.treatSigRiverStructures(riverStructures, comingRiverStructures, input.getTypeObject());

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(RiverStructure.class));
        }
    }

    List<RiverStructure> getAllRiverStructureByType() {
        RiverStructureCriteria riverStructureCriteria = new RiverStructureCriteria();
        StringFilter sf = new StringFilter();
        sf.setEqualsIgnoreCase("porte d'ecluse");
        riverStructureCriteria.setType(sf);
        return riverStructureGenFeignClient.get(riverStructureCriteria, null, null, null, null, null).getItems();
    }

}
