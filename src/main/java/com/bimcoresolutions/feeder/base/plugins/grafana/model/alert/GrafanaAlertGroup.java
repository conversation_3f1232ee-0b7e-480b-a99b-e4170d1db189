package com.bimcoresolutions.feeder.base.plugins.grafana.model.alert;

import com.bimcoresolutions.feeder.base.plugins.grafana.model.GrafanaCollection;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GrafanaAlertGroup extends GrafanaCollection {
    private String name;
    private String file;
    private List<GrafanaAlertRule> rules;
    private Integer interval;
    private String lastEvaluation;
    private Double evaluationTime;
}
