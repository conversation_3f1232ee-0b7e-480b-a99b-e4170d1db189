package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.SigMorbihanUtils;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.lightingpoint.LightingPointSig;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.lightingpoint.LightingPointSigCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.concat;
import static com.bimcoresolutions.feeder.base.plugins.sigmorbihan.SigMorbihanUtils.MORBIHANSIG;
import static com.bimcoresolutions.feeder.base.plugins.sigmorbihan.SigMorbihanUtils.findLp;

@Log4j2
@Component
public class SigMorbihanLightingPointHandler extends SigMorbihanHandler {
    public static final int LIMIT = 100;
    private final SigMorbihanUtils sigMorbihanUtils;
    private final LightingPointApiClient lightingPointApiClient;
    private final CabinetApiClient cabinetApiClient;

    public SigMorbihanLightingPointHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, LightingPointApiClient lightingPointApiClient, CabinetApiClient cabinetApiClient, SigMorbihanUtils sigMorbihanUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.lightingPointApiClient = lightingPointApiClient;
        this.cabinetApiClient = cabinetApiClient;
        this.sigMorbihanUtils = sigMorbihanUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "lightingPoint";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        LightingPointSigCollection input = objectMapper.readValue(fileBody, LightingPointSigCollection.class);
        List<LightingPointSig> comingLps = input.getFeatures();
        int created = 0;
        int updated = 0;
        int deleted = 0;
        int processed = 0;
        int lpsCount = input.getFeaturesCount();

        treatSlice(comingLps, created, updated, deleted, processed, lpsCount, LIMIT);
    }

    private void treatSlice(List<LightingPointSig> comingLps, int created, int updated, int deleted, int processed, int lpsCount, int limit) {
        List<List<LightingPointSig>> slicedLps = ListUtils.partition(comingLps, limit);
        if (lpsCount < LIMIT) {
            treatSlicedLps(comingLps, created, updated, deleted, processed, lpsCount);
        } else {
            slicedLps.forEach(slice -> {
                try {
                    treatSlicedLps(slice, created, updated, deleted, processed, lpsCount);
                } catch (Exception e) {
                    if (limit > 1) {
                        treatSlice(slice, created, updated, deleted, processed, lpsCount, limit < 10 ? 1 : limit / 10);
                    }
                }
            });
        }
    }

    public Map<String, Integer> treatSlicedLps(List<LightingPointSig> slicedLps, int created, int updated, int deleted, int processed, int lpsCount) {
        List<String> ids = new ArrayList<>();
        slicedLps.forEach(lp -> ids.add(lp.getPrimaryKey()));
        List<String> cabinetsIds = slicedLps.stream()
                .map(LightingPointSig::getPrimaryKeyCabinet)
                .distinct()
                .toList();
        List<Cabinet> linkedCabinets = cabinetApiClient.getCabinetsByExternalIds(MORBIHANSIG, cabinetsIds);
        List<LightingPoint> lps = lightingPointApiClient.getLightingPointsByExternalIds(MORBIHANSIG, ids);
        CrudOperation<Model> crudOperation = new CrudOperation<>();
        Set<Model> toCreate = crudOperation.getCreate();
        Set<Model> toUpdate = crudOperation.getUpdate();
        Set<Model> toDelete = crudOperation.getDelete();

        for (LightingPointSig lpSig : slicedLps) {
            boolean isDeleted = Objects.equals(lpSig.getDeleted(), "1");
            Optional<LightingPoint> dpLp = findLp(lpSig, lps);
            if (!isDeleted) {
                if (dpLp.isEmpty()) {
                    sigMorbihanUtils.createLightingPoint(lpSig, new HashSet<>(linkedCabinets), toCreate, toUpdate);
                } else {
                    sigMorbihanUtils.updateLightingPoint(lpSig, dpLp.get(), new HashSet<>(linkedCabinets), toUpdate);
                }
            } else {
                dpLp.ifPresent(toDelete::add);
            }
        }

        bimApiClient.addMissingIds(crudOperation.getCreate());
        CrudOperation<LightingPoint> crudLightingpoint = crudOperation.get(LightingPoint.class);

        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(LightingPoint.class));
            cabinetApiClient.updateCabinetAndRelations(crudOperation.get(Cabinet.class),
                    concat(crudLightingpoint.getCreate().stream().toList(), crudLightingpoint.getUpdate().stream().toList()));
        }

        created = created + crudLightingpoint.getCreate().size();
        updated = updated + crudLightingpoint.getUpdate().size();
        deleted = deleted + crudLightingpoint.getDelete().size();
        processed = processed + slicedLps.size();

        log.info("Processed {} of {} lightingpoints: {} created, {} updated, {} deleted", processed, lpsCount, created, updated, deleted);

        return Map.ofEntries(
                Map.entry("created", created),
                Map.entry("updated", updated),
                Map.entry("deleted", deleted)
        );
    }
}
