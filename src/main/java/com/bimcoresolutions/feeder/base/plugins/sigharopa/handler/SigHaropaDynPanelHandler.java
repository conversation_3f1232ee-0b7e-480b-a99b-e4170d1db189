package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.dynpanel.DynPanelCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.dynpanel.DynPanelSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.DynPanelGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Camera;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.DynPanel;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.DynPanelCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaDynPanelHandler extends SigHaropaHandler {
    private final DynPanelGenFeignClient dynPanelGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaDynPanelHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, DynPanelGenFeignClient dynPanelGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.dynPanelGenFeignClient = dynPanelGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "dynpanel";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        DynPanelCollection input = objectMapper.readValue(fileBody, DynPanelCollection.class);
        List<DynPanelSig> comingDynPanels = input.getFeatures();
        List<DynPanel> dynPanels = dynPanelGenFeignClient.get(new DynPanelCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigDynPanel(dynPanels, comingDynPanels);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Camera.class));
        }
    }

    public CrudOperation<Model> treatSigDynPanel(List<DynPanel> existingDynPanels, List<DynPanelSig> comingDynPanels) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (DynPanelSig dynPanelSig : comingDynPanels) {
            Optional<DynPanel> optionalDynPanel = existingDynPanels.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + dynPanelSig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalDynPanel.isEmpty()) {
                toAdd.add(sigHaropaUtils.createDynPanel(dynPanelSig));
            } else {
                optionalDynPanel.ifPresent(dynPanel -> toUpdate.add(sigHaropaUtils.updateDynPanel(dynPanelSig, dynPanel)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.dynpanelsToRemove(comingDynPanels, existingDynPanels));

        log.info("SigHaropaService, treatSigDynPanel results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
