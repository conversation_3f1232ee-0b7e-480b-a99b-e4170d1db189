package com.bimcoresolutions.feeder.base.plugins.grafana.model.alert;

import com.bimcoresolutions.feeder.base.plugins.grafana.model.GrafanaCollection;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GrafanaAlertDetail extends GrafanaCollection {
    private Map<String, Object> labels;
    private Map<String, Object> annotations;
    private String state;
    private String activeAt;
    private String value;
}
