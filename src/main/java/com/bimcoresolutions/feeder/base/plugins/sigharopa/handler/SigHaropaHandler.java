package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.handler.EntityHandler;
import com.fasterxml.jackson.databind.ObjectMapper;

public abstract class SigHaropaHandler extends EntityHandler {

    protected SigHaropaHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender) {
        super(bimApiClient, objectMapper, cmSender);
    }

}
