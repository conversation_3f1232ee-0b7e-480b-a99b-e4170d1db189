package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.lightingpoint;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class LightingPointSig {
    String id;

    String geom;

    @JsonProperty("cle_primaire")
    String primaryKey;

    @JsonProperty("commune")
    String city;

    @JsonProperty("reference")
    String nameAsset;

    @JsonProperty("adresse")
    String street;

    String gestionnaire;

    String owner;

    @JsonProperty("date_pose")
    String installationDate;

    @JsonProperty("type_luminaire")
    String lightingPointType;

    @JsonProperty("cle_primaire_armoire")
    String primaryKeyCabinet;

    @JsonProperty("code_depart")
    String circuitNumber;

    @JsonProperty("type_source")
    String sourceType;

    @JsonProperty("reference_luminaire")
    String streetlightModel;

    String puissance;

    @JsonProperty("datemodification")
    String dateModification;

    @JsonProperty("gi_deleted")
    String deleted;
}
