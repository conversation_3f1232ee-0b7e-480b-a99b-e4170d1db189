package com.bimcoresolutions.feeder.base.plugins.captor.model.transformerstation;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransformerStationDefaultCollection extends CaptorCollection {
    private List<TransformerStationDefault> items;
    private String typeObject;
}
