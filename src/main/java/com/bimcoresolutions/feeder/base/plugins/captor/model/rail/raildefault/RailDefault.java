package com.bimcoresolutions.feeder.base.plugins.captor.model.rail.raildefault;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RailDefault {
    private String name;
    private Long lastupdate;
    @JsonProperty("alarm_status")
    private Integer alarmStatus;
    private String comment;
}