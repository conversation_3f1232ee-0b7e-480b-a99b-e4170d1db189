package com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructuredefault;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RiverStructureDefault {
    private String id;
    private String name;
    private Integer status;
    private String comment;
    private Long lastupdate;
}

