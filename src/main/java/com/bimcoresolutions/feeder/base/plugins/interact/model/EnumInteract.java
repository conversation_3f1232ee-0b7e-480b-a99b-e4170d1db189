package com.bimcoresolutions.feeder.base.plugins.interact.model;

public enum EnumInteract {
    LampOutage("Event_LampOutage", "Panne de la source lumineuse", 3),
    Gen2OLCUnexpectedPowerOutage("Event_Gen2OLCUnexpectedPowerOutage", "Perte d'alimentation électrique inattendue", 1),
    OlcNotReachableNew("Event_OlcNotReachableNew","Impossible d'établir le lien avec le nœud", 2),
    DaliCommunicationFailure("Event_DaliCommunicationFailure", "Le luminaire ne s'abaisse pas. Vérifiez le câblage ou remplacez le driver LED.", 3),
    DaliIntermittentFailure("Event_DaliIntermittentFailure", "Défaillance source : Le système rapporte une défaillance de la source. Remplacer le luminaire", 2),
    OlcNotReportingLogData("Event_OlcNotReportingLogData", "Le réverbère n'est pas alimenté, le nœud ne communique plus ses données horaires.", 1),
    OTHER("Other", "Autre", 3);

    private final String value;
    private final String name;
    private final Integer priority;

    EnumInteract(String value, String name, Integer priority) {
        this.value = value;
        this.name = name;
        this.priority = priority;
    }

    public String getValeur() {
        return value;
    }

    public String getName() {
        return name;
    }

    public Integer getPriority() {
        return priority;
    }


    public static EnumInteract getByErrorCode(String value) {
        for (EnumInteract e : EnumInteract.values()) {
            if (e.value.equals(value)) {
                return e;
            }
        }
        return EnumInteract.OTHER;
    }
}
