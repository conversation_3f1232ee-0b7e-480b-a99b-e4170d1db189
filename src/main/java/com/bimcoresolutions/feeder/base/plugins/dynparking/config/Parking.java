package com.bimcoresolutions.feeder.base.plugins.dynparking.config;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
public class Parking {
    private String id;
    private final List<Pjd> pjds = new ArrayList<>();
    private String value;

    public Parking(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("Parking{");
        sb.append("id='").append(id).append('\'');
        sb.append(", pjds=").append(pjds.stream().map(Pjd::getId).toList());
        sb.append(", value='").append(value).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
