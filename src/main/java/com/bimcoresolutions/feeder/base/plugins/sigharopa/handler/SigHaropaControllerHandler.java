package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.controller.ControllerCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.controller.ControllerSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.ControllerGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Controller;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.ControllerCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaControllerHandler extends SigHaropaHandler {
    private final ControllerGenFeignClient controllerGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaControllerHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender,
                                      ControllerGenFeignClient controllerGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.controllerGenFeignClient = controllerGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "controller";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        ControllerCollection input = objectMapper.readValue(fileBody, ControllerCollection.class);
        List<ControllerSig> comingControllers = input.getFeatures();
        List<Controller> existingControllers = controllerGenFeignClient.get(new ControllerCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigController(existingControllers, comingControllers);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Controller.class));
        }
    }

    public CrudOperation<Model> treatSigController(List<Controller> existingControllers, List<ControllerSig> comingControllers) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (ControllerSig controllerSig : comingControllers) {
            Optional<Controller> optionalController = existingControllers.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + controllerSig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalController.isEmpty()) {
                toAdd.add(sigHaropaUtils.createController(controllerSig));
            } else {
                optionalController.ifPresent(controller -> toUpdate.add(sigHaropaUtils.updateController(controllerSig, controller)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.controllersToRemove(comingControllers, existingControllers));

        log.info("SigHaropaService, treatSigController results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
