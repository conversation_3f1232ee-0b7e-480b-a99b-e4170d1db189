package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.railcrossing.RailCrossingCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.railcrossing.RailCrossingSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.RailCrossingGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailCrossing;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailCrossingCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaRailCrossingHandler extends SigHaropaHandler {
    private final RailCrossingGenFeignClient railCrossingGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaRailCrossingHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, RailCrossingGenFeignClient railCrossingGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.railCrossingGenFeignClient = railCrossingGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "railcross";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        RailCrossingCollection input = objectMapper.readValue(fileBody, RailCrossingCollection.class);
        List<RailCrossingSig> comingRailCrossings = input.getFeatures();
        Map<String, Integer> counters = new HashMap<>(Map.of("createdCount", 0, "updatedCount", 0, "deletedCount", 0, "processedRailCrossings", 0));
        treatSliceOfRailCrossings(comingRailCrossings, counters, 500);
    }

    private void treatSliceOfRailCrossings(List<RailCrossingSig> comingRailCrossings, Map<String, Integer> counters, int limit) {
        List<List<RailCrossingSig>> slicedRailCrossings = ListUtils.partition(comingRailCrossings, limit);
        slicedRailCrossings.forEach(slice -> {
            try {
                treatSlicedRailCrossings(slice, counters);
            } catch (Exception e) {
                if (limit > 1) {
                    treatSliceOfRailCrossings(slice, counters, limit < 50 ? 1 : limit / 50);
                }
            }
        });
    }

    private void treatSlicedRailCrossings(List<RailCrossingSig> slicedRailCrossings, Map<String, Integer> counters) {
        List<RailCrossing> railCrossings = railCrossingGenFeignClient.get(new RailCrossingCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigRailCrossing(railCrossings, slicedRailCrossings);
        bimApiClient.addMissingIds(crudOperation.getCreate());

        // Info counters
        Integer createdCount = counters.get("createdCount");
        Integer updatedCount = counters.get("updatedCount");
        Integer deletedCount = counters.get("deletedCount");
        counters.put("createdCount", createdCount + crudOperation.get(RailCrossing.class).getCreate().size());
        counters.put("updatedCount", updatedCount + crudOperation.get(RailCrossing.class).getUpdate().size());
        counters.put("deletedCount", deletedCount + crudOperation.get(RailCrossing.class).getDelete().size());
        Integer processedRailCrossings = counters.get("processedRailCrossings");
        counters.put("processedRailCrossings", processedRailCrossings + slicedRailCrossings.size());

        log.info("SigHaropaService, treatSigCabinets results : {} - added, {} - updated, {} - deleted", createdCount, updatedCount, deletedCount);

        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(RailCrossing.class));
        }
    }

    public CrudOperation<Model> treatSigRailCrossing(List<RailCrossing> existingRailCrossing, List<RailCrossingSig> comingRailCrossing) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (RailCrossingSig railCrossingSig : comingRailCrossing) {
            Optional<RailCrossing> optionalRailCrossing = existingRailCrossing.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + railCrossingSig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalRailCrossing.isEmpty()) {
                toAdd.add(sigHaropaUtils.createRailCrossing(railCrossingSig));
            } else {
                optionalRailCrossing.ifPresent(railCrossing -> toUpdate.add(sigHaropaUtils.updateRailCrossing(railCrossingSig, railCrossing)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.railCrossingsToRemove(comingRailCrossing, existingRailCrossing));

        log.info("SigHaropaService, treatSigRailCrossing results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
