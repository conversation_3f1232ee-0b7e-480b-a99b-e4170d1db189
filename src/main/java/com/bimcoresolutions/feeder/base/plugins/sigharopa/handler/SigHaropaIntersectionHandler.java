package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ControllerApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.IntersectionApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.intersection.IntersectionCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.intersection.IntersectionSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Controller;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Intersection;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMBodyv2;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.concat;
import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.makeLotsOfRelations;

@Log4j2
@Component
public class SigHaropaIntersectionHandler extends SigHaropaHandler {
    private final IntersectionApiClient intersectionApiClient;
    private final ControllerApiClient controllerApiClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaIntersectionHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, IntersectionApiClient intersectionApiClient,
            ControllerApiClient controllerApiClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.intersectionApiClient = intersectionApiClient;
        this.controllerApiClient = controllerApiClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "intersection";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        IntersectionCollection input = objectMapper.readValue(fileBody, IntersectionCollection.class);
        List<IntersectionSig> comingIntersections = input.getFeatures();
        List<Intersection> intersections = intersectionApiClient.getAllIntersections();
        List<String> controllersIds = comingIntersections.stream()
                .map(ci -> ci.getProperties().get("idiroad_controller"))
                .filter(Objects::nonNull)
                .toList();

        List<Controller> linkedControllers = controllerApiClient.getControllerByExternalIds("iroad", controllersIds);
        CrudOperation<Model> crudOperation = treatSigIntersections(intersections, comingIntersections, linkedControllers);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Intersection.class));
            CrudOperation<Intersection> crudIntersection = crudOperation.get(Intersection.class);
            updateControllerAndRelation(crudOperation.get(Controller.class), concat(crudIntersection.getCreate().stream().toList(), crudIntersection.getUpdate().stream().toList()));
        }
    }

    private void updateControllerAndRelation(CrudOperation<Controller> crud, List<Intersection> intersections) {
        List<Controller> controllers = concat(crud.getCreate(), crud.getUpdate());
        CMBodyv2 commitMetier = CMBodyv2.builder()
                .listClassMetier(List.of("Controller"))
                .cre(cmSender.creationEntity(crud.getCreate()))
                .upd(cmSender.updateEntity(crud.getUpdate()))
                .updRel(Map.of("Controller_intersections", makeLotsOfRelations(controllers, List.of(), Controller::getIntersections, Intersection::getCode, intersections)))
                .build();
        CMRequestv2 cm = CMRequestv2.builder().commitMetier(commitMetier).build();

        cmSender.commit(cm);
    }

    public CrudOperation<Model> treatSigIntersections(List<Intersection> existingIntersections, List<IntersectionSig> comingIntersections, List<Controller> linkedControllers) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (IntersectionSig intersectionSig : comingIntersections) {
            Optional<Intersection> optionalIntersection = existingIntersections.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + intersectionSig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalIntersection.isEmpty()) {
                sigHaropaUtils.createIntersection(intersectionSig, linkedControllers, toAdd, toUpdate);
            } else {
                optionalIntersection.ifPresent(intersection -> sigHaropaUtils.updateIntersection(intersectionSig, intersection, linkedControllers, toUpdate));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.intersectionsToRemove(comingIntersections, existingIntersections));

        log.info("SigHaropaService, treatSigIntersections results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
