package com.bimcoresolutions.feeder.base.plugins.synapse.model.asset;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SynapseAsset {
    Integer id;
    Integer asset_id;
    String rtsp_url;
    String external_id;
    String name;
    String description;
    String status;
    String status_message;
    String created_at;
    String updated_at;
}
