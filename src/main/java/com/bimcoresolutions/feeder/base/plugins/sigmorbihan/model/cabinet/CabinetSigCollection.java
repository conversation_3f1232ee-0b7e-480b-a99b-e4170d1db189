package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.cabinet;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class CabinetSigCollection {
    Integer featuresCount;
    String featureType;
    List<CabinetSig> features;
}
