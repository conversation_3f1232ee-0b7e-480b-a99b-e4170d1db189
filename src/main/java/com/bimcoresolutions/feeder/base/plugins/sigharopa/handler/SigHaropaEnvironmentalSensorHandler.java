package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.environmentalsensor.EnvironmentalSensorCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.environmentalsensor.EnvironmentalSensorSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.EnvironmentalSensorGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.EnvironmentalSensor;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.EnvironmentalSensorCriteria;
import com.bimcoresolutions.util.base.FunctionalException;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaEnvironmentalSensorHandler extends SigHaropaHandler {
    private final EnvironmentalSensorGenFeignClient environmentalSensorGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaEnvironmentalSensorHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender,
                                               EnvironmentalSensorGenFeignClient environmentalSensorGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.environmentalSensorGenFeignClient = environmentalSensorGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "sensor";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        EnvironmentalSensorCollection input = objectMapper.readValue(fileBody, EnvironmentalSensorCollection.class);
        List<EnvironmentalSensorSig> comingEnvironmentalSensors = input.getFeatures();
        List<EnvironmentalSensor> environmentalSensors = environmentalSensorGenFeignClient.get(new EnvironmentalSensorCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigEnvironmentalSensors(environmentalSensors, comingEnvironmentalSensors);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(EnvironmentalSensor.class));
        }
    }

    public CrudOperation<Model> treatSigEnvironmentalSensors(List<EnvironmentalSensor> existingEnvironmentalSensors, List<EnvironmentalSensorSig> comingEnvironmentalSensors) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        for (EnvironmentalSensorSig environmentalSensorSig : comingEnvironmentalSensors) {
            Optional<EnvironmentalSensor> optionalEnvironmentalSensor = existingEnvironmentalSensors.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + environmentalSensorSig.getProperties().get("id_sig")))
                    .findFirst();

            try {
                if (optionalEnvironmentalSensor.isEmpty()) {
                    toAdd.add(sigHaropaUtils.createEnvironmentalSensor(environmentalSensorSig));
                } else {
                    optionalEnvironmentalSensor.ifPresent(environmentalSensor -> toUpdate.add(sigHaropaUtils.updateEnvironmentalSensor(environmentalSensorSig, environmentalSensor)));
                }
            } catch (FunctionalException e) {
                log.warn(e.getLocalizedMessage());
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.environmentalSensorsToRemove(comingEnvironmentalSensors, existingEnvironmentalSensors));

        log.info("SigHaropaService, treatSigEnvironmentalSensors results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
