package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.cabinet.CabinetCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.cabinet.CabinetSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.CabinetGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CabinetCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaCabinetHandler extends SigHaropaHandler {
    private final CabinetGenFeignClient cabinetGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;

    public SigHaropaCabinetHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, CabinetGenFeignClient cabinetGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.cabinetGenFeignClient = cabinetGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "cabinet";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        CabinetCollection input = objectMapper.readValue(fileBody, CabinetCollection.class);
        List<CabinetSig> comingCabinets = input.getFeatures();
        List<Cabinet> cabinets = cabinetGenFeignClient.get(new CabinetCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigCabinets(cabinets, comingCabinets);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(Cabinet.class));
        }
    }

    public CrudOperation<Model> treatSigCabinets(List<Cabinet> existingCabinets, List<CabinetSig> comingCabinets) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (CabinetSig cabinetSig : comingCabinets) {
            Optional<Cabinet> optionalCabinet = existingCabinets.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + cabinetSig.getProperties().get("id_sig")))
                    .findFirst();

            if (optionalCabinet.isEmpty()) {
                toAdd.add(sigHaropaUtils.createCabinet(cabinetSig));
            } else {
                optionalCabinet.ifPresent(cabinet -> toUpdate.add(sigHaropaUtils.updateCabinet(cabinetSig, cabinet)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.cabinetsToRemove(comingCabinets, existingCabinets));

        log.info("SigHaropaService, treatSigCabinets results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }
}
