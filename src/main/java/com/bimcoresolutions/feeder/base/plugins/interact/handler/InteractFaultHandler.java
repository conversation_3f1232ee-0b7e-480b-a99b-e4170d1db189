package com.bimcoresolutions.feeder.base.plugins.interact.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlertApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.models.alert.AlertCategory;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.interact.model.EnumInteract;
import com.bimcoresolutions.feeder.base.plugins.interact.model.InteractFault;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.CMUpdRelBodyv2;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.concat;
import static com.bimcoresolutions.feeder.base.notification.BimRelationUtils.makeLotsOfRelations;

@Log4j2
@Component
public class InteractFaultHandler extends DataPlatformHandler {

    public static final String INTERACT = "interact";
    private static final TypeReference<List<InteractFault>> typereferenceInteractFault = new TypeReference<>() {
    };
    private static final String HANDLED_ENTITY_NAME = "interact_fault_v0";
    private final LightingPointApiClient lightingPointApiClient;
    private final AlarmApiClient alarmApiClient;
    private final AlertApiClient alertApiClient;


    public InteractFaultHandler(BimApiClient bimApiClient, LightingPointApiClient lightingPointApiClient, ObjectMapper objectMapper, CMSender cmSender, AlarmApiClient alarmApiClient, AlertApiClient alertApiClient) {
        super(bimApiClient, objectMapper, cmSender);
        this.lightingPointApiClient = lightingPointApiClient;
        this.alarmApiClient = alarmApiClient;
        this.alertApiClient = alertApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return HANDLED_ENTITY_NAME;
    }

    @SneakyThrows
    @Override
    public void handle(String fileBody) {
        List<InteractFault> interactFaults = objectMapper.readValue(fileBody, typereferenceInteractFault);
        List<String> interactFaultsId = interactFaults.stream()
                .map(InteractFault::getExternalAssetId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        List<LightingPoint> existingLightingPoints = lightingPointApiClient.getLightingPointsByExternalIds(INTERACT, interactFaultsId);
        List<Alarm> existingAlarm = alarmApiClient.getAlarmByExternalIds(INTERACT, interactFaultsId);
        CrudOperation<Model> crudOperation = treatInteractFaults(interactFaults, existingLightingPoints, existingAlarm);


        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(LightingPoint.class));

            CrudOperation<Alarm> crudAlarm = crudOperation.get(Alarm.class);
            Map<String, List<CMUpdRelBodyv2>> relations = new HashMap<>();
            relations.put("Alarm_lightingpoint", makeLotsOfRelations(concat(crudAlarm.getCreate(), crudAlarm.getUpdate()), List.of(), Alarm::getLightingpoint, LightingPoint::getCode, existingLightingPoints));

            CrudOperation<Alert> crudAlert = crudOperation.get(Alert.class);
            relations.put("Alert_lightingpoint", makeLotsOfRelations(concat(crudAlert.getCreate(), crudAlert.getUpdate()), List.of(), Alert::getLightingpoint, LightingPoint::getCode, existingLightingPoints));

            relations.put("Alarm_alerts", makeLotsOfRelations(concat(crudAlarm.getCreate(), crudAlarm.getUpdate()), List.of(), Alarm::getAlerts, Alert::getCode, concat(crudAlert.getCreate(), crudAlert.getUpdate())));
            cmSender.commitWithRelations(crudOperation, relations);
        }
    }

    public CrudOperation<Model> treatInteractFaults(List<InteractFault> incomingAssetEnvironmentalSensor, List<LightingPoint> existingLightingPoints, List<Alarm> existingAlarms) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();

        for (InteractFault interactFault : incomingAssetEnvironmentalSensor) {
            if (EnumInteract.getByErrorCode(interactFault.getErrorKey()).equals(EnumInteract.OTHER)) {
                continue;
            }
            Optional<LightingPoint> optionalLightingPoint = existingLightingPoints.stream().filter(lp -> lp.getExternalids().get(INTERACT).toString().equals(interactFault.getExternalAssetId())).findFirst();
            optionalLightingPoint.ifPresent(lightingPoint -> {
                lightingPoint.setStatus(interactFault.getIsActive() ? -1 : 1);
                toUpdate.add(lightingPoint);

                Alarm alarm = treatAlarm(interactFault, lightingPoint, existingAlarms, toAdd, toUpdate);
                bimApiClient.addMissingIds(Set.of(alarm));

                treatAlert(interactFault, lightingPoint, toAdd, alarm);
                bimApiClient.addMissingIds(toAdd);
            });
        }

        log.info("InteractFaults results - updated : {}", toUpdate.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

    private Alarm treatAlarm(InteractFault interactFault, LightingPoint lightingPoint, List<Alarm> existingAlarms, Set<Model> toAdd, Set<Model> toUpdate) {
        String code = interactFault.getExternalAssetId() + "_" + interactFault.getCategoryKey();
        Optional<Alarm> optionalAlarm = existingAlarms.stream()
                .filter(alarm -> alarm.getExternalids().get(INTERACT).equals(interactFault.getExternalAssetId()) && alarm.getCode().equals(code))
                .findFirst();

        Alarm alarm;
        if (optionalAlarm.isEmpty()) {
            alarm = createAlarmFromInteract(interactFault, lightingPoint);
            toAdd.add(alarm);
        } else {
            Alarm alreadyExistingAlarm = optionalAlarm.get();
            alarm = updateAlarmFromInteract(interactFault, alreadyExistingAlarm);
            toUpdate.add(alarm);
        }
        return alarm;
    }

    private Alarm createAlarmFromInteract(InteractFault interactFault, LightingPoint lightingPoint) {
        return new Alarm().toBuilder()
                .code(interactFault.getExternalAssetId() + "_" + interactFault.getCategoryKey())
                .name(EnumInteract.getByErrorCode(interactFault.getErrorKey()).getName())
                .metier(Map.of("metiers", List.of("EP")))
                .lightingpoint(List.of(lightingPoint.get_id_bimcore()))
                .lastupdate(Date.from(interactFault.getCreationTimestamp()))
                .presence(interactFault.getIsActive())
                .externalids(lightingPoint.getExternalids())
                .source(INTERACT)
                .build();
    }

    private Alarm updateAlarmFromInteract(InteractFault interactFault, Alarm alarm) {
        return alarm.toBuilder()
                .lastupdate(Date.from(interactFault.getCreationTimestamp()))
                .presence(interactFault.getIsActive())
                .build();
    }

    private AlertCategory createAlertCategory(String name, Integer priority) {
        return AlertCategory.builder()
                .label(name)
                .criticality(priority)
                .equipmenttype("LightingPoint")
                .build();
    }

    private String getInteractAlertCategoryByName(String name, Integer priority) {
        List<String> existingAlertCategory = alertApiClient.getAlertCategoriesByLabel(name);
        if (CollectionUtils.isEmpty(existingAlertCategory)) {
            alertApiClient.createAlertCategory(List.of(createAlertCategory(name, priority)));
            return name;
        } else {
            return existingAlertCategory.stream().findFirst().get();
        }
    }

    private void treatAlert(InteractFault interactFault, LightingPoint lightingPoint, Set<Model> toAdd, Alarm alarm) {
        EnumInteract enumInteract = EnumInteract.getByErrorCode(interactFault.getErrorKey());
        Integer priority = enumInteract.getPriority();
        String interactAlertCategory = getInteractAlertCategoryByName(alarm.getName(), priority);

        List<Alert> existingAlerts = alertApiClient.getAlertByNotClosedAndSameCategory(interactAlertCategory);
        boolean alertAlreadyExists = existingAlerts.stream()
                .map(Alert::getLightingpoint)
                .anyMatch(list -> list.contains(lightingPoint.get_id_bimcore()));
        if (alertAlreadyExists) {
            return;
        }

        List<String> alarms = new ArrayList<>();
        if (alarm.get_id_bimcore() != null) {
            alarms.add(alarm.get_id_bimcore());
        }

        Alert alert = new Alert().toBuilder()
                .code(System.currentTimeMillis() + lightingPoint.getCode() + alarm.getCode())
                .location(lightingPoint.getLocation())
                .priority(priority)
                .name(alarm.getName())
                .source(INTERACT)
                .category(alarm.getName())
                .status("New")
                .creationdate(Date.from(interactFault.getCreationTimestamp()))
                .laststatuschangedate(Date.from(interactFault.getCreationTimestamp()))
                .alarms(alarms)
                .lightingpoint(List.of(lightingPoint.get_id_bimcore()))
                .build();

        toAdd.add(alert);
    }
}