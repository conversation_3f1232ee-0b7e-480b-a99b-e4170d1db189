package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.CountingStationDP;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.CountingStationGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CountingStation;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CountingStationCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

@Log4j2
@Component
public class SigHaropaCountingStationHandler extends SigHaropaHandler {
    private static final TypeReference<List<CountingStationDP>> typereferenceCountingStationDP = new TypeReference<>() {
    };
    private final CountingStationGenFeignClient countingStationGenFeignClient;
    private final SigHaropaUtils sigHaropaUtils;


    protected SigHaropaCountingStationHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, CountingStationGenFeignClient countingStationGenFeignClient, SigHaropaUtils sigHaropaUtils) {
        super(bimApiClient, objectMapper, cmSender);
        this.countingStationGenFeignClient = countingStationGenFeignClient;
        this.sigHaropaUtils = sigHaropaUtils;
    }

    @Override
    public String getHandledEntityName() {
        return "countingstation";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<CountingStationDP> data = objectMapper.readValue(fileBody, typereferenceCountingStationDP).stream().filter(x -> !x.getType().equals("Station")).toList();
        List<CountingStation> stations = countingStationGenFeignClient.get(new CountingStationCriteria(), null, null, null, null, null).getItems();
        CrudOperation<Model> crudOperation = treatSigCountingStations(stations, data);

        bimApiClient.addMissingIds(crudOperation.getCreate());
        if (!crudOperation.isEmpty()) {
            cmSender.commit(crudOperation.get(CountingStation.class));
        }
    }

    public CrudOperation<Model> treatSigCountingStations(List<CountingStation> existingCountingStations, List<CountingStationDP> comingCountingStationsDP) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();

        for (CountingStationDP countingStationDP : comingCountingStationsDP) {
            Optional<CountingStation> optionalCountingStation = existingCountingStations.stream()
                    .filter(a -> a.getCode().equals("sigharopa_" + countingStationDP.getId_sig()))
                    .findFirst();

            if (optionalCountingStation.isEmpty()) {
                toAdd.add(sigHaropaUtils.createStation(countingStationDP));
            } else {
                optionalCountingStation.ifPresent(countingStation -> toUpdate.add(sigHaropaUtils.updateStation(countingStationDP, countingStation)));
            }
        }

        Set<Model> toDelete = new HashSet<>(sigHaropaUtils.stationsToRemove(comingCountingStationsDP, existingCountingStations));

        log.info("SigHaropaService, treatSigCountingStations results : {} - added, {} - updated, {} - deleted", toAdd.size(), toUpdate.size(), toDelete.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }
}
