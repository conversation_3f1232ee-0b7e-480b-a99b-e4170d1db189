package com.bimcoresolutions.feeder.base.plugins.tellmycity.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder(toBuilder = true)
public class DataplatformTellMyCityCitizenAlert {
    private String sourcecode;
    private String name;
    private String sourcesystem;
    private ZonedDateTime signaldate;
    private String field;
    private int priority;
    private String address;
    private String coordonnees;
    private String comment;
    private List<Map<String, Object>> documents;

}