package com.bimcoresolutions.feeder.base.plugins.refparking.model;

import com.bimcoresolutions.util.base.serialisation.GeometryDeserializer;
import com.bimcoresolutions.util.base.serialisation.GeometrySerializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.*;
import org.locationtech.jts.geom.Geometry;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Getter
@Setter
@Builder(toBuilder = true)
public class DataplatformRefParking {
    private String parkingRef;
    private String address;
    private Map<String, Object> occupancies;
    private Map<String, Object> additionalCharacteristics;
    private String municipality;
    private String parkingProvider;
    private String parkingName;
    private String parkingManager;
    private Integer totalCapacity;
    private Date lastUpdate;
    private String parkingStatus;
    @JsonSerialize(using = GeometrySerializer.class)
    @JsonDeserialize(using = GeometryDeserializer.class)
    private Geometry location;
    private String managerWebsite;
    private List<Map<String, Object>> documents;
    private Integer forced;
    private Map<String, Object> infos;
    private Boolean inhibition;
    private Integer operatingstate;
    private String provider;
    private String site;
}
