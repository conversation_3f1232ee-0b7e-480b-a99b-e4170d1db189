package com.bimcoresolutions.feeder.base.plugins.captor.model.cameraevent;

import java.util.Arrays;
import java.util.Objects;

public enum EnumCameraEventCaptor {

    DEFAULT("0", ""),
    ANIMAL_LARGE("ANIMAL_LARGE", "Animal - grand"),
    ANIMAL_SMALL("ANIM<PERSON>_SMALL", "Animal - petit"),
    DAY_NIGHT("DAY_NIGHT", "Jour Nuit"),
    FALLEN_OBJECT("FALLEN_OBJECT", "Chute d'objet"),
    FIRE("FIRE", "Alerte incendie"),
    FIRE_PRE("FIRE_PRE", "Alerte pré-incendie"),
    INVERSE_DIRECTION("INVERSE_DIRECTION", "Contresens"),
    PEDESTRIAN("PEDESTRIAN", "Piéton"),
    PEDESTRIAN_OCCUPANCY_LEVEL1("PEDESTRIAN_OCCUPANCY_LEVEL1", "Occupation par les piétons 1"),
    PEDESTRIAN_OCCUPANCY_LEVEL2("PEDESTRIAN_OCCUPANCY_LEVEL2", "Occupation par les piétons 2"),
    PEDESTRIAN_OCCUPANCY_LEVEL3("PEDESTRIAN_OCCUPANCY_LEVEL3", "Occupation par les piétons 3"),
    PEDESTRIAN_OCCUPANCY_LEVEL4("PEDESTRIAN_OCCUPANCY_LEVEL4", "Occupation par les piétons 4"),
    PRESENCE_LEVEL1("PRESENCE_LEVEL1", "Niveau d'occupation 1"),
    PRESENCE_LEVEL2("PRESENCE_LEVEL2", "Niveau d'occupation 2"),
    QUEUE_OCCUPANCY_1("QUEUE_OCCUPANCY_1", "Queue occupancy 1"),
    QUEUE_OCCUPANCY_2("QUEUE_OCCUPANCY_2", "Queue occupancy 2"),
    QUEUE_OCCUPANCY_3("QUEUE_OCCUPANCY_3", "Queue occupancy 3"),
    SMOKE("SMOKE", "Fumée"),
    SPEED_ALARM_LEVEL1("SPEED_ALARM_LEVEL1", "Niveau de service 1"),
    SPEED_ALARM_LEVEL2("SPEED_ALARM_LEVEL2", "Niveau de service 2"),
    SPEED_ALARM_LEVEL3("SPEED_ALARM_LEVEL3", "Niveau de service 3"),
    SPEED_ALARM_LEVEL4("SPEED_ALARM_LEVEL4", "Niveau de service 4"),
    SPOTMETER_ALARM_LEVEL1("SPOTMETER_ALARM_LEVEL1", "Alarme point de mesure niveau 1"),
    SPOTMETER_ALARM_LEVEL2("SPOTMETER_ALARM_LEVEL2", "Alarme point de mesure niveau 2"),
    SPOTMETER_ALARM_LEVEL3("SPOTMETER_ALARM_LEVEL3", "Alarme point de mesure niveau 3"),
    STOPPED_VEHICLE("STOPPED_VEHICLE", "Véhicule arrêté"),
    UNDERSPEED("UNDERSPEED", "Sous-vitesse"),
    WT_PRESENCE("WT_PRESENCE", "World Tracker Presence");


    private final String value;
    private final String label;

    EnumCameraEventCaptor(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValeur() {
        return value;
    }

    public String getLabel() {
        return label;
    }


    public static EnumCameraEventCaptor getByValue(String value) {
        return Arrays.stream(EnumCameraEventCaptor.values())
                .filter(e -> Objects.equals(e.value, value)).findFirst()
                .orElse(DEFAULT);
    }
}
