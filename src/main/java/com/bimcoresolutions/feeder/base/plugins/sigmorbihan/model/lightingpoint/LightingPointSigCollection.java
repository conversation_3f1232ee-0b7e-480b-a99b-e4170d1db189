package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.lightingpoint;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class LightingPointSigCollection {
    Integer featuresCount;
    String featureType;
    List<LightingPointSig> features;
}
