package com.bimcoresolutions.feeder.base.plugins.interact.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.interact.model.InteractDataplatformAsset;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Log4j2
@Component
public class InteractAssetHandler extends DataPlatformHandler {
    private static final String INTERACT = "interact";
    private static final TypeReference<List<InteractDataplatformAsset>> typereferenceInteractAsset = new TypeReference<>() {};
    private static final String HANDLED_ENTITY_NAME = "interact_asset_v0";
    private final LightingPointApiClient lightingPointApiClient;

    public InteractAssetHandler(BimApiClient bimApiClient, LightingPointApiClient lightingPointApiClient, ObjectMapper objectMapper, CMSender cmSender) {
        super(bimApiClient, objectMapper, cmSender);
        this.lightingPointApiClient = lightingPointApiClient;
    }

    @Override
    public String getHandledEntityName() {
        return HANDLED_ENTITY_NAME;
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<InteractDataplatformAsset> interactDataplatformAssets = objectMapper.readValue(fileBody, typereferenceInteractAsset);
        List<String> incomingInteractAssetIds = interactDataplatformAssets.stream()
                .map(InteractDataplatformAsset::getCodeCityApp)
                .filter(Objects::nonNull)
                .map(String::trim)
                .distinct()
                .toList();

        List<LightingPoint> existingLightingPoints = lightingPointApiClient.getLightingPointsByNames(incomingInteractAssetIds);
        CrudOperation<Model> crudOperation = treatLightingPointsAssets(interactDataplatformAssets, existingLightingPoints);

        if (!crudOperation.isEmpty()) {
            bimApiClient.addMissingIds(crudOperation.getCreate());
            cmSender.commit(crudOperation);
        }
    }

    @SneakyThrows
    public CrudOperation<Model> treatLightingPointsAssets(List<InteractDataplatformAsset> incomingInteractAssets, List<LightingPoint> existingLightingPoints) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();

        for (InteractDataplatformAsset assetEnvironmentalSensor : incomingInteractAssets) {
            Optional<LightingPoint> optionalLightingPoint = existingLightingPoints.stream().filter(lightingPoint -> lightingPoint.getName().equals(assetEnvironmentalSensor.getCodeCityApp())).findFirst();
            optionalLightingPoint.ifPresent(lp -> toUpdate.add(updateLightingPoint(lp, assetEnvironmentalSensor)));
        }

        log.info("InteractAsset results : updated : {}", toUpdate.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

    private LightingPoint updateLightingPoint(LightingPoint extistingLightingPoint, InteractDataplatformAsset interactDataplatformAsset) {
        String externalAssetId = interactDataplatformAsset.getExternalAssetId();
        Map<String, Object> ids = extistingLightingPoint.getExternalids();

        if (CollectionUtils.isEmpty(ids)) {
            ids = new HashMap<>();
        }

        if (!ObjectUtils.isEmpty(externalAssetId)) {
            ids.put(INTERACT, externalAssetId);
        }
        return extistingLightingPoint.toBuilder().externalids(ids).build();
    }
}