package com.bimcoresolutions.feeder.base.plugins.salesforce.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ReportApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.notification.handler.DataPlatformHandler;
import com.bimcoresolutions.feeder.base.plugins.connectorstate.ConnectorStateService;
import com.bimcoresolutions.feeder.base.plugins.salesforce.SalesforceUtils;
import com.bimcoresolutions.feeder.base.plugins.salesforce.model.DataplatformSalesforceCitizenAlert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Report;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.bimcoresolutions.feeder.base.plugins.connectorstate.model.ConnectorStateStatus.ok;

@Log4j2
@Component
public class SalesforceCitizenAlertHandler extends DataPlatformHandler {

    public static final String SALESFORCE = "salesforce";
    private final SalesforceUtils salesforceUtils;
    private static final TypeReference<List<DataplatformSalesforceCitizenAlert>> typereferenceSalesforceCitizenAlert = new TypeReference<>() {
    };
    private final ReportApiClient reportApiClient;
    private final ConnectorStateService connectorStateService;

    public SalesforceCitizenAlertHandler(BimApiClient bimApiClient, ObjectMapper objectMapper, CMSender cmSender, ReportApiClient reportApiClient, SalesforceUtils salesforceUtils,
            ConnectorStateService connectorStateService) {
        super(bimApiClient, objectMapper, cmSender);
        this.reportApiClient = reportApiClient;
        this.salesforceUtils = salesforceUtils;
        this.connectorStateService = connectorStateService;
    }

    @Override
    public String getHandledEntityName() {
        return "salesforce_citizenalert_v0";
    }

    @Override
    public void handle(String fileBody) throws JsonProcessingException {
        List<DataplatformSalesforceCitizenAlert> salesforceCitizenAlerts = objectMapper.readValue(fileBody, typereferenceSalesforceCitizenAlert);
        List<String> incomingSalesforceCitizenAlertsCodes = salesforceCitizenAlerts.stream()
                .map(DataplatformSalesforceCitizenAlert::getSourcecode)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        connectorStateService.handleConnectorState(ok, SALESFORCE.toUpperCase());
        handleReports(incomingSalesforceCitizenAlertsCodes, salesforceCitizenAlerts);
    }

    private void handleReports(List<String> incomingSalesforceCitizenAlertsCodes, List<DataplatformSalesforceCitizenAlert> salesforceCitizenAlerts) {
        List<Report> existingReports = reportApiClient.getReportByExternalIds(SALESFORCE, incomingSalesforceCitizenAlertsCodes);
        CrudOperation<Model> crudOperationReport = treatReport(existingReports, salesforceCitizenAlerts);
        bimApiClient.addMissingIds(crudOperationReport.getCreate());

        if (!crudOperationReport.isEmpty()) {
            cmSender.commit(crudOperationReport);
        }
    }

    public CrudOperation<Model> treatReport(List<Report> existingReports, List<DataplatformSalesforceCitizenAlert> incomingCitizenAlerts) {
        Set<Model> toAdd = new HashSet<>();
        Set<Model> toUpdate = new HashSet<>();
        Set<Model> toDelete = new HashSet<>();

        for (DataplatformSalesforceCitizenAlert salesforceCitizenAlert : incomingCitizenAlerts) {
            Optional<Report> optionalReportFromReports = existingReports.stream()
                    .filter(a -> a.getCode().equals(salesforceCitizenAlert.getSourcecode()))
                    .findFirst();

            if (optionalReportFromReports.isEmpty()) {
                toAdd.add(salesforceUtils.createReport(salesforceCitizenAlert));
            } else {
                optionalReportFromReports.ifPresent(report -> toUpdate.add(salesforceUtils.updateReport(report, salesforceCitizenAlert)));
            }
        }

        log.info("SalesforceRuleService, treatReport results : {} - added - {} updated", toAdd.size(), toUpdate.size());
        return new CrudOperation<>(toAdd, toUpdate, toDelete);
    }

}
