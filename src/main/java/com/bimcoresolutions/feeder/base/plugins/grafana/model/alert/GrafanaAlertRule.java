package com.bimcoresolutions.feeder.base.plugins.grafana.model.alert;

import com.bimcoresolutions.feeder.base.plugins.grafana.model.GrafanaCollection;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder(toBuilder = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GrafanaAlertRule extends GrafanaCollection {
    private String state;
    private String name;
    private String query;
    private Integer duration;
    private Map<String, Object> annotations;
    private List<GrafanaAlertDetail> alerts;
    private String health;
    private String type;
    private String lastEvaluation;
    private Double evaluationTime;
}
