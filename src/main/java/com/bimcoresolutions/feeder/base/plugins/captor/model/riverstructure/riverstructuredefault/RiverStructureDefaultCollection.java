package com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructuredefault;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RiverStructureDefaultCollection extends CaptorCollection {
    private List<RiverStructureDefault> items;
    private String typeObject;
}

