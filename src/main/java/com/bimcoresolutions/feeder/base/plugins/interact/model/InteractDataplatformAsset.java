package com.bimcoresolutions.feeder.base.plugins.interact.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder(toBuilder = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class InteractDataplatformAsset {
    private List<InteractExternalId> luminaires;

    @JsonProperty("is_deleted")
    private Boolean isDeleted;

    @JsonProperty("external_asset_id")
    private String externalAssetId;

    private String libelle;

    private Float latitude;

    private Float longitude;

    @JsonProperty("site_id")
    private String siteId;

    @JsonProperty("code_cityapp")
    private String codeCityApp;

    @JsonProperty("element_id")
    private Long element_id;
}
