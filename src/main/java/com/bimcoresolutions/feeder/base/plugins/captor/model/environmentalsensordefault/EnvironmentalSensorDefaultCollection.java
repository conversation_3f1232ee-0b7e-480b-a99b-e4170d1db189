package com.bimcoresolutions.feeder.base.plugins.captor.model.environmentalsensordefault;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EnvironmentalSensorDefaultCollection extends CaptorCollection {
    private List<EnvironmentalSensorDefault> items;
    private String typeObject;
}

