package com.bimcoresolutions.feeder.base.plugins.captor.model.cabinetdefault;

import com.bimcoresolutions.feeder.base.plugins.captor.model.CaptorCollection;
import lombok.*;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CabinetDefaultCollection extends CaptorCollection {
    private List<CabinetDefault> items;
    private String typeObject;
}
