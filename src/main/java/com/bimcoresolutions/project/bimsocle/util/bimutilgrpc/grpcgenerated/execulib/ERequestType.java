// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Execulib.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib;

/**
 * Protobuf enum {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType}
 */
public enum ERequestType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>COMMAND = 0;</code>
   */
  COMMAND(0),
  /**
   * <code>CREATE = 1;</code>
   */
  CREATE(1),
  /**
   * <code>INSERT = 2;</code>
   */
  INSERT(2),
  /**
   * <code>UPDATE = 3;</code>
   */
  UPDATE(3),
  /**
   * <code>INSERT_OR_UPDATE = 4;</code>
   */
  INSERT_OR_UPDATE(4),
  /**
   * <code>DELETE = 5;</code>
   */
  DELETE(5),
  /**
   * <code>SELECT = 6;</code>
   */
  SELECT(6),
  /**
   * <code>DIRECT_GET = 7;</code>
   */
  DIRECT_GET(7),
  /**
   * <code>DIRECT_MODIFY = 8;</code>
   */
  DIRECT_MODIFY(8),
  /**
   * <code>DIRECT_MULTIPLE = 9;</code>
   */
  DIRECT_MULTIPLE(9),
  UNRECOGNIZED(-1),
  ;

  /**
   * <code>COMMAND = 0;</code>
   */
  public static final int COMMAND_VALUE = 0;
  /**
   * <code>CREATE = 1;</code>
   */
  public static final int CREATE_VALUE = 1;
  /**
   * <code>INSERT = 2;</code>
   */
  public static final int INSERT_VALUE = 2;
  /**
   * <code>UPDATE = 3;</code>
   */
  public static final int UPDATE_VALUE = 3;
  /**
   * <code>INSERT_OR_UPDATE = 4;</code>
   */
  public static final int INSERT_OR_UPDATE_VALUE = 4;
  /**
   * <code>DELETE = 5;</code>
   */
  public static final int DELETE_VALUE = 5;
  /**
   * <code>SELECT = 6;</code>
   */
  public static final int SELECT_VALUE = 6;
  /**
   * <code>DIRECT_GET = 7;</code>
   */
  public static final int DIRECT_GET_VALUE = 7;
  /**
   * <code>DIRECT_MODIFY = 8;</code>
   */
  public static final int DIRECT_MODIFY_VALUE = 8;
  /**
   * <code>DIRECT_MULTIPLE = 9;</code>
   */
  public static final int DIRECT_MULTIPLE_VALUE = 9;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @Deprecated
  public static ERequestType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static ERequestType forNumber(int value) {
    switch (value) {
      case 0: return COMMAND;
      case 1: return CREATE;
      case 2: return INSERT;
      case 3: return UPDATE;
      case 4: return INSERT_OR_UPDATE;
      case 5: return DELETE;
      case 6: return SELECT;
      case 7: return DIRECT_GET;
      case 8: return DIRECT_MODIFY;
      case 9: return DIRECT_MULTIPLE;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<ERequestType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      ERequestType> internalValueMap =
          number -> ERequestType.forNumber(number);

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return ExeculibGrpc.getDescriptor().getEnumTypes().getFirst();
  }

  private static final ERequestType[] VALUES = values();

  public static ERequestType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private ERequestType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType)
}

