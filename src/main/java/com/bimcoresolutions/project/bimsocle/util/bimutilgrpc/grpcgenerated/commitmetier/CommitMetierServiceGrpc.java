package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.commitmetier;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.35.0)",
    comments = "Source: CommitMetier.proto")
public final class CommitMetierServiceGrpc {

  private CommitMetierServiceGrpc() {}

  public static final String SERVICE_NAME = "com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.CommitMetierService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<CommitMetierRequest,
      CommitMetierResponse> getStreamCommitMetiersMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "streamCommitMetiers",
      requestType = CommitMetierRequest.class,
      responseType = CommitMetierResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
  public static io.grpc.MethodDescriptor<CommitMetierRequest,
      CommitMetierResponse> getStreamCommitMetiersMethod() {
    io.grpc.MethodDescriptor<CommitMetierRequest, CommitMetierResponse> getStreamCommitMetiersMethod;
    if ((getStreamCommitMetiersMethod = CommitMetierServiceGrpc.getStreamCommitMetiersMethod) == null) {
      synchronized (CommitMetierServiceGrpc.class) {
        if ((getStreamCommitMetiersMethod = CommitMetierServiceGrpc.getStreamCommitMetiersMethod) == null) {
          CommitMetierServiceGrpc.getStreamCommitMetiersMethod = getStreamCommitMetiersMethod =
              io.grpc.MethodDescriptor.<CommitMetierRequest, CommitMetierResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "streamCommitMetiers"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  CommitMetierRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  CommitMetierResponse.getDefaultInstance()))
              .setSchemaDescriptor(new CommitMetierServiceMethodDescriptorSupplier("streamCommitMetiers"))
              .build();
        }
      }
    }
    return getStreamCommitMetiersMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static CommitMetierServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<CommitMetierServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<CommitMetierServiceStub>() {
        @Override
        public CommitMetierServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new CommitMetierServiceStub(channel, callOptions);
        }
      };
    return CommitMetierServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static CommitMetierServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<CommitMetierServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<CommitMetierServiceBlockingStub>() {
        @Override
        public CommitMetierServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new CommitMetierServiceBlockingStub(channel, callOptions);
        }
      };
    return CommitMetierServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static CommitMetierServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<CommitMetierServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<CommitMetierServiceFutureStub>() {
        @Override
        public CommitMetierServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new CommitMetierServiceFutureStub(channel, callOptions);
        }
      };
    return CommitMetierServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class CommitMetierServiceImplBase implements io.grpc.BindableService {

    /**
     */
    public io.grpc.stub.StreamObserver<CommitMetierRequest> streamCommitMetiers(
        io.grpc.stub.StreamObserver<CommitMetierResponse> responseObserver) {
      return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getStreamCommitMetiersMethod(), responseObserver);
    }

    @Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getStreamCommitMetiersMethod(),
            io.grpc.stub.ServerCalls.asyncBidiStreamingCall(
              new MethodHandlers<
                CommitMetierRequest,
                CommitMetierResponse>(
                  this, METHODID_STREAM_COMMIT_METIERS)))
          .build();
    }
  }

  /**
   */
  public static final class CommitMetierServiceStub extends io.grpc.stub.AbstractAsyncStub<CommitMetierServiceStub> {
    private CommitMetierServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected CommitMetierServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new CommitMetierServiceStub(channel, callOptions);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<CommitMetierRequest> streamCommitMetiers(
        io.grpc.stub.StreamObserver<CommitMetierResponse> responseObserver) {
      return io.grpc.stub.ClientCalls.asyncBidiStreamingCall(
          getChannel().newCall(getStreamCommitMetiersMethod(), getCallOptions()), responseObserver);
    }
  }

  /**
   */
  public static final class CommitMetierServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<CommitMetierServiceBlockingStub> {
    private CommitMetierServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected CommitMetierServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new CommitMetierServiceBlockingStub(channel, callOptions);
    }
  }

  /**
   */
  public static final class CommitMetierServiceFutureStub extends io.grpc.stub.AbstractFutureStub<CommitMetierServiceFutureStub> {
    private CommitMetierServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected CommitMetierServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new CommitMetierServiceFutureStub(channel, callOptions);
    }
  }

  private static final int METHODID_STREAM_COMMIT_METIERS = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final CommitMetierServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(CommitMetierServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }

    @Override
    @SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_STREAM_COMMIT_METIERS:
          return (io.grpc.stub.StreamObserver<Req>) serviceImpl.streamCommitMetiers(
              (io.grpc.stub.StreamObserver<CommitMetierResponse>) responseObserver);
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class CommitMetierServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    CommitMetierServiceBaseDescriptorSupplier() {}

    @Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return CommitMetierGrpc.getDescriptor();
    }

    @Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("CommitMetierService");
    }
  }

  private static final class CommitMetierServiceFileDescriptorSupplier
      extends CommitMetierServiceBaseDescriptorSupplier {
    CommitMetierServiceFileDescriptorSupplier() {}
  }

  private static final class CommitMetierServiceMethodDescriptorSupplier
      extends CommitMetierServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    CommitMetierServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (CommitMetierServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new CommitMetierServiceFileDescriptorSupplier())
              .addMethod(getStreamCommitMetiersMethod())
              .build();
        }
      }
    }
    return result;
  }
}
