package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.notification;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 *
 */
@javax.annotation.Generated(value = "by gRPC proto compiler (version 1.35.0)", comments = "Source: Notification.proto")
public final class NotificationServiceGrpc {

    private NotificationServiceGrpc() {
    }

    public static final String SERVICE_NAME = "com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.NotificationService";

    // Static method descriptors that strictly reflect the proto.
    private static volatile io.grpc.MethodDescriptor<NotificationRequest, NotificationResponse> getStreamNotificationsMethod;

    @io.grpc.stub.annotations.RpcMethod(fullMethodName = SERVICE_NAME + '/' + "streamNotifications",
            requestType = NotificationRequest.class,
            responseType = NotificationResponse.class,
            methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
    public static io.grpc.MethodDescriptor<NotificationRequest, NotificationResponse> getStreamNotificationsMethod() {
        io.grpc.MethodDescriptor<NotificationRequest, NotificationResponse> getStreamNotificationsMethod;
        if ((getStreamNotificationsMethod = NotificationServiceGrpc.getStreamNotificationsMethod) == null) {
            synchronized (NotificationServiceGrpc.class) {
                if ((getStreamNotificationsMethod = NotificationServiceGrpc.getStreamNotificationsMethod) == null) {
                    NotificationServiceGrpc.getStreamNotificationsMethod = getStreamNotificationsMethod = io.grpc.MethodDescriptor.<NotificationRequest, NotificationResponse>newBuilder()
                            .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING).setFullMethodName(generateFullMethodName(SERVICE_NAME, "streamNotifications"))
                            .setSampledToLocalTracing(true).setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(NotificationRequest.getDefaultInstance()))
                            .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(NotificationResponse.getDefaultInstance()))
                            .setSchemaDescriptor(new NotificationServiceMethodDescriptorSupplier("streamNotifications")).build();
                }
            }
        }
        return getStreamNotificationsMethod;
    }

    /**
     * Creates a new async stub that supports all call types for the service
     */
    public static NotificationServiceStub newStub(io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<NotificationServiceStub> factory = new io.grpc.stub.AbstractStub.StubFactory<NotificationServiceStub>() {
            @Override
            public NotificationServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                return new NotificationServiceStub(channel, callOptions);
            }
        };
        return NotificationServiceStub.newStub(factory, channel);
    }

    /**
     * Creates a new blocking-style stub that supports unary and streaming output calls on the service
     */
    public static NotificationServiceBlockingStub newBlockingStub(io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<NotificationServiceBlockingStub> factory = new io.grpc.stub.AbstractStub.StubFactory<NotificationServiceBlockingStub>() {
            @Override
            public NotificationServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                return new NotificationServiceBlockingStub(channel, callOptions);
            }
        };
        return NotificationServiceBlockingStub.newStub(factory, channel);
    }

    /**
     * Creates a new ListenableFuture-style stub that supports unary calls on the service
     */
    public static NotificationServiceFutureStub newFutureStub(io.grpc.Channel channel) {
        io.grpc.stub.AbstractStub.StubFactory<NotificationServiceFutureStub> factory = new io.grpc.stub.AbstractStub.StubFactory<NotificationServiceFutureStub>() {
            @Override
            public NotificationServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
                return new NotificationServiceFutureStub(channel, callOptions);
            }
        };
        return NotificationServiceFutureStub.newStub(factory, channel);
    }

    /**
     *
     */
    public static abstract class NotificationServiceImplBase implements io.grpc.BindableService {

        /**
         *
         */
        public void streamNotifications(NotificationRequest request, io.grpc.stub.StreamObserver<NotificationResponse> responseObserver) {
            io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getStreamNotificationsMethod(), responseObserver);
        }

        @Override
        public final io.grpc.ServerServiceDefinition bindService() {
            return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor()).addMethod(getStreamNotificationsMethod(),
                    io.grpc.stub.ServerCalls.asyncServerStreamingCall(new MethodHandlers<NotificationRequest, NotificationResponse>(this, METHODID_STREAM_NOTIFICATIONS))).build();
        }
    }

    /**
     *
     */
    public static final class NotificationServiceStub extends io.grpc.stub.AbstractAsyncStub<NotificationServiceStub> {
        private NotificationServiceStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @Override
        protected NotificationServiceStub build(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new NotificationServiceStub(channel, callOptions);
        }

        /**
         *
         */
        public void streamNotifications(NotificationRequest request, io.grpc.stub.StreamObserver<NotificationResponse> responseObserver) {
            io.grpc.stub.ClientCalls.asyncServerStreamingCall(getChannel().newCall(getStreamNotificationsMethod(), getCallOptions()), request, responseObserver);
        }
    }

    /**
     *
     */
    public static final class NotificationServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<NotificationServiceBlockingStub> {
        private NotificationServiceBlockingStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @Override
        protected NotificationServiceBlockingStub build(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new NotificationServiceBlockingStub(channel, callOptions);
        }

        /**
         *
         */
        public java.util.Iterator<NotificationResponse> streamNotifications(NotificationRequest request) {
            return io.grpc.stub.ClientCalls.blockingServerStreamingCall(getChannel(), getStreamNotificationsMethod(), getCallOptions(), request);
        }
    }

    /**
     *
     */
    public static final class NotificationServiceFutureStub extends io.grpc.stub.AbstractFutureStub<NotificationServiceFutureStub> {
        private NotificationServiceFutureStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            super(channel, callOptions);
        }

        @Override
        protected NotificationServiceFutureStub build(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
            return new NotificationServiceFutureStub(channel, callOptions);
        }
    }

    private static final int METHODID_STREAM_NOTIFICATIONS = 0;

    private static final class MethodHandlers<Req, Resp> implements io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>, io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
            io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>, io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
        private final NotificationServiceImplBase serviceImpl;
        private final int methodId;

        MethodHandlers(NotificationServiceImplBase serviceImpl, int methodId) {
            this.serviceImpl = serviceImpl;
            this.methodId = methodId;
        }

        @Override
        @SuppressWarnings("unchecked")
        public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
            switch (methodId) {
                case METHODID_STREAM_NOTIFICATIONS:
                    serviceImpl.streamNotifications((NotificationRequest) request, (io.grpc.stub.StreamObserver<NotificationResponse>) responseObserver);
                    break;
                default:
                    throw new AssertionError();
            }
        }

        @Override
        @SuppressWarnings("unchecked")
        public io.grpc.stub.StreamObserver<Req> invoke(io.grpc.stub.StreamObserver<Resp> responseObserver) {
            switch (methodId) {
                default:
                    throw new AssertionError();
            }
        }
    }

    private static abstract class NotificationServiceBaseDescriptorSupplier
            implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
        NotificationServiceBaseDescriptorSupplier() {
        }

        @Override
        public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
            return NotificationGrpc.getDescriptor();
        }

        @Override
        public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
            return getFileDescriptor().findServiceByName("NotificationService");
        }
    }

    private static final class NotificationServiceFileDescriptorSupplier extends NotificationServiceBaseDescriptorSupplier {
        NotificationServiceFileDescriptorSupplier() {
        }
    }

    private static final class NotificationServiceMethodDescriptorSupplier extends NotificationServiceBaseDescriptorSupplier
            implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
        private final String methodName;

        NotificationServiceMethodDescriptorSupplier(String methodName) {
            this.methodName = methodName;
        }

        @Override
        public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
            return getServiceDescriptor().findMethodByName(methodName);
        }
    }

    private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

    public static io.grpc.ServiceDescriptor getServiceDescriptor() {
        io.grpc.ServiceDescriptor result = serviceDescriptor;
        if (result == null) {
            synchronized (NotificationServiceGrpc.class) {
                result = serviceDescriptor;
                if (result == null) {
                    serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME).setSchemaDescriptor(new NotificationServiceFileDescriptorSupplier())
                            .addMethod(getStreamNotificationsMethod()).build();
                }
            }
        }
        return result;
    }
}
