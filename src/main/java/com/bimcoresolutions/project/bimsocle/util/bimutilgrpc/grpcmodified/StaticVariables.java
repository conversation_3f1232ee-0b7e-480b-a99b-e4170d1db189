package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcmodified;

import io.grpc.Context;
import io.grpc.Metadata;

public class StaticVariables {

    public static final String METHOD_AUTHENTICATION = "Authentication";
    public static final String HEADER_AUTHORIZATION = "Authorization";
    public static final Metadata.Key<String> METADATA_KEY_AUTHORIZATION = Metadata.Key.of(HEADER_AUTHORIZATION, Metadata.ASCII_STRING_MARSHALLER);
    public static final Context.Key<String> CONTEXT_KEY_TOKEN = Context.key("token");

}
