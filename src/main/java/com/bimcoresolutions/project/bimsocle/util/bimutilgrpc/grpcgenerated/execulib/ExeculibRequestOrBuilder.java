// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Execulib.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib;

public interface ExeculibRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string database = 1;</code>
   * @return The database.
   */
  String getDatabase();
  /**
   * <code>string database = 1;</code>
   * @return The bytes for database.
   */
  com.google.protobuf.ByteString
      getDatabaseBytes();

  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The type.
   */
  ERequestType getType();

  /**
   * <code>int32 priority = 3;</code>
   * @return The priority.
   */
  int getPriority();

  /**
   * <code>int64 instant = 4;</code>
   * @return The instant.
   */
  long getInstant();

  /**
   * <code>string message = 5;</code>
   * @return The message.
   */
  String getMessage();
  /**
   * <code>string message = 5;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>string uuid = 6;</code>
   * @return The uuid.
   */
  String getUuid();
  /**
   * <code>string uuid = 6;</code>
   * @return The bytes for uuid.
   */
  com.google.protobuf.ByteString
      getUuidBytes();
}
