// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: IdMetier.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.idmetier;

public final class IdMetierGrpc {
  private IdMetierGrpc() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\016IdMetier.proto\022Dcom.bimcoresolutions.p" +
      "roject.bimsocle.util.bimutilgrpc.grpcgen" +
      "erated\"0\n\017IdMetierRequest\022\014\n\004uuid\030\001 \001(\t\022" +
      "\017\n\007content\030\002 \001(\t\"1\n\020IdMetierResponse\022\014\n\004" +
      "uuid\030\001 \001(\t\022\017\n\007content\030\002 \001(\t2\330\001\n\017IdMetier" +
      "Service\022\304\001\n\017streamIdMetiers\022U.com.bimcor" +
      "esolutions.project.bimsocle.util.bimutil" +
      "grpc.grpcgenerated.IdMetierRequest\032V.com" +
      ".bimcoresolutions.project.bimsocle.util." +
      "bimutilgrpc.grpcgenerated.IdMetierRespon" +
      "se(\0010\001Ba\nMcom.bimcoresolutions.project.b" +
      "imsocle.util.bimutilgrpc.grpcgenerated.i" +
      "dmetierB\014IdMetierGrpcH\001P\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierRequest_descriptor,
        new String[] { "Uuid", "Content", });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_IdMetierResponse_descriptor,
        new String[] { "Uuid", "Content", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
