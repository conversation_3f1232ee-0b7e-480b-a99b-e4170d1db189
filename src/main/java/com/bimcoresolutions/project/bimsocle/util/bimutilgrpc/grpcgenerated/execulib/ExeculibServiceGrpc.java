package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.35.0)",
    comments = "Source: Execulib.proto")
public final class ExeculibServiceGrpc {

  private ExeculibServiceGrpc() {}

  public static final String SERVICE_NAME = "com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getSyncRequestMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "syncRequest",
      requestType = ExeculibRequest.class,
      responseType = ExeculibResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getSyncRequestMethod() {
    io.grpc.MethodDescriptor<ExeculibRequest, ExeculibResponse> getSyncRequestMethod;
    if ((getSyncRequestMethod = ExeculibServiceGrpc.getSyncRequestMethod) == null) {
      synchronized (ExeculibServiceGrpc.class) {
        if ((getSyncRequestMethod = ExeculibServiceGrpc.getSyncRequestMethod) == null) {
          ExeculibServiceGrpc.getSyncRequestMethod = getSyncRequestMethod =
              io.grpc.MethodDescriptor.<ExeculibRequest, ExeculibResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "syncRequest"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ExeculibServiceMethodDescriptorSupplier("syncRequest"))
              .build();
        }
      }
    }
    return getSyncRequestMethod;
  }

  private static volatile io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getStreamResponseMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "streamResponse",
      requestType = ExeculibRequest.class,
      responseType = ExeculibResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
  public static io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getStreamResponseMethod() {
    io.grpc.MethodDescriptor<ExeculibRequest, ExeculibResponse> getStreamResponseMethod;
    if ((getStreamResponseMethod = ExeculibServiceGrpc.getStreamResponseMethod) == null) {
      synchronized (ExeculibServiceGrpc.class) {
        if ((getStreamResponseMethod = ExeculibServiceGrpc.getStreamResponseMethod) == null) {
          ExeculibServiceGrpc.getStreamResponseMethod = getStreamResponseMethod =
              io.grpc.MethodDescriptor.<ExeculibRequest, ExeculibResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "streamResponse"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ExeculibServiceMethodDescriptorSupplier("streamResponse"))
              .build();
        }
      }
    }
    return getStreamResponseMethod;
  }

  private static volatile io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getStreamRequestMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "streamRequest",
      requestType = ExeculibRequest.class,
      responseType = ExeculibResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.CLIENT_STREAMING)
  public static io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getStreamRequestMethod() {
    io.grpc.MethodDescriptor<ExeculibRequest, ExeculibResponse> getStreamRequestMethod;
    if ((getStreamRequestMethod = ExeculibServiceGrpc.getStreamRequestMethod) == null) {
      synchronized (ExeculibServiceGrpc.class) {
        if ((getStreamRequestMethod = ExeculibServiceGrpc.getStreamRequestMethod) == null) {
          ExeculibServiceGrpc.getStreamRequestMethod = getStreamRequestMethod =
              io.grpc.MethodDescriptor.<ExeculibRequest, ExeculibResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.CLIENT_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "streamRequest"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ExeculibServiceMethodDescriptorSupplier("streamRequest"))
              .build();
        }
      }
    }
    return getStreamRequestMethod;
  }

  private static volatile io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getBidirStreamMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "bidirStream",
      requestType = ExeculibRequest.class,
      responseType = ExeculibResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
  public static io.grpc.MethodDescriptor<ExeculibRequest,
      ExeculibResponse> getBidirStreamMethod() {
    io.grpc.MethodDescriptor<ExeculibRequest, ExeculibResponse> getBidirStreamMethod;
    if ((getBidirStreamMethod = ExeculibServiceGrpc.getBidirStreamMethod) == null) {
      synchronized (ExeculibServiceGrpc.class) {
        if ((getBidirStreamMethod = ExeculibServiceGrpc.getBidirStreamMethod) == null) {
          ExeculibServiceGrpc.getBidirStreamMethod = getBidirStreamMethod =
              io.grpc.MethodDescriptor.<ExeculibRequest, ExeculibResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "bidirStream"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  ExeculibResponse.getDefaultInstance()))
              .setSchemaDescriptor(new ExeculibServiceMethodDescriptorSupplier("bidirStream"))
              .build();
        }
      }
    }
    return getBidirStreamMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static ExeculibServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ExeculibServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ExeculibServiceStub>() {
        @Override
        public ExeculibServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ExeculibServiceStub(channel, callOptions);
        }
      };
    return ExeculibServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static ExeculibServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ExeculibServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ExeculibServiceBlockingStub>() {
        @Override
        public ExeculibServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ExeculibServiceBlockingStub(channel, callOptions);
        }
      };
    return ExeculibServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static ExeculibServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<ExeculibServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<ExeculibServiceFutureStub>() {
        @Override
        public ExeculibServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new ExeculibServiceFutureStub(channel, callOptions);
        }
      };
    return ExeculibServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class ExeculibServiceImplBase implements io.grpc.BindableService {

    /**
     */
    public void syncRequest(ExeculibRequest request,
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getSyncRequestMethod(), responseObserver);
    }

    /**
     */
    public void streamResponse(ExeculibRequest request,
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getStreamResponseMethod(), responseObserver);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<ExeculibRequest> streamRequest(
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getStreamRequestMethod(), responseObserver);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<ExeculibRequest> bidirStream(
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getBidirStreamMethod(), responseObserver);
    }

    @Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getSyncRequestMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                ExeculibRequest,
                ExeculibResponse>(
                  this, METHODID_SYNC_REQUEST)))
          .addMethod(
            getStreamResponseMethod(),
            io.grpc.stub.ServerCalls.asyncServerStreamingCall(
              new MethodHandlers<
                ExeculibRequest,
                ExeculibResponse>(
                  this, METHODID_STREAM_RESPONSE)))
          .addMethod(
            getStreamRequestMethod(),
            io.grpc.stub.ServerCalls.asyncClientStreamingCall(
              new MethodHandlers<
                ExeculibRequest,
                ExeculibResponse>(
                  this, METHODID_STREAM_REQUEST)))
          .addMethod(
            getBidirStreamMethod(),
            io.grpc.stub.ServerCalls.asyncBidiStreamingCall(
              new MethodHandlers<
                ExeculibRequest,
                ExeculibResponse>(
                  this, METHODID_BIDIR_STREAM)))
          .build();
    }
  }

  /**
   */
  public static final class ExeculibServiceStub extends io.grpc.stub.AbstractAsyncStub<ExeculibServiceStub> {
    private ExeculibServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected ExeculibServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ExeculibServiceStub(channel, callOptions);
    }

    /**
     */
    public void syncRequest(ExeculibRequest request,
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getSyncRequestMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public void streamResponse(ExeculibRequest request,
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncServerStreamingCall(
          getChannel().newCall(getStreamResponseMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<ExeculibRequest> streamRequest(
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      return io.grpc.stub.ClientCalls.asyncClientStreamingCall(
          getChannel().newCall(getStreamRequestMethod(), getCallOptions()), responseObserver);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<ExeculibRequest> bidirStream(
        io.grpc.stub.StreamObserver<ExeculibResponse> responseObserver) {
      return io.grpc.stub.ClientCalls.asyncBidiStreamingCall(
          getChannel().newCall(getBidirStreamMethod(), getCallOptions()), responseObserver);
    }
  }

  /**
   */
  public static final class ExeculibServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<ExeculibServiceBlockingStub> {
    private ExeculibServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected ExeculibServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ExeculibServiceBlockingStub(channel, callOptions);
    }

    /**
     */
    public ExeculibResponse syncRequest(ExeculibRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getSyncRequestMethod(), getCallOptions(), request);
    }

    /**
     */
    public java.util.Iterator<ExeculibResponse> streamResponse(
        ExeculibRequest request) {
      return io.grpc.stub.ClientCalls.blockingServerStreamingCall(
          getChannel(), getStreamResponseMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class ExeculibServiceFutureStub extends io.grpc.stub.AbstractFutureStub<ExeculibServiceFutureStub> {
    private ExeculibServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected ExeculibServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new ExeculibServiceFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<ExeculibResponse> syncRequest(
        ExeculibRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getSyncRequestMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_SYNC_REQUEST = 0;
  private static final int METHODID_STREAM_RESPONSE = 1;
  private static final int METHODID_STREAM_REQUEST = 2;
  private static final int METHODID_BIDIR_STREAM = 3;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final ExeculibServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(ExeculibServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_SYNC_REQUEST:
          serviceImpl.syncRequest((ExeculibRequest) request,
              (io.grpc.stub.StreamObserver<ExeculibResponse>) responseObserver);
          break;
        case METHODID_STREAM_RESPONSE:
          serviceImpl.streamResponse((ExeculibRequest) request,
              (io.grpc.stub.StreamObserver<ExeculibResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @Override
    @SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_STREAM_REQUEST:
          return (io.grpc.stub.StreamObserver<Req>) serviceImpl.streamRequest(
              (io.grpc.stub.StreamObserver<ExeculibResponse>) responseObserver);
        case METHODID_BIDIR_STREAM:
          return (io.grpc.stub.StreamObserver<Req>) serviceImpl.bidirStream(
              (io.grpc.stub.StreamObserver<ExeculibResponse>) responseObserver);
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class ExeculibServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    ExeculibServiceBaseDescriptorSupplier() {}

    @Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return ExeculibGrpc.getDescriptor();
    }

    @Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("ExeculibService");
    }
  }

  private static final class ExeculibServiceFileDescriptorSupplier
      extends ExeculibServiceBaseDescriptorSupplier {
    ExeculibServiceFileDescriptorSupplier() {}
  }

  private static final class ExeculibServiceMethodDescriptorSupplier
      extends ExeculibServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    ExeculibServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (ExeculibServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new ExeculibServiceFileDescriptorSupplier())
              .addMethod(getSyncRequestMethod())
              .addMethod(getStreamResponseMethod())
              .addMethod(getStreamRequestMethod())
              .addMethod(getBidirStreamMethod())
              .build();
        }
      }
    }
    return result;
  }
}
