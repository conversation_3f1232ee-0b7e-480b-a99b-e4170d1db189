// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Execulib.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib;

public final class ExeculibGrpc {
  private ExeculibGrpc() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_ContentEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_ContentEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\016Execulib.proto\022Dcom.bimcoresolutions.p" +
      "roject.bimsocle.util.bimutilgrpc.grpcgen" +
      "erated\"\307\001\n\017ExeculibRequest\022\020\n\010database\030\001" +
      " \001(\t\022`\n\004type\030\002 \001(\0162R.com.bimcoresolution" +
      "s.project.bimsocle.util.bimutilgrpc.grpc" +
      "generated.ERequestType\022\020\n\010priority\030\003 \001(\005" +
      "\022\017\n\007instant\030\004 \001(\003\022\017\n\007message\030\005 \001(\t\022\014\n\004uu" +
      "id\030\006 \001(\t\"\351\004\n\020ExeculibResponse\022\014\n\004uuid\030\001 " +
      "\001(\t\022`\n\004type\030\002 \001(\0162R.com.bimcoresolutions" +
      ".project.bimsocle.util.bimutilgrpc.grpcg" +
      "enerated.ERequestType\022\017\n\007instant\030\003 \001(\003\022m" +
      "\n\006result\030\004 \001(\0132].com.bimcoresolutions.pr" +
      "oject.bimsocle.util.bimutilgrpc.grpcgene" +
      "rated.ExeculibResponse.Result\032\344\002\n\006Result" +
      "\022\021\n\tcompleted\030\001 \001(\010\022r\n\005error\030\002 \001(\0132c.com" +
      ".bimcoresolutions.project.bimsocle.util." +
      "bimutilgrpc.grpcgenerated.ExeculibRespon" +
      "se.Result.Error\022{\n\007content\030\003 \003(\0132j.com.b" +
      "imcoresolutions.project.bimsocle.util.bi" +
      "mutilgrpc.grpcgenerated.ExeculibResponse" +
      ".Result.ContentEntry\032&\n\005Error\022\014\n\004code\030\001 " +
      "\001(\t\022\017\n\007message\030\002 \001(\t\032.\n\014ContentEntry\022\013\n\003" +
      "key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001*\245\001\n\014EReques" +
      "tType\022\013\n\007COMMAND\020\000\022\n\n\006CREATE\020\001\022\n\n\006INSERT" +
      "\020\002\022\n\n\006UPDATE\020\003\022\024\n\020INSERT_OR_UPDATE\020\004\022\n\n\006" +
      "DELETE\020\005\022\n\n\006SELECT\020\006\022\016\n\nDIRECT_GET\020\007\022\021\n\r" +
      "DIRECT_MODIFY\020\010\022\023\n\017DIRECT_MULTIPLE\020\t2\232\006\n" +
      "\017ExeculibService\022\274\001\n\013syncRequest\022U.com.b" +
      "imcoresolutions.project.bimsocle.util.bi" +
      "mutilgrpc.grpcgenerated.ExeculibRequest\032" +
      "V.com.bimcoresolutions.project.bimsocle." +
      "util.bimutilgrpc.grpcgenerated.ExeculibR" +
      "esponse\022\301\001\n\016streamResponse\022U.com.bimcore" +
      "solutions.project.bimsocle.util.bimutilg" +
      "rpc.grpcgenerated.ExeculibRequest\032V.com." +
      "bimcoresolutions.project.bimsocle.util.b" +
      "imutilgrpc.grpcgenerated.ExeculibRespons" +
      "e0\001\022\300\001\n\rstreamRequest\022U.com.bimcoresolut" +
      "ions.project.bimsocle.util.bimutilgrpc.g" +
      "rpcgenerated.ExeculibRequest\032V.com.bimco" +
      "resolutions.project.bimsocle.util.bimuti" +
      "lgrpc.grpcgenerated.ExeculibResponse(\001\022\300" +
      "\001\n\013bidirStream\022U.com.bimcoresolutions.pr" +
      "oject.bimsocle.util.bimutilgrpc.grpcgene" +
      "rated.ExeculibRequest\032V.com.bimcoresolut" +
      "ions.project.bimsocle.util.bimutilgrpc.g" +
      "rpcgenerated.ExeculibResponse(\0010\001Ba\nMcom" +
      ".bimcoresolutions.project.bimsocle.util." +
      "bimutilgrpc.grpcgenerated.execulibB\014Exec" +
      "ulibGrpcH\001P\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_descriptor,
        new String[] { "Database", "Type", "Priority", "Instant", "Message", "Uuid", });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_descriptor,
        new String[] { "Uuid", "Type", "Instant", "Result", });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor =
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_descriptor.getNestedTypes().getFirst();
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor,
        new String[] { "Completed", "Error", "Content", });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_descriptor =
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor.getNestedTypes().get(0);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_descriptor,
        new String[] { "Code", "Message", });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_ContentEntry_descriptor =
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor.getNestedTypes().get(1);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_ContentEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_ContentEntry_descriptor,
        new String[] { "Key", "Value", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
