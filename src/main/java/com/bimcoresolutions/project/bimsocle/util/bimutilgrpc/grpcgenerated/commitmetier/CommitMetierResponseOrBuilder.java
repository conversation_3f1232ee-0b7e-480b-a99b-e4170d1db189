// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CommitMetier.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.commitmetier;

public interface CommitMetierResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.CommitMetierResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string uuid = 1;</code>
   * @return The uuid.
   */
  String getUuid();
  /**
   * <code>string uuid = 1;</code>
   * @return The bytes for uuid.
   */
  com.google.protobuf.ByteString
      getUuidBytes();

  /**
   * <code>string content = 2;</code>
   * @return The content.
   */
  String getContent();
  /**
   * <code>string content = 2;</code>
   * @return The bytes for content.
   */
  com.google.protobuf.ByteString
      getContentBytes();
}
