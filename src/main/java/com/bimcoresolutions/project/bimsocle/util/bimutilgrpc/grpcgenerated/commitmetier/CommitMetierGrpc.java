// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CommitMetier.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.commitmetier;

public final class CommitMetierGrpc {
  private CommitMetierGrpc() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\022CommitMetier.proto\022Dcom.bimcoresolutio" +
      "ns.project.bimsocle.util.bimutilgrpc.grp" +
      "cgenerated\"4\n\023CommitMetierRequest\022\014\n\004uui" +
      "d\030\001 \001(\t\022\017\n\007content\030\002 \001(\t\"5\n\024CommitMetier" +
      "Response\022\014\n\004uuid\030\001 \001(\t\022\017\n\007content\030\002 \001(\t2" +
      "\350\001\n\023CommitMetierService\022\320\001\n\023streamCommit" +
      "Metiers\022Y.com.bimcoresolutions.project.b" +
      "imsocle.util.bimutilgrpc.grpcgenerated.C" +
      "ommitMetierRequest\032Z.com.bimcoresolution" +
      "s.project.bimsocle.util.bimutilgrpc.grpc" +
      "generated.CommitMetierResponse(\0010\001Bi\nQco" +
      "m.bimcoresolutions.project.bimsocle.util" +
      ".bimutilgrpc.grpcgenerated.commitmetierB" +
      "\020CommitMetierGrpcH\001P\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierRequest_descriptor,
        new String[] { "Uuid", "Content", });
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_CommitMetierResponse_descriptor,
        new String[] { "Uuid", "Content", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
