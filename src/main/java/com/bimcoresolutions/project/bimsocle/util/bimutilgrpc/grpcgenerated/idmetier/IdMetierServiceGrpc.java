package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.idmetier;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.35.0)",
    comments = "Source: IdMetier.proto")
public final class IdMetierServiceGrpc {

  private IdMetierServiceGrpc() {}

  public static final String SERVICE_NAME = "com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.IdMetierService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<IdMetierRequest,
      IdMetierResponse> getStreamIdMetiersMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "streamIdMetiers",
      requestType = IdMetierRequest.class,
      responseType = IdMetierResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
  public static io.grpc.MethodDescriptor<IdMetierRequest,
      IdMetierResponse> getStreamIdMetiersMethod() {
    io.grpc.MethodDescriptor<IdMetierRequest, IdMetierResponse> getStreamIdMetiersMethod;
    if ((getStreamIdMetiersMethod = IdMetierServiceGrpc.getStreamIdMetiersMethod) == null) {
      synchronized (IdMetierServiceGrpc.class) {
        if ((getStreamIdMetiersMethod = IdMetierServiceGrpc.getStreamIdMetiersMethod) == null) {
          IdMetierServiceGrpc.getStreamIdMetiersMethod = getStreamIdMetiersMethod =
              io.grpc.MethodDescriptor.<IdMetierRequest, IdMetierResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.BIDI_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "streamIdMetiers"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  IdMetierRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  IdMetierResponse.getDefaultInstance()))
              .setSchemaDescriptor(new IdMetierServiceMethodDescriptorSupplier("streamIdMetiers"))
              .build();
        }
      }
    }
    return getStreamIdMetiersMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static IdMetierServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<IdMetierServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<IdMetierServiceStub>() {
        @Override
        public IdMetierServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new IdMetierServiceStub(channel, callOptions);
        }
      };
    return IdMetierServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static IdMetierServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<IdMetierServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<IdMetierServiceBlockingStub>() {
        @Override
        public IdMetierServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new IdMetierServiceBlockingStub(channel, callOptions);
        }
      };
    return IdMetierServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static IdMetierServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<IdMetierServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<IdMetierServiceFutureStub>() {
        @Override
        public IdMetierServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new IdMetierServiceFutureStub(channel, callOptions);
        }
      };
    return IdMetierServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class IdMetierServiceImplBase implements io.grpc.BindableService {

    /**
     */
    public io.grpc.stub.StreamObserver<IdMetierRequest> streamIdMetiers(
        io.grpc.stub.StreamObserver<IdMetierResponse> responseObserver) {
      return io.grpc.stub.ServerCalls.asyncUnimplementedStreamingCall(getStreamIdMetiersMethod(), responseObserver);
    }

    @Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getStreamIdMetiersMethod(),
            io.grpc.stub.ServerCalls.asyncBidiStreamingCall(
              new MethodHandlers<
                IdMetierRequest,
                IdMetierResponse>(
                  this, METHODID_STREAM_ID_METIERS)))
          .build();
    }
  }

  /**
   */
  public static final class IdMetierServiceStub extends io.grpc.stub.AbstractAsyncStub<IdMetierServiceStub> {
    private IdMetierServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected IdMetierServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new IdMetierServiceStub(channel, callOptions);
    }

    /**
     */
    public io.grpc.stub.StreamObserver<IdMetierRequest> streamIdMetiers(
        io.grpc.stub.StreamObserver<IdMetierResponse> responseObserver) {
      return io.grpc.stub.ClientCalls.asyncBidiStreamingCall(
          getChannel().newCall(getStreamIdMetiersMethod(), getCallOptions()), responseObserver);
    }
  }

  /**
   */
  public static final class IdMetierServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<IdMetierServiceBlockingStub> {
    private IdMetierServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected IdMetierServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new IdMetierServiceBlockingStub(channel, callOptions);
    }
  }

  /**
   */
  public static final class IdMetierServiceFutureStub extends io.grpc.stub.AbstractFutureStub<IdMetierServiceFutureStub> {
    private IdMetierServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @Override
    protected IdMetierServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new IdMetierServiceFutureStub(channel, callOptions);
    }
  }

  private static final int METHODID_STREAM_ID_METIERS = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final IdMetierServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(IdMetierServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }

    @Override
    @SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_STREAM_ID_METIERS:
          return (io.grpc.stub.StreamObserver<Req>) serviceImpl.streamIdMetiers(
              (io.grpc.stub.StreamObserver<IdMetierResponse>) responseObserver);
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class IdMetierServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    IdMetierServiceBaseDescriptorSupplier() {}

    @Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return IdMetierGrpc.getDescriptor();
    }

    @Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("IdMetierService");
    }
  }

  private static final class IdMetierServiceFileDescriptorSupplier
      extends IdMetierServiceBaseDescriptorSupplier {
    IdMetierServiceFileDescriptorSupplier() {}
  }

  private static final class IdMetierServiceMethodDescriptorSupplier
      extends IdMetierServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    IdMetierServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (IdMetierServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new IdMetierServiceFileDescriptorSupplier())
              .addMethod(getStreamIdMetiersMethod())
              .build();
        }
      }
    }
    return result;
  }
}
