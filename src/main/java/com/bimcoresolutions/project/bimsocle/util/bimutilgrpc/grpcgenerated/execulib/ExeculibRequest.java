// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Execulib.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib;

/**
 * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibRequest}
 */
public final class ExeculibRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibRequest)
    ExeculibRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ExeculibRequest.newBuilder() to construct.
  private ExeculibRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ExeculibRequest() {
    database_ = "";
    type_ = 0;
    message_ = "";
    uuid_ = "";
  }

  @Override
  @SuppressWarnings({"unused"})
  protected Object newInstance(
      UnusedPrivateParameter unused) {
    return new ExeculibRequest();
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ExeculibRequest(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            String s = input.readStringRequireUtf8();

            database_ = s;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            type_ = rawValue;
            break;
          }
          case 24: {

            priority_ = input.readInt32();
            break;
          }
          case 32: {

            instant_ = input.readInt64();
            break;
          }
          case 42: {
            String s = input.readStringRequireUtf8();

            message_ = s;
            break;
          }
          case 50: {
            String s = input.readStringRequireUtf8();

            uuid_ = s;
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            ExeculibRequest.class, Builder.class);
  }

  public static final int DATABASE_FIELD_NUMBER = 1;
  private volatile Object database_;
  /**
   * <code>string database = 1;</code>
   * @return The database.
   */
  @Override
  public String getDatabase() {
    Object ref = database_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      database_ = s;
      return s;
    }
  }
  /**
   * <code>string database = 1;</code>
   * @return The bytes for database.
   */
  @Override
  public com.google.protobuf.ByteString
      getDatabaseBytes() {
    Object ref = database_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      database_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 2;
  private int type_;
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  @Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The type.
   */
  @Override public ERequestType getType() {
    @SuppressWarnings("deprecation")
    ERequestType result = ERequestType.valueOf(type_);
    return result == null ? ERequestType.UNRECOGNIZED : result;
  }

  public static final int PRIORITY_FIELD_NUMBER = 3;
  private int priority_;
  /**
   * <code>int32 priority = 3;</code>
   * @return The priority.
   */
  @Override
  public int getPriority() {
    return priority_;
  }

  public static final int INSTANT_FIELD_NUMBER = 4;
  private long instant_;
  /**
   * <code>int64 instant = 4;</code>
   * @return The instant.
   */
  @Override
  public long getInstant() {
    return instant_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 5;
  private volatile Object message_;
  /**
   * <code>string message = 5;</code>
   * @return The message.
   */
  @Override
  public String getMessage() {
    Object ref = message_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <code>string message = 5;</code>
   * @return The bytes for message.
   */
  @Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    Object ref = message_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int UUID_FIELD_NUMBER = 6;
  private volatile Object uuid_;
  /**
   * <code>string uuid = 6;</code>
   * @return The uuid.
   */
  @Override
  public String getUuid() {
    Object ref = uuid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      uuid_ = s;
      return s;
    }
  }
  /**
   * <code>string uuid = 6;</code>
   * @return The bytes for uuid.
   */
  @Override
  public com.google.protobuf.ByteString
      getUuidBytes() {
    Object ref = uuid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      uuid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getDatabaseBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, database_);
    }
    if (type_ != ERequestType.COMMAND.getNumber()) {
      output.writeEnum(2, type_);
    }
    if (priority_ != 0) {
      output.writeInt32(3, priority_);
    }
    if (instant_ != 0L) {
      output.writeInt64(4, instant_);
    }
    if (!getMessageBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, message_);
    }
    if (!getUuidBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, uuid_);
    }
    unknownFields.writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getDatabaseBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, database_);
    }
    if (type_ != ERequestType.COMMAND.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, type_);
    }
    if (priority_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, priority_);
    }
    if (instant_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(4, instant_);
    }
    if (!getMessageBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, message_);
    }
    if (!getUuidBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, uuid_);
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof ExeculibRequest)) {
      return super.equals(obj);
    }
    ExeculibRequest other = (ExeculibRequest) obj;

    if (!getDatabase()
        .equals(other.getDatabase())) return false;
    if (type_ != other.type_) return false;
    if (getPriority()
        != other.getPriority()) return false;
    if (getInstant()
        != other.getInstant()) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (!getUuid()
        .equals(other.getUuid())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + DATABASE_FIELD_NUMBER;
    hash = (53 * hash) + getDatabase().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + PRIORITY_FIELD_NUMBER;
    hash = (53 * hash) + getPriority();
    hash = (37 * hash) + INSTANT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getInstant());
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    hash = (37 * hash) + UUID_FIELD_NUMBER;
    hash = (53 * hash) + getUuid().hashCode();
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static ExeculibRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static ExeculibRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static ExeculibRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static ExeculibRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static ExeculibRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static ExeculibRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static ExeculibRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static ExeculibRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static ExeculibRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static ExeculibRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static ExeculibRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static ExeculibRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(ExeculibRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibRequest)
      ExeculibRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ExeculibRequest.class, Builder.class);
    }

    // Construct using com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib.ExeculibRequest.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @Override
    public Builder clear() {
      super.clear();
      database_ = "";

      type_ = 0;

      priority_ = 0;

      instant_ = 0L;

      message_ = "";

      uuid_ = "";

      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibRequest_descriptor;
    }

    @Override
    public ExeculibRequest getDefaultInstanceForType() {
      return ExeculibRequest.getDefaultInstance();
    }

    @Override
    public ExeculibRequest build() {
      ExeculibRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public ExeculibRequest buildPartial() {
      ExeculibRequest result = new ExeculibRequest(this);
      result.database_ = database_;
      result.type_ = type_;
      result.priority_ = priority_;
      result.instant_ = instant_;
      result.message_ = message_;
      result.uuid_ = uuid_;
      onBuilt();
      return result;
    }

    @Override
    public Builder clone() {
      return super.clone();
    }
    @Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.setField(field, value);
    }
    @Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.addRepeatedField(field, value);
    }
    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof ExeculibRequest) {
        return mergeFrom((ExeculibRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(ExeculibRequest other) {
      if (other == ExeculibRequest.getDefaultInstance()) return this;
      if (!other.getDatabase().isEmpty()) {
        database_ = other.database_;
        onChanged();
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (other.getPriority() != 0) {
        setPriority(other.getPriority());
      }
      if (other.getInstant() != 0L) {
        setInstant(other.getInstant());
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        onChanged();
      }
      if (!other.getUuid().isEmpty()) {
        uuid_ = other.uuid_;
        onChanged();
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      ExeculibRequest parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (ExeculibRequest) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private Object database_ = "";
    /**
     * <code>string database = 1;</code>
     * @return The database.
     */
    public String getDatabase() {
      Object ref = database_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        database_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string database = 1;</code>
     * @return The bytes for database.
     */
    public com.google.protobuf.ByteString
        getDatabaseBytes() {
      Object ref = database_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        database_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string database = 1;</code>
     * @param value The database to set.
     * @return This builder for chaining.
     */
    public Builder setDatabase(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      database_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string database = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearDatabase() {
      
      database_ = getDefaultInstance().getDatabase();
      onChanged();
      return this;
    }
    /**
     * <code>string database = 1;</code>
     * @param value The bytes for database to set.
     * @return This builder for chaining.
     */
    public Builder setDatabaseBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      database_ = value;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @return The enum numeric value on the wire for type.
     */
    @Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @return The type.
     */
    @Override
    public ERequestType getType() {
      @SuppressWarnings("deprecation")
      ERequestType result = ERequestType.valueOf(type_);
      return result == null ? ERequestType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(ERequestType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      
      type_ = 0;
      onChanged();
      return this;
    }

    private int priority_ ;
    /**
     * <code>int32 priority = 3;</code>
     * @return The priority.
     */
    @Override
    public int getPriority() {
      return priority_;
    }
    /**
     * <code>int32 priority = 3;</code>
     * @param value The priority to set.
     * @return This builder for chaining.
     */
    public Builder setPriority(int value) {
      
      priority_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int32 priority = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearPriority() {
      
      priority_ = 0;
      onChanged();
      return this;
    }

    private long instant_ ;
    /**
     * <code>int64 instant = 4;</code>
     * @return The instant.
     */
    @Override
    public long getInstant() {
      return instant_;
    }
    /**
     * <code>int64 instant = 4;</code>
     * @param value The instant to set.
     * @return This builder for chaining.
     */
    public Builder setInstant(long value) {
      
      instant_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int64 instant = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearInstant() {
      
      instant_ = 0L;
      onChanged();
      return this;
    }

    private Object message_ = "";
    /**
     * <code>string message = 5;</code>
     * @return The message.
     */
    public String getMessage() {
      Object ref = message_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string message = 5;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string message = 5;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      message_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string message = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      
      message_ = getDefaultInstance().getMessage();
      onChanged();
      return this;
    }
    /**
     * <code>string message = 5;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      message_ = value;
      onChanged();
      return this;
    }

    private Object uuid_ = "";
    /**
     * <code>string uuid = 6;</code>
     * @return The uuid.
     */
    public String getUuid() {
      Object ref = uuid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        uuid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string uuid = 6;</code>
     * @return The bytes for uuid.
     */
    public com.google.protobuf.ByteString
        getUuidBytes() {
      Object ref = uuid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        uuid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string uuid = 6;</code>
     * @param value The uuid to set.
     * @return This builder for chaining.
     */
    public Builder setUuid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      uuid_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string uuid = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearUuid() {
      
      uuid_ = getDefaultInstance().getUuid();
      onChanged();
      return this;
    }
    /**
     * <code>string uuid = 6;</code>
     * @param value The bytes for uuid to set.
     * @return This builder for chaining.
     */
    public Builder setUuidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      uuid_ = value;
      onChanged();
      return this;
    }
    @Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibRequest)
  }

  // @@protoc_insertion_point(class_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibRequest)
  private static final ExeculibRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new ExeculibRequest();
  }

  public static ExeculibRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ExeculibRequest>
      PARSER = new com.google.protobuf.AbstractParser<>() {
      @Override
      public ExeculibRequest parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
          return new ExeculibRequest(input, extensionRegistry);
      }
  };

  public static com.google.protobuf.Parser<ExeculibRequest> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<ExeculibRequest> getParserForType() {
    return PARSER;
  }

  @Override
  public ExeculibRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

