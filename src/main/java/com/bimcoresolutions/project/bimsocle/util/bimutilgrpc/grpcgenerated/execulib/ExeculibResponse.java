// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Execulib.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib;

/**
 * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse}
 */
public final class ExeculibResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse)
    ExeculibResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use ExeculibResponse.newBuilder() to construct.
  private ExeculibResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private ExeculibResponse() {
    uuid_ = "";
    type_ = 0;
  }

  @Override
  @SuppressWarnings({"unused"})
  protected Object newInstance(
      UnusedPrivateParameter unused) {
    return new ExeculibResponse();
  }

  @Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private ExeculibResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new NullPointerException();
    }
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 10: {
            String s = input.readStringRequireUtf8();

            uuid_ = s;
            break;
          }
          case 16: {
            int rawValue = input.readEnum();

            type_ = rawValue;
            break;
          }
          case 24: {

            instant_ = input.readInt64();
            break;
          }
          case 34: {
            Result.Builder subBuilder = null;
            if (result_ != null) {
              subBuilder = result_.toBuilder();
            }
            result_ = input.readMessage(Result.parser(), extensionRegistry);
            if (subBuilder != null) {
              subBuilder.mergeFrom(result_);
              result_ = subBuilder.buildPartial();
            }

            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_descriptor;
  }

  @Override
  protected FieldAccessorTable
      internalGetFieldAccessorTable() {
    return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            ExeculibResponse.class, Builder.class);
  }

  public interface ResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>bool completed = 1;</code>
     * @return The completed.
     */
    boolean getCompleted();

    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
     * @return Whether the error field is set.
     */
    boolean hasError();
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
     * @return The error.
     */
    Result.Error getError();
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
     */
    Result.ErrorOrBuilder getErrorOrBuilder();

    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */
    int getContentCount();
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */
    boolean containsContent(
        String key);
    /**
     * Use {@link #getContentMap()} instead.
     */
    @Deprecated
    java.util.Map<String, String>
    getContent();
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */
    java.util.Map<String, String>
    getContentMap();
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */

    String getContentOrDefault(
        String key,
        String defaultValue);
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */

    String getContentOrThrow(
        String key);
  }
  /**
   * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result}
   */
  public static final class Result extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result)
      ResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Result.newBuilder() to construct.
    private Result(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Result() {
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new Result();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Result(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              completed_ = input.readBool();
              break;
            }
            case 18: {
              Error.Builder subBuilder = null;
              if (error_ != null) {
                subBuilder = error_.toBuilder();
              }
              error_ = input.readMessage(Error.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(error_);
                error_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                content_ = com.google.protobuf.MapField.newMapField(
                    ContentDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<String, String>
              content__ = input.readMessage(
                  ContentDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              content_.getMutableMap().put(
                  content__.getKey(), content__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetContent();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Result.class, Builder.class);
    }

    public interface ErrorOrBuilder extends
        // @@protoc_insertion_point(interface_extends:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>string code = 1;</code>
       * @return The code.
       */
      String getCode();
      /**
       * <code>string code = 1;</code>
       * @return The bytes for code.
       */
      com.google.protobuf.ByteString
          getCodeBytes();

      /**
       * <code>string message = 2;</code>
       * @return The message.
       */
      String getMessage();
      /**
       * <code>string message = 2;</code>
       * @return The bytes for message.
       */
      com.google.protobuf.ByteString
          getMessageBytes();
    }
    /**
     * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error}
     */
    public static final class Error extends
        com.google.protobuf.GeneratedMessageV3 implements
        // @@protoc_insertion_point(message_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error)
        ErrorOrBuilder {
    private static final long serialVersionUID = 0L;
      // Use Error.newBuilder() to construct.
      private Error(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
        super(builder);
      }
      private Error() {
        code_ = "";
        message_ = "";
      }

      @Override
      @SuppressWarnings({"unused"})
      protected Object newInstance(
          UnusedPrivateParameter unused) {
        return new Error();
      }

      @Override
      public final com.google.protobuf.UnknownFieldSet
      getUnknownFields() {
        return this.unknownFields;
      }
      private Error(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        com.google.protobuf.UnknownFieldSet.Builder unknownFields =
            com.google.protobuf.UnknownFieldSet.newBuilder();
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                String s = input.readStringRequireUtf8();

                code_ = s;
                break;
              }
              case 18: {
                String s = input.readStringRequireUtf8();

                message_ = s;
                break;
              }
              default: {
                if (!parseUnknownField(
                    input, unknownFields, extensionRegistry, tag)) {
                  done = true;
                }
                break;
              }
            }
          }
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(this);
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(
              e).setUnfinishedMessage(this);
        } finally {
          this.unknownFields = unknownFields.build();
          makeExtensionsImmutable();
        }
      }
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Error.class, Builder.class);
      }

      public static final int CODE_FIELD_NUMBER = 1;
      private volatile Object code_;
      /**
       * <code>string code = 1;</code>
       * @return The code.
       */
      @Override
      public String getCode() {
        Object ref = code_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          code_ = s;
          return s;
        }
      }
      /**
       * <code>string code = 1;</code>
       * @return The bytes for code.
       */
      @Override
      public com.google.protobuf.ByteString
          getCodeBytes() {
        Object ref = code_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          code_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      public static final int MESSAGE_FIELD_NUMBER = 2;
      private volatile Object message_;
      /**
       * <code>string message = 2;</code>
       * @return The message.
       */
      @Override
      public String getMessage() {
        Object ref = message_;
        if (ref instanceof String) {
          return (String) ref;
        } else {
          com.google.protobuf.ByteString bs = 
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          message_ = s;
          return s;
        }
      }
      /**
       * <code>string message = 2;</code>
       * @return The bytes for message.
       */
      @Override
      public com.google.protobuf.ByteString
          getMessageBytes() {
        Object ref = message_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          message_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }

      private byte memoizedIsInitialized = -1;
      @Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (!getCodeBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 1, code_);
        }
        if (!getMessageBytes().isEmpty()) {
          com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
        }
        unknownFields.writeTo(output);
      }

      @Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (!getCodeBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, code_);
        }
        if (!getMessageBytes().isEmpty()) {
          size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
        }
        size += unknownFields.getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @Override
      public boolean equals(final Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof Error)) {
          return super.equals(obj);
        }
        Error other = (Error) obj;

        if (!getCode()
            .equals(other.getCode())) return false;
        if (!getMessage()
            .equals(other.getMessage())) return false;
        if (!unknownFields.equals(other.unknownFields)) return false;
        return true;
      }

      @Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode().hashCode();
        hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getMessage().hashCode();
        hash = (29 * hash) + unknownFields.hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static Error parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static Error parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static Error parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static Error parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static Error parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static Error parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static Error parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static Error parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }
      public static Error parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input);
      }
      public static Error parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static Error parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input);
      }
      public static Error parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessageV3
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(Error prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @Override
      protected Builder newBuilderForType(
          BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error)
          ErrorOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_descriptor;
        }

        @Override
        protected FieldAccessorTable
            internalGetFieldAccessorTable() {
          return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  Error.class, Builder.class);
        }

        // Construct using com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib.ExeculibResponse.Result.Error.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessageV3
                  .alwaysUseFieldBuilders) {
          }
        }
        @Override
        public Builder clear() {
          super.clear();
          code_ = "";

          message_ = "";

          return this;
        }

        @Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_Error_descriptor;
        }

        @Override
        public Error getDefaultInstanceForType() {
          return Error.getDefaultInstance();
        }

        @Override
        public Error build() {
          Error result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @Override
        public Error buildPartial() {
          Error result = new Error(this);
          result.code_ = code_;
          result.message_ = message_;
          onBuilt();
          return result;
        }

        @Override
        public Builder clone() {
          return super.clone();
        }
        @Override
        public Builder setField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            Object value) {
          return super.setField(field, value);
        }
        @Override
        public Builder clearField(
            com.google.protobuf.Descriptors.FieldDescriptor field) {
          return super.clearField(field);
        }
        @Override
        public Builder clearOneof(
            com.google.protobuf.Descriptors.OneofDescriptor oneof) {
          return super.clearOneof(oneof);
        }
        @Override
        public Builder setRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            int index, Object value) {
          return super.setRepeatedField(field, index, value);
        }
        @Override
        public Builder addRepeatedField(
            com.google.protobuf.Descriptors.FieldDescriptor field,
            Object value) {
          return super.addRepeatedField(field, value);
        }
        @Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof Error) {
            return mergeFrom((Error)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(Error other) {
          if (other == Error.getDefaultInstance()) return this;
          if (!other.getCode().isEmpty()) {
            code_ = other.code_;
            onChanged();
          }
          if (!other.getMessage().isEmpty()) {
            message_ = other.message_;
            onChanged();
          }
          this.mergeUnknownFields(other.unknownFields);
          onChanged();
          return this;
        }

        @Override
        public final boolean isInitialized() {
          return true;
        }

        @Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          Error parsedMessage = null;
          try {
            parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            parsedMessage = (Error) e.getUnfinishedMessage();
            throw e.unwrapIOException();
          } finally {
            if (parsedMessage != null) {
              mergeFrom(parsedMessage);
            }
          }
          return this;
        }

        private Object code_ = "";
        /**
         * <code>string code = 1;</code>
         * @return The code.
         */
        public String getCode() {
          Object ref = code_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            code_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string code = 1;</code>
         * @return The bytes for code.
         */
        public com.google.protobuf.ByteString
            getCodeBytes() {
          Object ref = code_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            code_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string code = 1;</code>
         * @param value The code to set.
         * @return This builder for chaining.
         */
        public Builder setCode(
            String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          code_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string code = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearCode() {
          
          code_ = getDefaultInstance().getCode();
          onChanged();
          return this;
        }
        /**
         * <code>string code = 1;</code>
         * @param value The bytes for code to set.
         * @return This builder for chaining.
         */
        public Builder setCodeBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          code_ = value;
          onChanged();
          return this;
        }

        private Object message_ = "";
        /**
         * <code>string message = 2;</code>
         * @return The message.
         */
        public String getMessage() {
          Object ref = message_;
          if (!(ref instanceof String)) {
            com.google.protobuf.ByteString bs =
                (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            message_ = s;
            return s;
          } else {
            return (String) ref;
          }
        }
        /**
         * <code>string message = 2;</code>
         * @return The bytes for message.
         */
        public com.google.protobuf.ByteString
            getMessageBytes() {
          Object ref = message_;
          if (ref instanceof String) {
            com.google.protobuf.ByteString b = 
                com.google.protobuf.ByteString.copyFromUtf8(
                    (String) ref);
            message_ = b;
            return b;
          } else {
            return (com.google.protobuf.ByteString) ref;
          }
        }
        /**
         * <code>string message = 2;</code>
         * @param value The message to set.
         * @return This builder for chaining.
         */
        public Builder setMessage(
            String value) {
          if (value == null) {
    throw new NullPointerException();
  }
  
          message_ = value;
          onChanged();
          return this;
        }
        /**
         * <code>string message = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearMessage() {
          
          message_ = getDefaultInstance().getMessage();
          onChanged();
          return this;
        }
        /**
         * <code>string message = 2;</code>
         * @param value The bytes for message to set.
         * @return This builder for chaining.
         */
        public Builder setMessageBytes(
            com.google.protobuf.ByteString value) {
          if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
          
          message_ = value;
          onChanged();
          return this;
        }
        @Override
        public final Builder setUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.setUnknownFields(unknownFields);
        }

        @Override
        public final Builder mergeUnknownFields(
            final com.google.protobuf.UnknownFieldSet unknownFields) {
          return super.mergeUnknownFields(unknownFields);
        }


        // @@protoc_insertion_point(builder_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error)
      }

      // @@protoc_insertion_point(class_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error)
      private static final Error DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new Error();
      }

      public static Error getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Error>
          PARSER = new com.google.protobuf.AbstractParser<>() {
          @Override
          public Error parsePartialFrom(
                  com.google.protobuf.CodedInputStream input,
                  com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                  throws com.google.protobuf.InvalidProtocolBufferException {
              return new Error(input, extensionRegistry);
          }
      };

      public static com.google.protobuf.Parser<Error> parser() {
        return PARSER;
      }

      @Override
      public com.google.protobuf.Parser<Error> getParserForType() {
        return PARSER;
      }

      @Override
      public Error getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public static final int COMPLETED_FIELD_NUMBER = 1;
    private boolean completed_;
    /**
     * <code>bool completed = 1;</code>
     * @return The completed.
     */
    @Override
    public boolean getCompleted() {
      return completed_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private Error error_;
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
     * @return Whether the error field is set.
     */
    @Override
    public boolean hasError() {
      return error_ != null;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
     * @return The error.
     */
    @Override
    public Error getError() {
      return error_ == null ? Error.getDefaultInstance() : error_;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
     */
    @Override
    public ErrorOrBuilder getErrorOrBuilder() {
      return getError();
    }

    public static final int CONTENT_FIELD_NUMBER = 3;
    private static final class ContentDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          String, String> defaultEntry =
              com.google.protobuf.MapEntry
              .<String, String>newDefaultInstance(
                  ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_ContentEntry_descriptor,
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        String, String> content_;
    private com.google.protobuf.MapField<String, String>
    internalGetContent() {
      if (content_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ContentDefaultEntryHolder.defaultEntry);
      }
      return content_;
    }

    public int getContentCount() {
      return internalGetContent().getMap().size();
    }
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */

    @Override
    public boolean containsContent(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      return internalGetContent().getMap().containsKey(key);
    }
    /**
     * Use {@link #getContentMap()} instead.
     */
    @Override
    @Deprecated
    public java.util.Map<String, String> getContent() {
      return getContentMap();
    }
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */
    @Override

    public java.util.Map<String, String> getContentMap() {
      return internalGetContent().getMap();
    }
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */
    @Override

    public String getContentOrDefault(
        String key,
        String defaultValue) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetContent().getMap();
      return map.getOrDefault(key, defaultValue);
    }
    /**
     * <code>map&lt;string, string&gt; content = 3;</code>
     */
    @Override

    public String getContentOrThrow(
        String key) {
      if (key == null) { throw new NullPointerException(); }
      java.util.Map<String, String> map =
          internalGetContent().getMap();
      if (!map.containsKey(key)) {
        throw new IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (completed_ != false) {
        output.writeBool(1, completed_);
      }
      if (error_ != null) {
        output.writeMessage(2, getError());
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetContent(),
          ContentDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (completed_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, completed_);
      }
      if (error_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getError());
      }
      for (java.util.Map.Entry<String, String> entry
           : internalGetContent().getMap().entrySet()) {
        com.google.protobuf.MapEntry<String, String>
        content__ = ContentDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, content__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof Result)) {
        return super.equals(obj);
      }
      Result other = (Result) obj;

      if (getCompleted()
          != other.getCompleted()) return false;
      if (hasError() != other.hasError()) return false;
      if (hasError()) {
        if (!getError()
            .equals(other.getError())) return false;
      }
      if (!internalGetContent().equals(
          other.internalGetContent())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + COMPLETED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getCompleted());
      if (hasError()) {
        hash = (37 * hash) + ERROR_FIELD_NUMBER;
        hash = (53 * hash) + getError().hashCode();
      }
      if (!internalGetContent().getMap().isEmpty()) {
        hash = (37 * hash) + CONTENT_FIELD_NUMBER;
        hash = (53 * hash) + internalGetContent().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static Result parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Result parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Result parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Result parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Result parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Result parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Result parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static Result parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static Result parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static Result parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static Result parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static Result parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(Result prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result)
        ResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetContent();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableContent();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Result.class, Builder.class);
      }

      // Construct using com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib.ExeculibResponse.Result.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        completed_ = false;

        if (errorBuilder_ == null) {
          error_ = null;
        } else {
          error_ = null;
          errorBuilder_ = null;
        }
        internalGetMutableContent().clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_Result_descriptor;
      }

      @Override
      public Result getDefaultInstanceForType() {
        return Result.getDefaultInstance();
      }

      @Override
      public Result build() {
        Result result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public Result buildPartial() {
        Result result = new Result(this);
        int from_bitField0_ = bitField0_;
        result.completed_ = completed_;
        if (errorBuilder_ == null) {
          result.error_ = error_;
        } else {
          result.error_ = errorBuilder_.build();
        }
        result.content_ = internalGetContent();
        result.content_.makeImmutable();
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Result) {
          return mergeFrom((Result)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Result other) {
        if (other == Result.getDefaultInstance()) return this;
        if (other.getCompleted() != false) {
          setCompleted(other.getCompleted());
        }
        if (other.hasError()) {
          mergeError(other.getError());
        }
        internalGetMutableContent().mergeFrom(
            other.internalGetContent());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        Result parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (Result) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean completed_ ;
      /**
       * <code>bool completed = 1;</code>
       * @return The completed.
       */
      @Override
      public boolean getCompleted() {
        return completed_;
      }
      /**
       * <code>bool completed = 1;</code>
       * @param value The completed to set.
       * @return This builder for chaining.
       */
      public Builder setCompleted(boolean value) {
        
        completed_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool completed = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCompleted() {
        
        completed_ = false;
        onChanged();
        return this;
      }

      private Error error_;
      private com.google.protobuf.SingleFieldBuilderV3<
          Error, Error.Builder, ErrorOrBuilder> errorBuilder_;
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       * @return Whether the error field is set.
       */
      public boolean hasError() {
        return errorBuilder_ != null || error_ != null;
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       * @return The error.
       */
      public Error getError() {
        if (errorBuilder_ == null) {
          return error_ == null ? Error.getDefaultInstance() : error_;
        } else {
          return errorBuilder_.getMessage();
        }
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       */
      public Builder setError(Error value) {
        if (errorBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          error_ = value;
          onChanged();
        } else {
          errorBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       */
      public Builder setError(
          Error.Builder builderForValue) {
        if (errorBuilder_ == null) {
          error_ = builderForValue.build();
          onChanged();
        } else {
          errorBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       */
      public Builder mergeError(Error value) {
        if (errorBuilder_ == null) {
          if (error_ != null) {
            error_ =
              Error.newBuilder(error_).mergeFrom(value).buildPartial();
          } else {
            error_ = value;
          }
          onChanged();
        } else {
          errorBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       */
      public Builder clearError() {
        if (errorBuilder_ == null) {
          error_ = null;
          onChanged();
        } else {
          error_ = null;
          errorBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       */
      public Error.Builder getErrorBuilder() {
        
        onChanged();
        return getErrorFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       */
      public ErrorOrBuilder getErrorOrBuilder() {
        if (errorBuilder_ != null) {
          return errorBuilder_.getMessageOrBuilder();
        } else {
          return error_ == null ?
              Error.getDefaultInstance() : error_;
        }
      }
      /**
       * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result.Error error = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          Error, Error.Builder, ErrorOrBuilder>
          getErrorFieldBuilder() {
        if (errorBuilder_ == null) {
          errorBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<>(
                  getError(),
                  getParentForChildren(),
                  isClean());
          error_ = null;
        }
        return errorBuilder_;
      }

      private com.google.protobuf.MapField<
          String, String> content_;
      private com.google.protobuf.MapField<String, String>
      internalGetContent() {
        if (content_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ContentDefaultEntryHolder.defaultEntry);
        }
        return content_;
      }
      private com.google.protobuf.MapField<String, String>
      internalGetMutableContent() {
        onChanged();;
        if (content_ == null) {
          content_ = com.google.protobuf.MapField.newMapField(
              ContentDefaultEntryHolder.defaultEntry);
        }
        if (!content_.isMutable()) {
          content_ = content_.copy();
        }
        return content_;
      }

      public int getContentCount() {
        return internalGetContent().getMap().size();
      }
      /**
       * <code>map&lt;string, string&gt; content = 3;</code>
       */

      @Override
      public boolean containsContent(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        return internalGetContent().getMap().containsKey(key);
      }
      /**
       * Use {@link #getContentMap()} instead.
       */
      @Override
      @Deprecated
      public java.util.Map<String, String> getContent() {
        return getContentMap();
      }
      /**
       * <code>map&lt;string, string&gt; content = 3;</code>
       */
      @Override

      public java.util.Map<String, String> getContentMap() {
        return internalGetContent().getMap();
      }
      /**
       * <code>map&lt;string, string&gt; content = 3;</code>
       */
      @Override

      public String getContentOrDefault(
          String key,
          String defaultValue) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetContent().getMap();
        return map.getOrDefault(key, defaultValue);
      }
      /**
       * <code>map&lt;string, string&gt; content = 3;</code>
       */
      @Override

      public String getContentOrThrow(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        java.util.Map<String, String> map =
            internalGetContent().getMap();
        if (!map.containsKey(key)) {
          throw new IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearContent() {
        internalGetMutableContent().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; content = 3;</code>
       */

      public Builder removeContent(
          String key) {
        if (key == null) { throw new NullPointerException(); }
        internalGetMutableContent().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @Deprecated
      public java.util.Map<String, String>
      getMutableContent() {
        return internalGetMutableContent().getMutableMap();
      }
      /**
       * <code>map&lt;string, string&gt; content = 3;</code>
       */
      public Builder putContent(
          String key,
          String value) {
        if (key == null) { throw new NullPointerException(); }
        if (value == null) { throw new NullPointerException(); }
        internalGetMutableContent().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;string, string&gt; content = 3;</code>
       */

      public Builder putAllContent(
          java.util.Map<String, String> values) {
        internalGetMutableContent().getMutableMap()
            .putAll(values);
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result)
    }

    // @@protoc_insertion_point(class_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result)
    private static final Result DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new Result();
    }

    public static Result getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Result>
        PARSER = new com.google.protobuf.AbstractParser<>() {
        @Override
        public Result parsePartialFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
            return new Result(input, extensionRegistry);
        }
    };

    public static com.google.protobuf.Parser<Result> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<Result> getParserForType() {
      return PARSER;
    }

    @Override
    public Result getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public static final int UUID_FIELD_NUMBER = 1;
  private volatile Object uuid_;
  /**
   * <code>string uuid = 1;</code>
   * @return The uuid.
   */
  @Override
  public String getUuid() {
    Object ref = uuid_;
    if (ref instanceof String) {
      return (String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      String s = bs.toStringUtf8();
      uuid_ = s;
      return s;
    }
  }
  /**
   * <code>string uuid = 1;</code>
   * @return The bytes for uuid.
   */
  @Override
  public com.google.protobuf.ByteString
      getUuidBytes() {
    Object ref = uuid_;
    if (ref instanceof String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (String) ref);
      uuid_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TYPE_FIELD_NUMBER = 2;
  private int type_;
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  @Override public int getTypeValue() {
    return type_;
  }
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The type.
   */
  @Override public ERequestType getType() {
    @SuppressWarnings("deprecation")
    ERequestType result = ERequestType.valueOf(type_);
    return result == null ? ERequestType.UNRECOGNIZED : result;
  }

  public static final int INSTANT_FIELD_NUMBER = 3;
  private long instant_;
  /**
   * <code>int64 instant = 3;</code>
   * @return The instant.
   */
  @Override
  public long getInstant() {
    return instant_;
  }

  public static final int RESULT_FIELD_NUMBER = 4;
  private Result result_;
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
   * @return Whether the result field is set.
   */
  @Override
  public boolean hasResult() {
    return result_ != null;
  }
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
   * @return The result.
   */
  @Override
  public Result getResult() {
    return result_ == null ? Result.getDefaultInstance() : result_;
  }
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
   */
  @Override
  public ResultOrBuilder getResultOrBuilder() {
    return getResult();
  }

  private byte memoizedIsInitialized = -1;
  @Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!getUuidBytes().isEmpty()) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, uuid_);
    }
    if (type_ != ERequestType.COMMAND.getNumber()) {
      output.writeEnum(2, type_);
    }
    if (instant_ != 0L) {
      output.writeInt64(3, instant_);
    }
    if (result_ != null) {
      output.writeMessage(4, getResult());
    }
    unknownFields.writeTo(output);
  }

  @Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!getUuidBytes().isEmpty()) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, uuid_);
    }
    if (type_ != ERequestType.COMMAND.getNumber()) {
      size += com.google.protobuf.CodedOutputStream
        .computeEnumSize(2, type_);
    }
    if (instant_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, instant_);
    }
    if (result_ != null) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getResult());
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @Override
  public boolean equals(final Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof ExeculibResponse)) {
      return super.equals(obj);
    }
    ExeculibResponse other = (ExeculibResponse) obj;

    if (!getUuid()
        .equals(other.getUuid())) return false;
    if (type_ != other.type_) return false;
    if (getInstant()
        != other.getInstant()) return false;
    if (hasResult() != other.hasResult()) return false;
    if (hasResult()) {
      if (!getResult()
          .equals(other.getResult())) return false;
    }
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + UUID_FIELD_NUMBER;
    hash = (53 * hash) + getUuid().hashCode();
    hash = (37 * hash) + TYPE_FIELD_NUMBER;
    hash = (53 * hash) + type_;
    hash = (37 * hash) + INSTANT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getInstant());
    if (hasResult()) {
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static ExeculibResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static ExeculibResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static ExeculibResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static ExeculibResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static ExeculibResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static ExeculibResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static ExeculibResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static ExeculibResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static ExeculibResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static ExeculibResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static ExeculibResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static ExeculibResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(ExeculibResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @Override
  protected Builder newBuilderForType(
      BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse)
      ExeculibResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ExeculibResponse.class, Builder.class);
    }

    // Construct using com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib.ExeculibResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
      }
    }
    @Override
    public Builder clear() {
      super.clear();
      uuid_ = "";

      type_ = 0;

      instant_ = 0L;

      if (resultBuilder_ == null) {
        result_ = null;
      } else {
        result_ = null;
        resultBuilder_ = null;
      }
      return this;
    }

    @Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return ExeculibGrpc.internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_ExeculibResponse_descriptor;
    }

    @Override
    public ExeculibResponse getDefaultInstanceForType() {
      return ExeculibResponse.getDefaultInstance();
    }

    @Override
    public ExeculibResponse build() {
      ExeculibResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @Override
    public ExeculibResponse buildPartial() {
      ExeculibResponse result = new ExeculibResponse(this);
      result.uuid_ = uuid_;
      result.type_ = type_;
      result.instant_ = instant_;
      if (resultBuilder_ == null) {
        result.result_ = result_;
      } else {
        result.result_ = resultBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @Override
    public Builder clone() {
      return super.clone();
    }
    @Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.setField(field, value);
    }
    @Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        Object value) {
      return super.addRepeatedField(field, value);
    }
    @Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof ExeculibResponse) {
        return mergeFrom((ExeculibResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(ExeculibResponse other) {
      if (other == ExeculibResponse.getDefaultInstance()) return this;
      if (!other.getUuid().isEmpty()) {
        uuid_ = other.uuid_;
        onChanged();
      }
      if (other.type_ != 0) {
        setTypeValue(other.getTypeValue());
      }
      if (other.getInstant() != 0L) {
        setInstant(other.getInstant());
      }
      if (other.hasResult()) {
        mergeResult(other.getResult());
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @Override
    public final boolean isInitialized() {
      return true;
    }

    @Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      ExeculibResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (ExeculibResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }

    private Object uuid_ = "";
    /**
     * <code>string uuid = 1;</code>
     * @return The uuid.
     */
    public String getUuid() {
      Object ref = uuid_;
      if (!(ref instanceof String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        uuid_ = s;
        return s;
      } else {
        return (String) ref;
      }
    }
    /**
     * <code>string uuid = 1;</code>
     * @return The bytes for uuid.
     */
    public com.google.protobuf.ByteString
        getUuidBytes() {
      Object ref = uuid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        uuid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string uuid = 1;</code>
     * @param value The uuid to set.
     * @return This builder for chaining.
     */
    public Builder setUuid(
        String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      uuid_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string uuid = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUuid() {
      
      uuid_ = getDefaultInstance().getUuid();
      onChanged();
      return this;
    }
    /**
     * <code>string uuid = 1;</code>
     * @param value The bytes for uuid to set.
     * @return This builder for chaining.
     */
    public Builder setUuidBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      uuid_ = value;
      onChanged();
      return this;
    }

    private int type_ = 0;
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @return The enum numeric value on the wire for type.
     */
    @Override public int getTypeValue() {
      return type_;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @param value The enum numeric value on the wire for type to set.
     * @return This builder for chaining.
     */
    public Builder setTypeValue(int value) {
      
      type_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @return The type.
     */
    @Override
    public ERequestType getType() {
      @SuppressWarnings("deprecation")
      ERequestType result = ERequestType.valueOf(type_);
      return result == null ? ERequestType.UNRECOGNIZED : result;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @param value The type to set.
     * @return This builder for chaining.
     */
    public Builder setType(ERequestType value) {
      if (value == null) {
        throw new NullPointerException();
      }
      
      type_ = value.getNumber();
      onChanged();
      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearType() {
      
      type_ = 0;
      onChanged();
      return this;
    }

    private long instant_ ;
    /**
     * <code>int64 instant = 3;</code>
     * @return The instant.
     */
    @Override
    public long getInstant() {
      return instant_;
    }
    /**
     * <code>int64 instant = 3;</code>
     * @param value The instant to set.
     * @return This builder for chaining.
     */
    public Builder setInstant(long value) {
      
      instant_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>int64 instant = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearInstant() {
      
      instant_ = 0L;
      onChanged();
      return this;
    }

    private Result result_;
    private com.google.protobuf.SingleFieldBuilderV3<
        Result, Result.Builder, ResultOrBuilder> resultBuilder_;
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     * @return Whether the result field is set.
     */
    public boolean hasResult() {
      return resultBuilder_ != null || result_ != null;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     * @return The result.
     */
    public Result getResult() {
      if (resultBuilder_ == null) {
        return result_ == null ? Result.getDefaultInstance() : result_;
      } else {
        return resultBuilder_.getMessage();
      }
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     */
    public Builder setResult(Result value) {
      if (resultBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        result_ = value;
        onChanged();
      } else {
        resultBuilder_.setMessage(value);
      }

      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     */
    public Builder setResult(
        Result.Builder builderForValue) {
      if (resultBuilder_ == null) {
        result_ = builderForValue.build();
        onChanged();
      } else {
        resultBuilder_.setMessage(builderForValue.build());
      }

      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     */
    public Builder mergeResult(Result value) {
      if (resultBuilder_ == null) {
        if (result_ != null) {
          result_ =
            Result.newBuilder(result_).mergeFrom(value).buildPartial();
        } else {
          result_ = value;
        }
        onChanged();
      } else {
        resultBuilder_.mergeFrom(value);
      }

      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     */
    public Builder clearResult() {
      if (resultBuilder_ == null) {
        result_ = null;
        onChanged();
      } else {
        result_ = null;
        resultBuilder_ = null;
      }

      return this;
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     */
    public Result.Builder getResultBuilder() {
      
      onChanged();
      return getResultFieldBuilder().getBuilder();
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     */
    public ResultOrBuilder getResultOrBuilder() {
      if (resultBuilder_ != null) {
        return resultBuilder_.getMessageOrBuilder();
      } else {
        return result_ == null ?
            Result.getDefaultInstance() : result_;
      }
    }
    /**
     * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilderV3<
        Result, Result.Builder, ResultOrBuilder>
        getResultFieldBuilder() {
      if (resultBuilder_ == null) {
        resultBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<>(
                getResult(),
                getParentForChildren(),
                isClean());
        result_ = null;
      }
      return resultBuilder_;
    }
    @Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse)
  }

  // @@protoc_insertion_point(class_scope:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse)
  private static final ExeculibResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new ExeculibResponse();
  }

  public static ExeculibResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ExeculibResponse>
      PARSER = new com.google.protobuf.AbstractParser<>() {
      @Override
      public ExeculibResponse parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
          return new ExeculibResponse(input, extensionRegistry);
      }
  };

  public static com.google.protobuf.Parser<ExeculibResponse> parser() {
    return PARSER;
  }

  @Override
  public com.google.protobuf.Parser<ExeculibResponse> getParserForType() {
    return PARSER;
  }

  @Override
  public ExeculibResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

