// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Notification.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.notification;

public final class NotificationGrpc {
    private NotificationGrpc() {
    }

    public static void registerAllExtensions(com.google.protobuf.ExtensionRegistryLite registry) {
    }

    public static void registerAllExtensions(com.google.protobuf.ExtensionRegistry registry) {
        registerAllExtensions((com.google.protobuf.ExtensionRegistryLite) registry);
    }

    static final com.google.protobuf.Descriptors.Descriptor internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationRequest_descriptor;
    static final com.google.protobuf.GeneratedMessageV3.FieldAccessorTable internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationRequest_fieldAccessorTable;
    static final com.google.protobuf.Descriptors.Descriptor internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationResponse_descriptor;
    static final com.google.protobuf.GeneratedMessageV3.FieldAccessorTable internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationResponse_fieldAccessorTable;

    public static com.google.protobuf.Descriptors.FileDescriptor getDescriptor() {
        return descriptor;
    }

    private static com.google.protobuf.Descriptors.FileDescriptor descriptor;

    static {
        String[] descriptorData = {
                "\n\022Notification.proto\022Dcom.bimcoresolutio" + "ns.project.bimsocle.util.bimutilgrpc.grp" + "cgenerated\"4\n\023NotificationRequest\022\014\n\004uui" + "d\030\001 \001(\t\022\017\n\007content\030\002 \001(\t\"5\n\024Notification" + "Response\022\014\n\004uuid\030\001 \001(\t\022\017\n\007content\030\002 \001(\t2" + "\346\001\n\023NotificationService\022\316\001\n\023streamNotifi" + "cations\022Y.com.bimcoresolutions.project.b" + "imsocle.util.bimutilgrpc.grpcgenerated.N" + "otificationRequest\032Z.com.bimcoresolution" + "s.project.bimsocle.util.bimutilgrpc.grpc" + "generated.NotificationResponse0\001Bi\nQcom." + "bimcoresolutions.project.bimsocle.util.b" + "imutilgrpc.grpcgenerated.notificationB\020N" + "otificationGrpcH\001P\001b\006proto3" };
        descriptor = com.google.protobuf.Descriptors.FileDescriptor.internalBuildGeneratedFileFrom(descriptorData, new com.google.protobuf.Descriptors.FileDescriptor[] {});
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationRequest_descriptor = getDescriptor().getMessageTypes().get(0);
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationRequest_fieldAccessorTable = new com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationRequest_descriptor, new String[] { "Uuid", "Content", });
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationResponse_descriptor = getDescriptor().getMessageTypes().get(1);
        internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationResponse_fieldAccessorTable = new com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
                internal_static_com_bimcoresolutions_project_bimsocle_util_bimutilgrpc_grpcgenerated_NotificationResponse_descriptor, new String[] { "Uuid", "Content", });
    }

    // @@protoc_insertion_point(outer_class_scope)
}
