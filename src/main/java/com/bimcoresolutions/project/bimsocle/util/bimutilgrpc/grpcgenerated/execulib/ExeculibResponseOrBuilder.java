// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Execulib.proto

package com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.execulib;

public interface ExeculibResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string uuid = 1;</code>
   * @return The uuid.
   */
  String getUuid();
  /**
   * <code>string uuid = 1;</code>
   * @return The bytes for uuid.
   */
  com.google.protobuf.ByteString
      getUuidBytes();

  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The enum numeric value on the wire for type.
   */
  int getTypeValue();
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ERequestType type = 2;</code>
   * @return The type.
   */
  ERequestType getType();

  /**
   * <code>int64 instant = 3;</code>
   * @return The instant.
   */
  long getInstant();

  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
   * @return Whether the result field is set.
   */
  boolean hasResult();
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
   * @return The result.
   */
  ExeculibResponse.Result getResult();
  /**
   * <code>.com.bimcoresolutions.project.bimsocle.util.bimutilgrpc.grpcgenerated.ExeculibResponse.Result result = 4;</code>
   */
  ExeculibResponse.ResultOrBuilder getResultOrBuilder();
}
