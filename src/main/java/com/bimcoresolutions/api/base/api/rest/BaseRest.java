package com.bimcoresolutions.api.base.api.rest;

import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import org.springframework.data.domain.Sort;
import org.springframework.data.util.StreamUtils;

import java.util.List;

public abstract class BaseRest {

    protected static List<String> checkRelations(List<String> submittedRelations, final List<String> validRelations) {
        List<String> ret;
        if (submittedRelations.contains("*")) {
            ret = List.copyOf(validRelations);
        } else if (submittedRelations.contains("!")) {
            ret = List.of();
        } else {
            List<String> unknownRelations = submittedRelations.stream()
                    .filter(rel -> !validRelations.contains(rel))
                    .toList();
            if (!unknownRelations.isEmpty()) {
                throw new WrongArgumentValueException("Wrong parameter given for 'relations' : " + submittedRelations + ". Acceptable values : " + validRelations + "[*(all)][!(none)]");
            }
            ret = List.copyOf(submittedRelations);
        }
        return ret;
    }

    protected static List<Sort.Order> checkOrderBy(List<String> submittedOrderBys, final List<String> validOrderBys, final List<Sort.Direction> orders) {
        if (submittedOrderBys.size() != orders.size()) {
            throw new WrongArgumentValueException("Parameters 'orderby'(" + submittedOrderBys.size() + ") and 'order'(" + orders.size() + ") must have same size");
        }
        if (submittedOrderBys.isEmpty()) {
            return List.of();
        }
        List<String> unValids = submittedOrderBys.stream()
                .filter(orderby -> !validOrderBys.contains(orderby))
                .toList();

        if (!unValids.isEmpty()) {
            throw new WrongArgumentValueException("Wrong parameter given for 'orderby' : " + submittedOrderBys + ". Acceptable values : " + validOrderBys);
        }
        return StreamUtils.zip(submittedOrderBys.stream(), orders.stream(), (orderby, order) -> new Sort.Order(order, orderby)).toList();
    }

}
