package com.bimcoresolutions.api.base.api.dao;

import com.bimcoresolutions.api.base.configuration.heritage.HeritageHandler;
import com.bimcoresolutions.util.base.filter.*;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelElement;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import tech.jhipster.service.Criteria;

import javax.sql.DataSource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_ID_BIMCORE;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PREFIX_ELEMTABLE;
import static com.bimcoresolutions.util.base.sql.QueryFormer.betweenSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatAndSimpleQuote;
import static com.bimcoresolutions.util.base.sql.QueryFormer.formatValueToSQL;
import static java.lang.Boolean.TRUE;

public abstract class CriteriaRepository<ENTITY extends Model, CRITERIA extends Criteria> extends BaseDao<ENTITY> {

    protected CriteriaRepository(ObjectMapper om, HeritageHandler heritageHandler, DataSource dataSource, QueryFormer qf, FunctionalModelService functionalModelService) {
        super(om, heritageHandler, dataSource, qf, functionalModelService);
    }

    private String getOrder(Pageable page) {
        if (page.getSort() == Sort.unsorted()) {
            return null;
        }
        return page.getSort()
                .stream()
                .map(it -> it.getDirection().toString())
                .findFirst().orElseGet(null);
    }

    private List<String> getOrderBy(Pageable page) {
        return page.getSort()
                .stream()
                .map(Sort.Order::getProperty)
                .toList();
    }

    private String getWhereClauses(CRITERIA criterias) {
        List<String> whereClauses = whereClauses(criterias);
        return String.join(" and \n", whereClauses);
    }

    protected abstract List<String> whereClauses(CRITERIA criterias);

    protected static List<String> buildGeometrySpecification(GeometryFilter filter, String field) {
        List<String> clauses = new ArrayList<>();
        if (filter.getEquals() != null) {
            clauses.add("ST_Equals(" + field + ", ST_GeomFromText('" + filter.equalsG().toText() + "', " + filter.equalsG().getSRID() + "))");
        }
        if (filter.getNotEquals() != null) {
            clauses.add("not ST_Equals(" + field + ", ST_GeomFromText('" + filter.notEqualsG().toText() + "', " + filter.notEqualsG().getSRID() + "))");
        }
        if (filter.getBbox() != null) {
            String template = "ST_Intersects(${field}, ST_Envelope(ST_GeomFromText('LINESTRING(${a} ${b}, ${c} ${d})', 4326)))";

            // Build StringSubstitutor
            Map<String, Object> valuesMap = Map.of("field", field, "a", filter.getBbox().get(0), "b", filter.getBbox().get(1), "c", filter.getBbox().get(2), "d",
                    filter.getBbox().get(3));
            StringSubstitutor sub = new StringSubstitutor(valuesMap);

            // Replace
            String resolvedString = sub.replace(template);
            clauses.add(resolvedString);
        }
        if (filter.getIntersects() != null) {
            clauses.add("ST_Intersects(" + field + ", ST_GeomFromText('" + filter.intersectsG().toText() + "', " + filter.intersectsG().getSRID() + "))");
        }
        if (filter.getContains() != null) {
            clauses.add("ST_Contains(" + field + ", ST_GeomFromText('" + filter.containsG().toText() + "', " + filter.containsG().getSRID() + "))");
        }
        if (filter.getCovers() != null) {
            clauses.add("ST_Covers(" + field + ", ST_GeomFromText('" + filter.coversG().toText() + "', " + filter.coversG().getSRID() + "))");
        }
        if (filter.getWithin() != null) {
            clauses.add("ST_Within(" + field + ", ST_GeomFromText('" + filter.withinG().toText() + "', " + filter.withinG().getSRID() + "))");
        }
        return clauses;
    }

    protected static Collection<String> buildJsonSpecification(JsonFilter filter, String field) {
        List<String> clauses = new ArrayList<>();

        if (filter.getPath() != null) {
            String path = field + "#>> '{" + filter.getPath() + "}'";

            if (filter.getEquals() != null) {
                clauses.add(path + " = " + toSqlValue(filter.getEquals().toString()));
            }
            if (filter.getIn() != null) {
                clauses.add(path + " in (" + filter.getIn()
                        .stream()
                        .map(v -> toSqlValue(v.toString()))
                        .collect(Collectors.joining(",")) + ")");
            }
            if (filter.getNotIn() != null) {
                clauses.add(path + " not in (" + filter.getNotIn()
                        .stream()
                        .map(v -> toSqlValue(v.toString()))
                        .collect(Collectors.joining(",")) + ")");
            }
            if (filter.getNotEquals() != null) {
                clauses.add(path + " != " + toSqlValue(filter.getNotEquals().toString()));
            }

            if (clauses.isEmpty()) {
                clauses.add(field + "?" + toSqlValue(filter.getPath()));
            }
        }
        return clauses;
    }

    protected static Collection<String> buildArraySpecification(JsonArrayFilter filter, String field) {
        List<String> clauses = new ArrayList<>();

        if (filter.getSpecified() != null) {
            if (filter.getSpecified() == TRUE) {
                clauses.add(field + " is not null");
            } else {
                clauses.add(field + " is null");
            }
        }

        if (filter.getEquals() != null) {
            clauses.add(field + " = " + toSqlValue(filter.getEquals().toString()));
        }
        if (filter.getNotEquals() != null) {
            clauses.add(field + " != " + toSqlValue(filter.getNotEquals().toString()));
        }
        if (filter.getIn() != null) {
            clauses.add((String) filter.getIn()
                    .stream()
                    .map(v -> field + "@>" + toSqlValue(v.toString()))
                    .collect(Collectors.joining(" AND ")));
        }
        if (filter.getNotIn() != null) {
            clauses.add((String) filter.getNotIn()
                    .stream()
                    .map(v -> "NOT " + field + "@>" + toSqlValue(v.toString()))
                    .collect(Collectors.joining(" AND ")));
        }
        return clauses;
    }

    protected static List<String> buildRangeSpecification(RangeFilter filter, String field) {
        List<String> clauses = new ArrayList<>();
        if (filter.getEquals() != null) {
            clauses.add(field + " = " + formatAndSimpleQuote(filter.getEquals().toString()));
        }
        if (filter.getIn() != null) {
            clauses.add(field + " in (" + filter.getIn()
                    .stream()
                    .map(i -> formatAndSimpleQuote(i.toString()))
                    .collect(Collectors.joining(",")) + ")");
        }
        if (filter.getSpecified() != null) {
            clauses.add(field + " != null");
        }

        if (filter.getNotEquals() != null) {
            clauses.add(field + " != " + formatAndSimpleQuote(filter.getNotEquals().toString()));
        }

        if (filter.getNotIn() != null) {
            clauses.add(field + "not in (" + filter.getNotIn()
                    .stream()
                    .map(i -> formatAndSimpleQuote(i.toString()))
                    .collect(Collectors.joining(",")) + ")");
        }

        if (filter.getGreaterThan() != null) {
            clauses.add(field + " > " + formatAndSimpleQuote(filter.getGreaterThan().toString()));
        }

        if (filter.getGreaterThanOrEqual() != null) {
            clauses.add(field + " >= " + formatAndSimpleQuote(filter.getGreaterThanOrEqual().toString()));
        }

        if (filter.getLessThan() != null) {
            clauses.add(field + " < " + formatAndSimpleQuote(filter.getLessThan().toString()));
        }

        if (filter.getLessThanOrEqual() != null) {
            clauses.add(field + " <= " + formatAndSimpleQuote(filter.getLessThanOrEqual().toString()));
        }
        return clauses;
    }

    protected static List<String> buildSimpleSpecification(SimpleFilter<?> filter, String field) {
        List<String> clauses = new ArrayList<>();
        if (filter.getEquals() != null) {
            clauses.add(field + " = " + filter.getEquals());
        }
        if (filter.getIn() != null) {
            clauses.add(field + " in (" + filter.getIn()
                    .stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(",")) + ")");
        }
        if (filter.getSpecified() != null) {
            clauses.add(field + " != null");
        }
        if (filter.getNotEquals() != null) {
            clauses.add(field + " != " + filter.getNotEquals());
        }
        if (filter.getNotIn() != null) {
            clauses.add(field + " not in (" + filter.getNotIn()
                    .stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(",")) + ")");
        }
        return clauses;
    }

    protected static List<String> buildStringSpecification(StringFilter filter, String field) {
        List<String> clauses = new ArrayList<>();
        if (filter.getEquals() != null) {
            clauses.add(field + " = " + toSqlValue(filter.getEquals()));
        }
        if (filter.getEqualsIgnoreCase() != null) {
            clauses.add(field + " ilike " + toSqlValue(filter.getEqualsIgnoreCase()));
        }
        if (filter.getNotEquals() != null) {
            clauses.add(field + " != " + toSqlValue(filter.getNotEquals()));
        }
        if (filter.getNotEqualsIgnoreCase() != null) {
            clauses.add(field + " not ilike " + toSqlValue(filter.getNotEqualsIgnoreCase()));
        }
        if (filter.getIn() != null) {
            clauses.add(field + " in (" + filter.getIn()
                    .stream()
                    .map(CriteriaRepository::toSqlValue)
                    .collect(Collectors.joining(",")) + ")");
        }
        if (filter.getNotIn() != null) {
            clauses.add(field + " not in (" + filter.getNotIn()
                    .stream()
                    .map(CriteriaRepository::toSqlValue)
                    .collect(Collectors.joining(",")) + ")");
        }
        if (filter.getContains() != null) {
            clauses.add(field + " like " + toSqlValuelike(filter.getContains()));
        }
        if (filter.getContainsIgnoreCase() != null) {
            clauses.add(field + " ilike " + toSqlValuelike(filter.getContainsIgnoreCase()));
        }
        if (filter.getDoesNotContain() != null) {
            clauses.add(field + " not like " + toSqlValuelike(filter.getDoesNotContain()));
        }
        if (filter.getDoesNotContainIgnoreCase() != null) {
            clauses.add(field + " not ilike " + toSqlValuelike(filter.getDoesNotContainIgnoreCase()));
        }
        return clauses;
    }

    protected static String toSqlValue(String i) {
        return betweenSimpleQuote(formatValueToSQL(i));
    }

    protected static String toSqlValuelike(String i) {
        return betweenSimpleQuote("%" + formatValueToSQL(i) + "%");
    }

    public List<ENTITY> searchListCriteria(CRITERIA criterias, List<String> relations, Pageable page) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT root.* ${relationArray}
                FROM "${schema}"."${table}" root
                    ${jointure}
                WHERE ${where}
                GROUP BY root."${_id_bimcore}"
                ${pagination};""";
        String relationArray = getRelationArraysObject(bce, relations);
        relationArray = !relationArray.isBlank() ? ", " + relationArray : relationArray;
        String jointures = getJointures(bce);
        String where = getWhereClauses(criterias);

        String pagination = getAllPagination(getOrderBy(page), getOrder(page), (long) page.getPageSize(), page.getOffset());
        Map<String, Object> valuesMap = Map.of("relationArray", relationArray, "table", DB_REALTIME_PREFIX_ELEMTABLE + bce.getName(), "jointure", jointures, "where", where,
                "_id_bimcore", DB_REALTIME_ID_BIMCORE, "schema", qf.getSchema(), "pagination", pagination);
        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return execQuery(getClassT(), resolvedString);
    }

    public Long searchCountCriteria(CRITERIA criteria) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT count(*)
                FROM "${schema}"."${table}" root
                    ${jointure}
                WHERE ${where};""";
        String relationArray = getRelationArraysObject(bce);
        relationArray = !relationArray.isBlank() ? ", " + relationArray : relationArray;
        String jointures = getJointures(bce);
        String where = getWhereClauses(criteria);
        Map<String, Object> valuesMap = Map.of("relationArray", relationArray, "table", "elem_" + bce.getName(), "jointure", jointures, "where", where, "schema", qf.getSchema());
        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        String resolvedString = sub.replace(template);
        return (long) execCount(resolvedString);
    }

}
