package com.bimcoresolutions.api.base.api.service;

import com.bimcoresolutions.api.base.api.dao.BaseDao;
import com.bimcoresolutions.api.base.api.model.GeometryFunction;
import com.bimcoresolutions.api.base.util.exceptions.NoEntityFoundException;
import com.bimcoresolutions.api.base.util.exceptions.WrongArgumentValueException;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.Bbox;
import com.bimcoresolutions.util.base.rest.EnveloppeGetter;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.exceptions.ModelException;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jooq.tools.StringUtils;
import org.locationtech.jts.geom.Geometry;

import java.time.Instant;
import java.util.List;
import java.util.Map;

import static com.google.common.collect.Lists.partition;
import static org.springframework.util.CollectionUtils.isEmpty;

public abstract class BaseService<T extends Model> {
    static final Logger LOGGER = LogManager.getLogger();
    static final int PACK = 1000;
    private long lastDateModified = Instant.now().toEpochMilli();

    protected final BimCoreModel bimCoreModel;

    protected final BaseDao<T> dao;

    protected BaseService(BaseDao<T> dao, FunctionalModelService functionalModelService) {
        this.bimCoreModel = functionalModelService.getModel();
        this.dao = dao;
    }

    public void setLastDateModified() {
        this.lastDateModified = Instant.now().toEpochMilli();
    }

    public long getLastDateModified() {
        return this.lastDateModified;
    }

    public String create(List<T> list) {
        int total = partition(list, PACK).stream().mapToInt(dao::insert).sum();
        return "{\"rows\":" + total + "}";
    }

    public String updateElt(String id, T refCamera) {
        refCamera.set_id_bimcore(id);
        dao.update(refCamera);
        return "{\"success\":1}";
    }

    public String updateMultiple(List<T> entity) {
        Integer success = 0;
        Integer fail = 0;
        for (T refCamera : entity) {
            try {
                dao.update(refCamera);
                success++;
            } catch (Exception ex) {
                fail++;
                LOGGER.error(ex);
            }
        }
        return "{\"success\":" + success + ",\"fail\":" + fail + "}";
    }

    public String count(Map<String, Object> whereClause, List<String> isNull, List<String> isNotNull) {
        Long resultat = dao.count(whereClause, isNull, isNotNull);
        if (resultat == null) {
            throw new NoEntityFoundException();
        }
        return "{\"rows\":" + resultat + "}";
    }

    public EnveloppeGetter<T> getAll(Map<String, Object> whereClause, List<String> pattern, Long limit, Long offset, List<String> orderby, String order, List<String> isNull,
                                     List<String> isNotNull) {
        EnveloppeGetter<T> toRet = new EnveloppeGetter<>();
        List<T> resultat = dao.getAll(whereClause, pattern, limit, offset, orderby, order, isNull, isNotNull);
        if (isEmpty(resultat)) {
            throw new NoEntityFoundException();
        }

        if (offset == null) {
            offset = 0L;
        }

        toRet.setItems(resultat);
        Long total = dao.count(whereClause, isNull, isNotNull);
        toRet.getPagination().setTotal(total);
        toRet.getPagination().setDebut(offset);

        if (limit != null && total > limit) {
            toRet.getPagination().setFin(offset + limit);
        } else {
            toRet.getPagination().setFin(offset + resultat.size());
        }
        return toRet;
    }

    public EnveloppeGetter<T> findBy(String field, List<String> values, Long limit, Long offset, List<String> orderby, String order) {
        checkIfSearchKeyIsValid(List.of(field));
        List<T> resultat = dao.findBy(field, values, limit, offset, orderby, order);
        if (isEmpty(resultat)) {
            throw new NoEntityFoundException();
        }

        Long total = dao.findByCount(field, values);
        return buildEnveloppeGetter(offset, resultat, total);
    }

    public EnveloppeGetter<T> search(String value, List<String> champs, List<String> pattern, Long limit, Long offset, List<String> orderby, String order) {
        checkIfSearchKeyIsValid(champs);

        if (isEmpty(champs)) {
            throw new WrongArgumentValueException("Parameter 'champs' cannot be empty as they are the attributes which 'value' is evaluated against");
        }

        List<T> resultat = dao.search(value, champs, pattern, limit, offset, orderby, order);
        if (isEmpty(resultat)) {
            throw new NoEntityFoundException();
        }

        Long total = dao.searchCount(value, champs);
        return buildEnveloppeGetter(offset, resultat, total);
    }

    public EnveloppeGetter<T> findBy(String field, List<String> values, List<String> pattern, Long limit, Long offset, List<String> orderby, String order) {
        checkIfSearchKeyIsValid(List.of(field));

        if (StringUtils.isBlank(field)) {
            throw new WrongArgumentValueException("Parameter 'field' cannot be blank as it is the attribute to search on");
        }
        if (isEmpty(values)) {
            throw new WrongArgumentValueException("Parameter 'values' cannot be empty as they are the values searched in 'field'");
        }

        List<T> resultat = dao.findBy(field, values, limit, offset, orderby, order);
        if (isEmpty(resultat)) {
            throw new NoEntityFoundException();
        }

        Long total = dao.searchCount(field, values);
        return buildEnveloppeGetter(offset, resultat, total);
    }

    protected abstract void checkIfSearchKeyIsValid(List<String> champs);

    public T getId(String id) {
        T resultat = dao.getId(id);
        if (resultat == null) {
            throw new NoEntityFoundException();
        }
        return resultat;
    }

    public EnveloppeGetter<T> getIds(List<String> ids) {
        EnveloppeGetter<T> toRet = new EnveloppeGetter<>();
        List<T> resultat = dao.getIds(ids);
        if (isEmpty(resultat)) {
            throw new NoEntityFoundException();
        }
        toRet.getItems().addAll(resultat);
        return toRet;
    }

    public void deleteById(String id) {
        dao.delete(id);
    }

    public List<Bbox<T>> bbox(List<Float> bbox, List<String> relations) {
        List<T> resultat = dao.bbox(bbox, relations);
        if (isEmpty(resultat)) {
            throw new NoEntityFoundException();
        }
        return resultat.stream().map(this::toBbox).toList();
    }

    public Bbox<T> toBbox(T truc) {
        throw new ModelException("This modelClass hasn't any geometric column");
    }

    public List<T> geometry(GeometryFunction function, Geometry geometry) {
        List<T> resultat = dao.geometry(function, geometry);
        if (isEmpty(resultat)) {
            throw new NoEntityFoundException();
        }
        return resultat;
    }

    protected static <U> EnveloppeGetter<U> buildEnveloppeGetter(Long offset, List<U> resultat, long total) {
        EnveloppeGetter<U> toRet = new EnveloppeGetter<>();
        toRet.getPagination().setTotal(total);
        toRet.setItems(resultat);
        toRet.getPagination().setDebut(offset);
        toRet.getPagination().setFin(offset + resultat.size());
        return toRet;
    }

}
