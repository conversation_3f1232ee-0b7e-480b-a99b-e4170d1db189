package com.bimcoresolutions.api.base.api.dao;

import com.bimcoresolutions.api.base.configuration.heritage.HeritageHandler;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.filter.BimCriterias;
import com.bimcoresolutions.util.base.filter.SpecificationBuilder;
import com.bimcoresolutions.util.base.model.HistoryModel;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.sql.DatabaseTypeException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelElement;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelRelation;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import net.postgis.jdbc.PGgeometry;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgresql.jdbc.PgArray;
import org.postgresql.util.PGobject;
import org.reflections.util.ReflectionUtilsPredicates;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static com.bimcoresolutions.api.base.spring.conf.SpringBeanConfiguration.DATE_PATTERN;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.rest.DaoUtils.buildLimitOffset;
import static com.bimcoresolutions.util.base.rest.DaoUtils.buildOrders;
import static com.bimcoresolutions.util.base.sql.QueryFormer.*;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static java.util.stream.Collectors.joining;
import static org.reflections.ReflectionUtils.getAllFields;

public abstract class BaseGenDao<R extends Model, H extends HistoryModel> {

    private static final Logger LOGGER = LogManager.getLogger();

    protected final ObjectMapper om;
    protected final HeritageHandler heritageHandler;
    protected final DataSource dsRt;
    protected final QueryFormer qfRt;
    protected final SpecificationBuilder specRt;
    protected final DataSource dsHisto;
    protected final QueryFormer qfHisto;
    protected final SpecificationBuilder specHisto;
    protected final BimCoreModel model;
    protected final WKTReader wktReader;

    protected final TypeReference<Map<String, Object>> typeReference = new TypeReference<>() {
    };
    protected final Predicate<Field> notJsonIgnore = c -> !c.isAnnotationPresent(JsonIgnore.class);

    protected BaseGenDao(ObjectMapper om, HeritageHandler heritageHandler,
                         @Qualifier("dsRealtime") DataSource dsRt, @Qualifier("specRealtime") SpecificationBuilder specRt,
                         @Qualifier("dsHisto") DataSource dsHisto, @Qualifier("specHisto") SpecificationBuilder specHisto,
                         FunctionalModelService functionalModelService) {
        this.om = om;
        this.heritageHandler = heritageHandler;
        this.dsRt = dsRt;
        this.qfRt = specRt.getQueryFormer();
        this.specRt = specRt;
        this.dsHisto = dsHisto;
        this.qfHisto = specHisto.getQueryFormer();
        this.specHisto = specHisto;
        this.model = functionalModelService.getModel();
        this.wktReader = new WKTReader();
    }

    public abstract Pair<String, Boolean> colToRelTableAndWay(String col);

    private BimCoreModelElement getElement(Class<R> clazz) {
        return model.getElementsBimCore().get(clazz.getSimpleName());
    }

    protected String getEntityName() {
        return getClassR().getSimpleName();
    }

    protected Class<R> getClassR() {
        return (Class<R>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    protected Class<H> getClassH() {
        return (Class<H>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }

    private String fieldToGetter(Field field) {
        String fieldName = field.getName();
        return "get" + fieldName.substring(0, 1).toUpperCase(Locale.ROOT) + fieldName.substring(1);
    }

    @SneakyThrows
    private <T> List<T> resultSetToEntities(ResultSet rs, Class<T> clazz) {
        ResultSetMetaData rsmd = rs.getMetaData();
        Set<String> columnLabels = new HashSet<>();

        for (int i = 1; i < rsmd.getColumnCount() + 1; i++) {
            columnLabels.add(rsmd.getColumnLabel(i));
        }

        List<T> result = new ArrayList<>();
        while (rs.next()) {
            result.add(om.convertValue(retrieveLine(rs, columnLabels), clazz));
        }

        return result;
    }

    @SneakyThrows
    private Map<String, Object> retrieveLine(ResultSet rs, Set<String> columnLabels) {
        Map<String, Object> line = new LinkedHashMap<>();
        for (String label : columnLabels) {
            line.put(label, convert(rs.getObject(label)));
        }
        return line;
    }

    @SneakyThrows
    protected Object convert(Object object) {
        if (object instanceof PGgeometry pGgeometry) {
            StringBuffer sb = new StringBuffer();
            pGgeometry.getGeometry().outerWKT(sb);
            return new WKTWriter().write(new WKTReader().read(sb.toString()));
        } else if (object instanceof Timestamp t) {
            return new SimpleDateFormat(DATE_PATTERN).format(t);
        } else if (object instanceof PGobject pgObject && pgObject.getType().equals("jsonb")) {
            return om.readValue(pgObject.getValue(), JsonNode.class);
        } else if (object instanceof PgArray pgArray) {
            return pgArray.getArray();
        } else {
            return object;
        }
    }

    @SneakyThrows
    public List<R> execQueryRt(String query, Class<R> clazz) {
        LOGGER.debug(query);
        try (Connection con = dsRt.getConnection(); Statement stmtQuery = con.createStatement(); ResultSet rs = stmtQuery.executeQuery(query)) {
            return resultSetToEntities(rs, clazz);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    @SneakyThrows
    public List<H> execQueryHisto(String query, Class<H> clazz) {
        LOGGER.debug(query);
        try (Connection con = dsHisto.getConnection(); Statement stmtQuery = con.createStatement(); ResultSet rs = stmtQuery.executeQuery(query)) {
            return resultSetToEntities(rs, clazz);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    public int executeUpdate(String query) {
        LOGGER.debug(query);
        try (Connection con = dsRt.getConnection(); Statement stmtQuery = con.createStatement()) {
            return stmtQuery.executeUpdate(query);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    public int execCountRt(String query) {
        LOGGER.debug(query);
        try (Connection con = dsRt.getConnection(); Statement stmtQuery = con.createStatement(); ResultSet rs = stmtQuery.executeQuery(query)) {
            rs.next();
            return rs.getInt(1);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    public int execCountHisto(String query) {
        LOGGER.debug(query);
        try (Connection con = dsHisto.getConnection(); Statement stmtQuery = con.createStatement(); ResultSet rs = stmtQuery.executeQuery(query)) {
            rs.next();
            return rs.getInt(1);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    @SneakyThrows
    public boolean execute(String sql) {
        try (Connection con = dsRt.getConnection(); Statement stmtQuery = con.createStatement()) {
            return stmtQuery.execute(sql);
        }
    }

    public <T extends Model> Pair<List<Pair<String, String>>, Map<String, List<String>>> verifyColumns(Class<T> clazz, Collection<String> att, Collection<String> rel,
                                                                                                       Map<String, Object> colValues) {
        List<Pair<String, String>> attributes = new ArrayList<>();
        Map<String, List<String>> relations = new HashMap<>();

        for (Map.Entry<String, Object> e : colValues.entrySet()) {
            String column = e.getKey();
            Object value = e.getValue();
            Optional<Field> optionalField = getAllFields(clazz, ReflectionUtilsPredicates.withName(column)).stream().findFirst();
            if (!optionalField.isPresent()) {
                LOGGER.warn("Unknown field for class {} : {}", clazz, column);
            } else {
                Field field = optionalField.get();
                if (att.contains(field.getName())) {
                    attributes.add(Pair.of(column, convertAttributeValueToSQL(field, value)));
                } else if (rel.contains(field.getName())) {
                    relations.put(column, (List<String>) value);
                }
            }
        }
        return Pair.of(attributes, relations);
    }

    @SneakyThrows
    public String convertAttributeValueToSQL(Field field, Object value) {
        if (value == null) { // Si pas de valeurs à insérer => NULL
            return betweenSimpleQuote("NULL");
        } else if (field.getType().isAssignableFrom(Boolean.class)) { // fields Boolean
            String booleanValue = String.valueOf(value);
            String booleanString;
            if (booleanValue.equalsIgnoreCase("true") || booleanValue.equalsIgnoreCase("1") || booleanValue.equalsIgnoreCase("t")) {
                booleanString = "1";
            } else if (booleanValue.equalsIgnoreCase("false") || booleanValue.equalsIgnoreCase("0") || booleanValue.equalsIgnoreCase("f")) {
                booleanString = "0";
            } else {
                throw new TechnicalException("Fail to cast boolean value :" + booleanValue);
            }
            return betweenSimpleQuote(booleanString);
        } else if (field.getType().isAssignableFrom(Integer.class) || field.getType().isAssignableFrom(Float.class) || field.getType()
                .isAssignableFrom(Long.class) || field.getType().isAssignableFrom(Double.class)) { // fields numériques
            return betweenSimpleQuote(String.valueOf(value));
        } else if (field.getType().isAssignableFrom(Date.class)) { // fields date
            String dateString;
            // dans le cas insert, c'est un objet Date, dans le cas update, c'est une String
            if (value.getClass().isAssignableFrom(Date.class)) {
                dateString = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format((Date) value);
            } else {
                dateString = value.toString();
            }
            return betweenSimpleQuote(String.valueOf(dateString));
        } else if (field.getType().isAssignableFrom(Geometry.class)) { // fields date
            return betweenSimpleQuote("SRID=4326;" + value);
        } else if (value instanceof Map map) {
            return betweenSimpleQuote(om.writeValueAsString(map));
        } else { // fields String
            return betweenSimpleQuote(formatValueToSQL(value.toString()));
        }
    }

    public int insert(List<R> elements) {
        StringBuilder sql = new StringBuilder();
        Predicate<Field> ofListType = c -> (c.getType().isAssignableFrom(List.class));
        Predicate<Field> notOfListType = c -> !(c.getType().isAssignableFrom(List.class));
        try {
            for (R obj : elements) {
                // Attributes
                sql.append("INSERT INTO ").append(qfRt.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName())).append(" (");
                Set<Field> attributeFields = getAllFields(getClassR(), notJsonIgnore, notOfListType);
                StringBuilder cols = new StringBuilder();
                StringBuilder values = new StringBuilder();

                for (Field field : attributeFields) {
                    String fieldName = field.getName();
                    if (fieldName.equals(DB_REALTIME_ID_BIMCORE)) {
                        cols.append(qfRt.addIdentifier(fieldName)).append(",");
                    } else {
                        cols.append(qfRt.addIdentifier(fieldName)).append(",");
                    }
                    values.append(convertAttributeValueToSQL(field, getClassR().getMethod(fieldToGetter(field)).invoke(obj))).append(",");
                }
                cols.append(qfRt.addIdentifier(DB_REALTIME_TYPEOF));
                values.append(betweenSimpleQuote(formatValueToSQL(heritageHandler.getTypeOfRealtime().get(getEntityName()))));

                sql.append(cols).append(") VALUES (").append(values).append(");");

                // Relations
                Set<Field> relationFields = getAllFields(getClassR(), notJsonIgnore, ofListType);

                for (Field field : relationFields) {
                    List<String> idDestinations = (List<String>) getClassR().getMethod(fieldToGetter(field)).invoke(obj);
                    if (idDestinations != null && !idDestinations.isEmpty()) {
                        Pair<String, Boolean> tableNameAndWay = colToRelTableAndWay(field.getName());
                        String originCol;
                        String destCol;
                        if (Boolean.TRUE.equals(tableNameAndWay.getRight())) {
                            originCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                            destCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                        } else {
                            originCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                            destCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                        }

                        sql.append("INSERT INTO ").append(qfRt.addSchema(DB_REALTIME_PREFIX_RELTABLE + tableNameAndWay.getLeft())).append("(");
                        sql.append(qfRt.addIdentifier(originCol)).append(",").append(qfRt.addIdentifier(destCol)).append(") VALUES ");
                        for (String idDest : idDestinations) {
                            sql.append("(").append(betweenSimpleQuote(formatValueToSQL(obj.get_id_bimcore()))).append(",");
                            sql.append(betweenSimpleQuote(formatValueToSQL(idDest))).append("),");
                        }
                        sql.delete(sql.length() - 1, sql.length()).append(";");
                    }
                }
                executeUpdate(sql.toString());
            }
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            throw new TechnicalException(e);
        }
        return elements.size();
    }

    @SneakyThrows
    public int update(R r) {
        Map<String, Object> colValue = om.convertValue(r, typeReference);
        StringBuilder sql = new StringBuilder();
        Pair<List<Pair<String, String>>, Map<String, List<String>>> verified = verifyColumns(getClassR(), R.ATTRIBUTES, List.of(), colValue);
        List<Pair<String, String>> attributes = verified.getLeft();
        Map<String, List<String>> relations = verified.getRight();
        String idOrigin = colValue.get(DB_REALTIME_ID_BIMCORE).toString();

        sql.append("BEGIN;");
        // Attributes
        sql.append("UPDATE ").append(qfRt.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName())).append(" SET ");
        attributes.forEach((Pair<String, String> att) -> {
            sql.append(qfRt.addIdentifier(att.getLeft())).append(" = ").append(att.getRight()).append(",");
        });
        sql.delete(sql.length() - 1, sql.length());
        sql.append(" WHERE ").append(qfRt.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = ");
        sql.append(betweenSimpleQuote(formatValueToSQL(colValue.get(DB_REALTIME_ID_BIMCORE).toString()))).append(";");

        // Relations
        relations.entrySet().forEach((Map.Entry<String, List<String>> rel) -> {
            String col = rel.getKey();
            Pair<String, Boolean> tableNameAndWay = colToRelTableAndWay(col);
            String originCol;
            String destCol;
            if (Boolean.TRUE.equals(tableNameAndWay.getRight())) {
                originCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                destCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
            } else {
                originCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                destCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
            }
            List<String> idDests = rel.getValue();

            sql.append("DELETE FROM ").append(qfRt.addSchema(DB_REALTIME_PREFIX_RELTABLE + tableNameAndWay.getLeft())).append(" WHERE ");
            sql.append(qfRt.addIdentifier(originCol)).append(" = ").append(betweenSimpleQuote(formatValueToSQL(idOrigin)));
            sql.append(" AND ").append(qfRt.addIdentifier(destCol)).append(" NOT IN (");
            idDests.forEach((String dest) -> sql.append(betweenSimpleQuote(formatValueToSQL(dest))).append(","));
            sql.delete(sql.length() - 1, sql.length()).append(");");

            sql.append("INSERT INTO ").append(qfRt.addSchema(DB_REALTIME_PREFIX_RELTABLE + tableNameAndWay.getLeft())).append("(");
            sql.append(qfRt.addIdentifier(originCol)).append(",").append(qfRt.addIdentifier(destCol)).append(") VALUES ");
            idDests.forEach((String idDest) -> {
                sql.append("(").append(betweenSimpleQuote(formatValueToSQL(idOrigin))).append(",");
                sql.append(betweenSimpleQuote(formatValueToSQL(idDest))).append("),");
            });
            sql.delete(sql.length() - 1, sql.length());
            sql.append(" ON CONFLICT (").append(qfRt.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN)).append(",");
            sql.append(qfRt.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(") DO NOTHING").append(";");
        });
        sql.append("COMMIT;");

        return executeUpdate(sql.toString());
    }

    public void delete(String id) {
        StringBuilder sql = new StringBuilder("BEGIN;");
        sql.append("DELETE FROM ").append(qfRt.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName()));
        sql.append(" WHERE ").append(qfRt.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = '").append(id).append("';");
        sql.append("COMMIT;");
        executeUpdate(sql.toString());
        //effacer les relations !
    }

    protected String getRelationArraysObject(BimCoreModelElement bce, List<String> relations) {
        Stream<String> ownRelations = bce.getRelations().values()
                .stream()
                .filter(bcr -> relations.contains(bcr.getName()))
                .map(getBimCoreModelRelationStringFunction());
        Stream<String> childrenRelations = bce.getChildren()
                .stream()
                .flatMap(it -> it.getRelations().values().stream())
                .filter(bcr -> relations.contains(bcr.getName()))
                .map(getBimCoreModelRelationStringFunction());
        return Stream.concat(ownRelations, childrenRelations).collect(joining(","));
    }

    private static Function<BimCoreModelRelation, String> getBimCoreModelRelationStringFunction() {
        return bcr -> {
            String otherSide = bcr.isDirect() ? DB_REALTIME_REL_ID_BIMCORE_DESTINATION : DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
            try {
                return "array_remove(array_agg(DISTINCT("
                        + addIdentifier(POSTGRESQL, DB_REALTIME_PREFIX_RELTABLE + bcr.getRelationTableName()) + "." + addIdentifier(POSTGRESQL, otherSide)
                        + ")), null) AS " + bcr.getName();
            } catch (DatabaseTypeException e) {
                throw new TechnicalException(e);
            }
        };
    }

    protected String buildJoins(BimCoreModelElement bce, List<String> relations, String alias) {
        return bce.getRelations().values()
                .stream()
                .filter(bcr -> relations.contains(bcr.getName()))
                .map(bcr -> addJoin(bcr, alias))
                .collect(joining("\n", "\n", ""));
    }

    private String addJoin(BimCoreModelRelation bcr, String alias) {
        String ownSide = bcr.isDirect() ? DB_REALTIME_REL_ID_BIMCORE_ORIGIN : DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
        StringBuilder sql = new StringBuilder("LEFT JOIN ");
        sql.append(qfRt.addSchema(DB_REALTIME_PREFIX_RELTABLE + bcr.getRelationTableName()));
        sql.append("\n ON ").append(qfRt.addSchema(DB_REALTIME_PREFIX_RELTABLE + bcr.getRelationTableName())).append(".").append(qfRt.addIdentifier(ownSide));
        sql.append(" = ").append(qfRt.addIdentifier(alias)).append(".").append(qfRt.addIdentifier(DB_REALTIME_ID_BIMCORE));

        return sql.toString();
    }

    public int countRt(BimCriterias criteria) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM ").append(qfRt.addSchema(criteria.table())).append(" root");
        sql.append(specRt.buildClauses(criteria, "root"));
        sql.append(";");

        return execCountRt(sql.toString());
    }

    public int countHistory(BimCriterias criteria) {
        StringBuilder sql = new StringBuilder("SELECT COUNT(*) FROM ").append(qfHisto.addSchema(criteria.table())).append(" root");
        sql.append(specHisto.buildClauses(criteria, "root"));
        sql.append(";");

        return execCountHisto(sql.toString());
    }

    public List<R> getRt(BimCriterias criteria, Long limit, Long offset, List<Sort.Order> orders, List<String> relations) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassR().getSimpleName());
        StringBuilder sql = new StringBuilder("SELECT root.*");

        String relationArray = getRelationArraysObject(bce, relations);
        relationArray = !relationArray.isBlank() ? ", " + relationArray : relationArray;
        sql.append(relationArray);

        sql.append(" FROM ").append(qfRt.addSchema(criteria.table())).append(" root");
        sql.append(buildJoins(bce, relations, "root"));
        sql.append(specRt.buildClauses(criteria, "root"));
        sql.append("\nGROUP BY ").append(qfRt.addIdentifier("root")).append(".").append(qfRt.addIdentifier(DB_REALTIME_ID_BIMCORE));
        sql.append(buildOrders(qfRt, orders, "root"));
        sql.append(buildLimitOffset(limit, offset));
        sql.append(";");

        return execQueryRt(sql.toString(), getClassR());
    }

    public List<H> getHistory(BimCriterias criteria, Long limit, Long offset, List<Sort.Order> orders) {
        StringBuilder sql = new StringBuilder("SELECT root.*");

        sql.append(" FROM ").append(qfHisto.addSchema(criteria.table())).append(" root");
        sql.append(specHisto.buildClauses(criteria, "root"));
        sql.append(buildOrders(qfHisto, orders, "root"));
        sql.append(buildLimitOffset(limit, offset));
        sql.append(";");

        return execQueryHisto(sql.toString(), getClassH());
    }

}


