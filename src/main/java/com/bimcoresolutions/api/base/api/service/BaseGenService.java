package com.bimcoresolutions.api.base.api.service;

import com.bimcoresolutions.api.base.api.dao.BaseGenDao;
import com.bimcoresolutions.util.base.model.HistoryModel;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.Bbox;
import com.bimcoresolutions.util.base.rest.PutMultipleResponse;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.exceptions.ModelException;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.List;

import static com.google.common.collect.Lists.partition;

public abstract class BaseGenService<R extends Model, H extends HistoryModel> {
    static final Logger LOGGER = LogManager.getLogger();
    static final int PACK = 1000;
    private long lastDateModified = Instant.now().toEpochMilli();

    protected final BimCoreModel bimCoreModel;

    protected final BaseGenDao<R, H> dao;

    protected BaseGenService(BaseGenDao<R, H> dao, FunctionalModelService functionalModelService) {
        this.bimCoreModel = functionalModelService.getModel();
        this.dao = dao;
    }

    public void setLastDateModified() {
        this.lastDateModified = Instant.now().toEpochMilli();
    }

    public long getLastDateModified() {
        return this.lastDateModified;
    }

    public long create(List<R> list) {
        return partition(list, PACK).stream().mapToInt(dao::insert).sum();
    }

    public long updateElt(String id, R refCamera) {
        refCamera.set_id_bimcore(id);
        return dao.update(refCamera);
    }

    public PutMultipleResponse updateMultiple(List<R> entity) {
        int success = 0;
        int fail = 0;
        for (R refCamera : entity) {
            try {
                dao.update(refCamera);
                success++;
            } catch (Exception ex) {
                fail++;
                LOGGER.error(ex);
            }
        }
        return new PutMultipleResponse(success, fail);
    }

    public void deleteById(String id) {
        dao.delete(id);
    }

    public Bbox<R> toBbox(R truc) {
        throw new ModelException("This modelClass hasn't any geometric column");
    }

}
