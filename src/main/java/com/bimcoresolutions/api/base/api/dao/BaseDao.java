package com.bimcoresolutions.api.base.api.dao;

import com.bimcoresolutions.api.base.api.model.GeometryFunction;
import com.bimcoresolutions.api.base.configuration.heritage.HeritageHandler;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.sql.DatabaseTypeException;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModel;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelElement;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelRelation;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.BimCoreModelVariable;
import com.bimcoresolutions.util.model.exceptions.ModelException;
import com.bimcoresolutions.util.model.service.FunctionalModelService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import net.postgis.jdbc.PGgeometry;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.text.StringSubstitutor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.WKTReader;
import org.postgresql.jdbc.PgArray;
import org.postgresql.util.PGobject;
import org.reflections.util.ReflectionUtilsPredicates;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.ParameterizedType;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static com.bimcoresolutions.util.base.model.util.enumeration.EBimCoreVariableType.*;
import static com.bimcoresolutions.util.base.realtime.StaticVariables.*;
import static com.bimcoresolutions.util.base.sql.QueryFormer.*;
import static com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType.POSTGRESQL;
import static java.util.stream.Collectors.joining;
import static org.reflections.ReflectionUtils.getAllFields;

public abstract class BaseDao<T extends Model> {
    protected final ObjectMapper om;
    protected final HeritageHandler heritageHandler;
    protected final DataSource dataSource;
    protected final QueryFormer qf;
    protected final BimCoreModel model;
    protected final WKTReader wktReader;
    protected final Predicate<Field> notJsonIgnore = c -> !c.isAnnotationPresent(JsonIgnore.class);

    protected BaseDao(ObjectMapper om, HeritageHandler heritageHandler, @Qualifier("dsRealtime") DataSource dataSource, @Qualifier("qfRealtime") QueryFormer qf, FunctionalModelService functionalModelService) {
        this.om = om;
        this.heritageHandler = heritageHandler;
        this.dataSource = dataSource;
        this.qf = qf;
        this.model = functionalModelService.getModel();
        this.wktReader = new WKTReader();
    }

    public List<T> resultSetToModel(Class<T> clazz, ResultSet rs) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<Integer, String> columnNames = new HashMap<>();
        ResultSetMetaData rsmd = rs.getMetaData();
        for (int i = 1; i < rsmd.getColumnCount() + 1; i++) {
            columnNames.put(i, rsmd.getColumnName(i));
        }
        while (rs.next()) {
            result.add(resultSetToModel(rs, getElement(clazz), columnNames));
        }

        return convertValue(result);
    }

    public List<T> convertValue(List<Map<String, Object>> result) {
        return om.convertValue(result, getTypeRef());
    }

    public abstract TypeReference<List<T>> getTypeRef();

    private <U> BimCoreModelElement getElement(Class<U> clazz) {
        return model.getElementsBimCore().get(clazz.getSimpleName());
    }

    private String getEntityName(String name) {
        return name.substring(3);
    }

    @SneakyThrows
    private Map<String, Object> resultSetToModel(ResultSet rs, BimCoreModelElement elements, Map<Integer, String> columnNames) {
        Map<String, Object> result = new HashMap<>();
        for (int i = 1; i <= columnNames.size(); i++) {
            String columnName = columnNames.get(i);
            if (rs.getObject(i) != null) { //ne pas setter les nulls, permet de prendre les valeurs par defaut pour les collections
                result.put(columnName, convert(rs.getObject(i), elements.getVariables().get(columnName)));
            }
        }
        result.put(DB_REALTIME_ID_BIMCORE, rs.getString(DB_REALTIME_ID_BIMCORE));
        return result;
    }

    @SneakyThrows
    private Object convert(Object object, BimCoreModelVariable element) {
        if (object == null) {
            return null;
        }
        if (element != null && element.getVariableType() == GEOMETRY) {
            StringBuffer sb = new StringBuffer();
            ((PGgeometry) object).getGeometry().outerWKT(sb);
            Geometry geometryObject = wktReader.read(sb.toString());
            return geometryObject;
        }
        if (element != null && element.getVariableType() == JSON) {
            return om.readValue(((PGobject) object).getValue(), Map.class);
        }
        if (element != null && element.getVariableType() == ARRAY) {
            return om.readValue(((PGobject) object).getValue(), List.class);
        }
        if (object instanceof PgArray array) {
            List<String> result = new ArrayList<>();
            String[] arry = (String[]) array.getArray();
            Collections.addAll(result, arry);
            return result;
        }

        return object;
    }

    protected TypeReference<Map<String, Object>> typeReference = new TypeReference<>() {
    };
    private static final Logger LOGGER = LogManager.getLogger();

    public abstract String colToElemtableColumn(String col);

    public abstract Pair<String, Boolean> colToRelTableAndWay(String col);

    @SneakyThrows
    public List<T> execQuery(Class<T> clazz, String query) {
        LOGGER.debug(query);
        ResultSet rs;
        try (Connection con = dataSource.getConnection(); Statement stmtQuery = con.createStatement()) {
            rs = stmtQuery.executeQuery(query);
            return resultSetToModel(clazz, rs);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    public int executeUpdate(String query) {
        LOGGER.debug(query);
        try (Connection con = dataSource.getConnection(); Statement stmtQuery = con.createStatement()) {
            return stmtQuery.executeUpdate(query);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    public int execCount(String query) {
        LOGGER.debug(query);
        try (Connection con = dataSource.getConnection(); Statement stmtQuery = con.createStatement(); ResultSet rs = stmtQuery.executeQuery(query)) {
            rs.next();
            return rs.getInt(1);
        } catch (SQLException e) {
            throw new TechnicalException(e);
        }
    }

    public StringBuilder testLimitOffset(Long limit, Long offset) {
        StringBuilder sql = new StringBuilder();

        if (limit != null) {
            if (offset != null) { // Limit et offset
                sql.append(" LIMIT " + limit + " OFFSET " + offset);
            } else { // Seulement limit
                sql.append(" LIMIT " + limit + " OFFSET 0");
            }
        } else {
            if (offset != null) { // Seulement offset
                sql.append(" OFFSET " + offset);
            } else { // Ni limit, ni offset
            }
        }
        return sql;
    }

    public StringBuilder testWhereClauseQuery(Map<String, Object> whereClause, List<String> isnull, List<String> isnotnull) {
        StringBuilder sql = new StringBuilder();
        if (CollectionUtils.isEmpty(whereClause) && CollectionUtils.isEmpty(isnull) && CollectionUtils.isEmpty(isnotnull)) {
            return sql;
        }
        for (Map.Entry<String, Object> att : whereClause.entrySet()) {
            sql.append(" AND");
            sql.append(" root." + qf.addIdentifier(att.getKey().toLowerCase()) + " = '" + att.getValue().toString().replace("'", "''") + "'");
        }
        if (isnull != null) {
            for (String att : isnull) {
                sql.append(" AND");
                sql.append(" root." + qf.addIdentifier(att.toLowerCase()) + " is null ");
            }
        }
        if (isnotnull != null) {
            for (String att : isnotnull) {
                sql.append(" AND");
                sql.append(" root." + qf.addIdentifier(att.toLowerCase()) + " is not null ");
            }
        }
        return sql;
    }

    public <T extends Model> StringBuilder testOrderBy(Class<T> clazz, List<String> orderBy) {
        StringBuilder sql = new StringBuilder();
        if (orderBy != null && !orderBy.isEmpty()) { // Si on a un pattern spécifié
            sql.append(" ORDER BY ");
            for (String column : orderBy) {
                Optional<Field> opField = getAllFields(clazz, ReflectionUtilsPredicates.withName(column)).stream().findFirst();
                if (opField.isPresent()) { // Si ce champ est bien une colonne de la base
                    sql.append(qf.addIdentifier(column) + ", ");
                } else { // Si ce champ n'est pas une colonne de la base
                    LOGGER.error("Unknown field : {}", column);
                    throw new TechnicalException("Unknown field : " + column);
                }
            }
            sql.delete(sql.length() - 2, sql.length());
            return sql;
        } else {
            return null;
        }
    }

    public StringBuilder testOrder(String order) {
        StringBuilder sql = new StringBuilder();
        if (order != null) {
            if (order.equalsIgnoreCase("ASC") || order.equalsIgnoreCase("DESC")) {
                sql.append(" " + order);
            } else {
                throw new TechnicalException("Field 'order' unknown (ASC/DESC)");
            }
        }
        return sql;
    }

    public StringBuilder testWhereClauseSearchQuery(String key, List<String> champs) {
        StringBuilder sql = new StringBuilder();
        if (!champs.isEmpty()) {
            sql.append(" AND (");
            List<String> likeClauses = new ArrayList<>();
            for (String att : champs) {
                likeClauses.add("root." + qf.addIdentifier(att) + " like '%" + key.replace("'", "''") + "%'");
            }
            sql.append(StringUtils.join(likeClauses, " OR "));
            sql.append(")");
        }
        return sql;
    }

    public <T extends Model> Pair<List<Pair<String, String>>, Map<String, List<String>>> verifyColumns(Class<T> clazz, Collection<String> att, Collection<String> rel,
                                                                                                       Map<String, Object> colValues) {
        List<Pair<String, String>> attributes = new ArrayList<>();
        Map<String, List<String>> relations = new HashMap<>();

        for (Map.Entry<String, Object> e : colValues.entrySet()) {
            String column = e.getKey();
            Object value = e.getValue();
            Optional<Field> optionalField = getAllFields(clazz, ReflectionUtilsPredicates.withName(column)).stream().findFirst();
            if (!optionalField.isPresent()) {
                LOGGER.warn("Unknown field for class {} : {}", clazz, column);
            } else {
                Field field = optionalField.get();
                if (att.contains(field.getName())) {
                    attributes.add(Pair.of(colToElemtableColumn(column), convertAttributeValueToSQL(field, value)));
                } else if (rel.contains(field.getName())) {
                    relations.put(column, (ArrayList<String>) value);
                }
            }
        }
        return Pair.of(attributes, relations);
    }

    @SneakyThrows
    public String convertAttributeValueToSQL(Field field, Object value) {
        if (value == null) { // Si pas de valeurs à insérer => NULL
            return betweenSimpleQuote("NULL");
        } else if (field.getType().isAssignableFrom(Boolean.class)) { // fields Boolean
            String booleanValue = String.valueOf(value);
            String booleanString;
            if (booleanValue.equalsIgnoreCase("true") || booleanValue.equalsIgnoreCase("1") || booleanValue.equalsIgnoreCase("t")) {
                booleanString = "1";
            } else if (booleanValue.equalsIgnoreCase("false") || booleanValue.equalsIgnoreCase("0") || booleanValue.equalsIgnoreCase("f")) {
                booleanString = "0";
            } else {
                throw new TechnicalException("Fail to cast boolean value :" + booleanValue);
            }
            return betweenSimpleQuote(booleanString);
        } else if (field.getType().isAssignableFrom(Integer.class) || field.getType().isAssignableFrom(Float.class) || field.getType()
                .isAssignableFrom(Long.class) || field.getType().isAssignableFrom(Double.class)) { // fields numériques
            return betweenSimpleQuote(String.valueOf(value));
        } else if (field.getType().isAssignableFrom(java.util.Date.class)) { // fields date
            String dateString;
            // dans le cas insert, c'est un objet Date, dans le cas update, c'est une String
            if (value.getClass().isAssignableFrom(java.util.Date.class)) {
                dateString = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format((Date) value);
            } else {
                dateString = value.toString();
            }
            return betweenSimpleQuote(String.valueOf(dateString));
        } else if (field.getType().isAssignableFrom(Geometry.class)) { // fields date
            return betweenSimpleQuote("SRID=4326;" + value); //TODO faire plus beau
        } else if (value instanceof Map map) {
            return betweenSimpleQuote(om.writeValueAsString(map));
        } else { // fields String
            return betweenSimpleQuote(formatValueToSQL(value.toString()));
        }
    }

    public int insert(List<T> elements) {
        StringBuilder sql = new StringBuilder();
        Predicate<Field> ofListType = c -> (c.getType().isAssignableFrom(List.class));
        Predicate<Field> notOfListType = c -> !(c.getType().isAssignableFrom(List.class));
        try {
            for (T obj : elements) {
                // Attributes
                sql.append("INSERT INTO ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName())).append(" (");
                Set<Field> attributeFields = getAllFields(getClassT(), notJsonIgnore, notOfListType);
                StringBuilder cols = new StringBuilder("");
                StringBuilder values = new StringBuilder("");

                for (Field field : attributeFields) {
                    String fieldName = field.getName();
                    if (fieldName.equals(DB_REALTIME_ID_BIMCORE)||fieldName.equals(DB_REALTIME_PERIMETERS)) {
                        cols.append(qf.addIdentifier(fieldName)).append(",");
                    } else {
                        cols.append(qf.addIdentifier(colToElemtableColumn(fieldName))).append(",");
                    }
                    values.append(convertAttributeValueToSQL(field, getClassT().getMethod(fieldToGetter(field)).invoke(obj))).append(",");
                }
                cols.append(qf.addIdentifier(DB_REALTIME_TYPEOF));
                values.append(betweenSimpleQuote(formatValueToSQL(heritageHandler.getTypeOfRealtime().get(getEntityName()))));

                sql.append(cols).append(") VALUES (").append(values).append(");");

                // Relations
                Set<Field> relationFields = getAllFields(getClassT(), notJsonIgnore, ofListType);

                for (Field field : relationFields) {
                    List<String> idDestinations = (List<String>) getClassT().getMethod(fieldToGetter(field)).invoke(obj);
                    if (idDestinations != null && !idDestinations.isEmpty()) {
                        Pair<String, Boolean> tableNameAndWay = colToRelTableAndWay(field.getName());
                        String originCol;
                        String destCol;
                        if (Boolean.TRUE.equals(tableNameAndWay.getRight())) {
                            originCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                            destCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                        } else {
                            originCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                            destCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                        }

                        sql.append("INSERT INTO ").append(qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + tableNameAndWay.getLeft())).append("(");
                        sql.append(qf.addIdentifier(originCol)).append(",").append(qf.addIdentifier(destCol)).append(") VALUES ");
                        for (String idDest : idDestinations) {
                            sql.append("(").append(betweenSimpleQuote(formatValueToSQL(obj.get_id_bimcore()))).append(",");
                            sql.append(betweenSimpleQuote(formatValueToSQL(idDest))).append("),");
                        }
                        sql.delete(sql.length() - 1, sql.length()).append(";");
                    }
                }
                executeUpdate(sql.toString());
            }
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            throw new TechnicalException(e);
        }
        return elements.size();
    }

    private String fieldToGetter(Field field) {
        String fieldName = field.getName();
        return "get" + fieldName.substring(0, 1).toUpperCase(Locale.ROOT) + fieldName.substring(1);
    }

    protected String getEntityName() {
        return getClassT().getSimpleName();
    }

    @SneakyThrows
    public void update(T t) {
        Map<String, Object> colValue = om.convertValue(t, typeReference);
        StringBuilder sql = new StringBuilder();
        Pair<List<Pair<String, String>>, Map<String, List<String>>> verified = verifyColumns(getClassT(), getAttributes(), List.of(), colValue);
        List<Pair<String, String>> attributes = verified.getLeft();
        Map<String, List<String>> relations = verified.getRight();
        String idOrigin = colValue.get(DB_REALTIME_ID_BIMCORE).toString();

        sql.append("BEGIN;");
        // Attributes
        sql.append("UPDATE ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName())).append(" SET ");
        attributes.forEach((Pair<String, String> att) -> {
            sql.append(qf.addIdentifier(colToElemtableColumn(att.getLeft()))).append(" = ").append(att.getRight()).append(",");
        });
        sql.delete(sql.length() - 1, sql.length());
        sql.append(" WHERE ").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = ");
        sql.append(betweenSimpleQuote(formatValueToSQL(colValue.get(DB_REALTIME_ID_BIMCORE).toString()))).append(";");

        // Relations
        relations.entrySet().forEach((Map.Entry<String, List<String>> rel) -> {
            String col = rel.getKey();
            Pair<String, Boolean> tableNameAndWay = colToRelTableAndWay(col);
            String originCol;
            String destCol;
            if (Boolean.TRUE.equals(tableNameAndWay.getRight())) {
                originCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
                destCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
            } else {
                originCol = DB_REALTIME_REL_ID_BIMCORE_DESTINATION;
                destCol = DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
            }
            List<String> idDests = rel.getValue();

            sql.append("DELETE FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + tableNameAndWay.getLeft())).append(" WHERE ");
            sql.append(qf.addIdentifier(originCol)).append(" = ").append(betweenSimpleQuote(formatValueToSQL(idOrigin)));
            sql.append(" AND ").append(qf.addIdentifier(destCol)).append(" NOT IN (");
            idDests.forEach((String dest) -> sql.append(betweenSimpleQuote(formatValueToSQL(dest))).append(","));
            sql.delete(sql.length() - 1, sql.length()).append(");");

            sql.append("INSERT INTO ").append(qf.addSchema(DB_REALTIME_PREFIX_RELTABLE + tableNameAndWay.getLeft())).append("(");
            sql.append(qf.addIdentifier(originCol)).append(",").append(qf.addIdentifier(destCol)).append(") VALUES ");
            idDests.forEach((String idDest) -> {
                sql.append("(").append(betweenSimpleQuote(formatValueToSQL(idOrigin))).append(",");
                sql.append(betweenSimpleQuote(formatValueToSQL(idDest))).append("),");
            });
            sql.delete(sql.length() - 1, sql.length());
            sql.append(" ON CONFLICT (").append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_ORIGIN)).append(",");
            sql.append(qf.addIdentifier(DB_REALTIME_REL_ID_BIMCORE_DESTINATION)).append(") DO NOTHING").append(";");
        });
        sql.append("COMMIT;");

        executeUpdate(sql.toString());
    }

    private List<String> getAttributes() throws IllegalAccessException, NoSuchFieldException {
        return (List<String>) getClassT().getField("ATTRIBUTES").get(null);
    }

    public Long count(Map<String, Object> whereClause, List<String> isNull, List<String> isNotNull) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT (*)");
        sql.append(" FROM " + qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName()) + " AS root ");
        sql.append(" WHERE root.").append(qf.addIdentifier(DB_REALTIME_TYPEOF)).append(" IN (");
        for (String itselfOrChild : heritageHandler.getHeritageMFRealtime().get(getEntityName())) {
            sql.append(betweenSimpleQuote(formatValueToSQL(heritageHandler.getTypeOfRealtime().get(itselfOrChild)))).append(",");
        }
        sql.delete(sql.length() - 1, sql.length()).append(")");
        sql.append(testWhereClauseQuery(whereClause, isNull, isNotNull));
        return Long.valueOf(execCount(sql.toString()));
    }

    public Long searchCount(String key, List<String> champs) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT (*)");
        sql.append(" FROM " + qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName()) + " AS root ");
        sql.append(" WHERE root.").append(qf.addIdentifier(DB_REALTIME_TYPEOF)).append(" IN (");
        for (String itselfOrChild : heritageHandler.getHeritageMFRealtime().get(getEntityName())) {
            sql.append(betweenSimpleQuote(formatValueToSQL(heritageHandler.getTypeOfRealtime().get(itselfOrChild)))).append(",");
        }
        sql.delete(sql.length() - 1, sql.length()).append(")");
        sql.append(testWhereClauseSearchQuery(key, champs));
        return Long.valueOf(execCount(sql.toString()));
    }

    public List<T> search(String key, List<String> champs, List<String> pattern, Long limit, Long offset, List<String> orderBy, String order) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT root.* ${relationArray}
                FROM "${schema}"."${table}" root
                    ${jointure}
                WHERE ${where}
                GROUP BY root."${_id_bimcore}"
                ${pagination};""";
        String relationArray = getRelationArraysObject(bce);
        relationArray = !relationArray.isBlank() ? ", " + relationArray : relationArray;

        String jointures = getJointures(bce);
        String where = "root." + qf.addIdentifier(DB_REALTIME_TYPEOF) + " = " + betweenSimpleQuote(
                formatValueToSQL(heritageHandler.getTypeOfRealtime().get(getEntityName()))) + " " + testWhereClauseSearchQuery(key, champs);

        String pagination = getAllPagination(orderBy, order, limit, offset);
        Map<String, Object> valuesMap = Map.of(
                "relationArray", relationArray,
                "table", DB_REALTIME_PREFIX_ELEMTABLE + bce.getName(),
                "jointure", jointures,
                "where", where,
                "schema", qf.getSchema(),
                "_id_bimcore", DB_REALTIME_ID_BIMCORE,
                "pagination", pagination);
        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return execQuery(getClassT(), resolvedString);
    }

    private String getLimitAndOrderBy(Long limit, Long offset, List<String> orderBy, String order) {
        String sql = "";
        StringBuilder stb = testOrderBy(getClassT(), orderBy);
        StringBuilder stbOrderclause = new StringBuilder();
        // Test de order
        if (stb != null && !orderBy.isEmpty()) {
            stbOrderclause.append(stb);
            stbOrderclause.append(testOrder(order));
            sql += stb;
            sql += testOrder(order);
            // Test de Limit et offset
            if (!org.springframework.util.StringUtils.isEmpty(order)) {
                sql += testLimitOffset(limit, offset);
            }
        }
        if ((limit != null || offset != null) && (CollectionUtils.isEmpty(orderBy) || org.springframework.util.StringUtils.isEmpty(order))) {
            throw new TechnicalException("orderby et order doivent être spécifiés pour utiliser limit/offset");
        }
        return sql + ";";
    }

    public Long findByCount(String field, List<String> values) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT count (*)
                FROM "${schema}"."${table}" root
                WHERE ${where}
                  AND root."_typeof" = '0';
                """;

        Map<String, Object> valuesMap = Map.of(
                "schema", qf.getSchema(),
                "table", DB_REALTIME_PREFIX_ELEMTABLE + bce.getName(),
                "where", getByWithRelationSqlWhereClause(values, field));

        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String sql = sub.replace(template);

        return Long.valueOf(execCount(sql));
    }

    public List<T> findBy(String field, List<String> values, Long limit, Long offset, List<String> orderBy, String order) {
        String sql = getByWithRelationSql(values, field);
        // Test de order
        sql += getLimitAndOrderBy(limit, offset, orderBy, order);
        try {
            return execQuery(getClassT(), sql);
        } catch (TechnicalException e) {
            LOGGER.error("SQL : {}", sql);
            throw e;
        }
    }

    public List<T> getAll(Map<String, Object> whereClause, List<String> pattern, Long limit, Long offset, List<String> orderBy, String order, List<String> isNull,
                          List<String> isNotNull) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT root.* ${relationArray}
                FROM "${schema}"."${table}" root
                    ${jointure}
                WHERE ${where}
                GROUP BY root."${_id_bimcore}"
                ${pagination};""";
        String relationArray = getRelationArraysObject(bce);
        relationArray = !relationArray.isBlank() ? ", " + relationArray : relationArray;

        String jointures = getJointures(bce);
        String where = "root." + qf.addIdentifier(DB_REALTIME_TYPEOF) + " = " + betweenSimpleQuote(
                formatValueToSQL(heritageHandler.getTypeOfRealtime().get(getEntityName()))) + " " + testWhereClauseQuery(whereClause, isNull, isNotNull);
        String pagination = getAllPagination(orderBy, order, limit, offset);
        Map<String, Object> valuesMap = Map.of(
                "relationArray", relationArray,
                "table", DB_REALTIME_PREFIX_ELEMTABLE + bce.getName(),
                "jointure", jointures,
                "where", where,
                "schema", qf.getSchema(),
                "_id_bimcore", DB_REALTIME_ID_BIMCORE,
                "pagination", pagination);
        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return execQuery(getClassT(), resolvedString);
    }

    protected String getAllPagination(List<String> orderBy, String order, Long limit, Long offset) {
        StringBuilder res = new StringBuilder();
        // Test de orderBy
        StringBuilder stb = testOrderBy(getClassT(), orderBy);
        StringBuilder stbOrderclause = new StringBuilder();
        // Test de order
        if (stb != null && !orderBy.isEmpty()) {
            stbOrderclause.append(stb);
            stbOrderclause.append(testOrder(order));
            res.append(stb);
            res.append(testOrder(order));
            // Test de Limit et offset
            if (!org.springframework.util.StringUtils.isEmpty(order)) {
                res.append(testLimitOffset(limit, offset));
            }
        }
        if ((limit != null || offset != null) && (ObjectUtils.isEmpty(orderBy) || org.springframework.util.StringUtils.isEmpty(order))) {
            throw new TechnicalException("orderby et order doivent être spécifiés pour utiliser limit/offset");
        }
        return res.toString();
    }

    public T getId(String id) {
        String sql = getByIdWithRelationSql(id);
        try {
            List<T> objs = execQuery(getClassT(), sql);
            if (!ObjectUtils.isEmpty(objs)) {
                return objs.get(0);
            } else {
                return null;
            }
        } catch (TechnicalException e) {
            LOGGER.error("SQL : {}", sql);
            throw e;
        }
    }

    public List<T> getIds(List<String> ids) {
        String sql = getByIdsWithRelationSql(ids);
        try {
            List<T> objs = execQuery(getClassT(), sql);
            return objs;
        } catch (TechnicalException e) {
            LOGGER.error("SQL : {}", sql);
            throw e;
        }
    }

    public void delete(String id) {
        StringBuilder sql = new StringBuilder("BEGIN;");
        sql.append("DELETE FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName()));
        sql.append(" WHERE ").append(qf.addIdentifier(DB_REALTIME_ID_BIMCORE)).append(" = '").append(id).append("';");
        sql.append("COMMIT;");
        executeUpdate(sql.toString());
        //effacer les relations !
    }

    public List<T> bbox(List<Float> bbox, List<String> relations) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT root.* ${relationArray}
                FROM "${schema}"."${table}" root
                     ${jointure}
                WHERE ${where}
                GROUP BY root."${_id_bimcore}";""";
        String relationArray = getRelationArraysObject(bce, relations);
        relationArray = !relationArray.isBlank() ? ", " + relationArray : relationArray;
        String jointures = getJointures(bce, relations);
        String where = bboxWhereClause(bbox);

        Map<String, Object> valuesMap = Map.of(
                "relationArray", relationArray,
                "table", DB_REALTIME_PREFIX_ELEMTABLE + bce.getName(),
                "jointure", jointures,
                "where", where,
                "_id_bimcore", DB_REALTIME_ID_BIMCORE,
                "schema", qf.getSchema());

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return execQuery(getClassT(), resolvedString);
    }

    private String bboxWhereClause(List<Float> bbox) {
        String template = """
                 root."_typeof" IN ('0')
                AND (ST_Intersects("location", ST_Envelope(ST_GeomFromText('LINESTRING(${a} ${b}, ${c} ${d})', 4326))))""";

        // Build StringSubstitutor
        Map<String, Object> valuesMap = Map.of(
                "a", bbox.get(0),
                "b", bbox.get(1),
                "c", bbox.get(2),
                "d", bbox.get(3));
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return resolvedString;
    }

    protected Class<T> getClassT() {
        return (Class<T>) ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    public String getByIdWithRelationSql(String id) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT root.* ${relationArray}
                FROM "${schema}"."${table}" root
                     ${jointure}
                WHERE root."${_id_bimcore}" = '${id}'
                  AND root."_typeof" = '0'
                GROUP BY root."${_id_bimcore}";""";
        String relationArray = getRelationArraysObject(bce);
        relationArray = !relationArray.isBlank() ? "," + relationArray : relationArray;
        String jointures = getJointures(bce);

        Map<String, Object> valuesMap = Map.of(
                "relationArray", relationArray,
                "table", DB_REALTIME_PREFIX_ELEMTABLE + bce.getName(),
                "jointure", jointures,
                "id", id,
                "_id_bimcore", DB_REALTIME_ID_BIMCORE,
                "schema", qf.getSchema());

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return resolvedString;

    }

    public String getByIdsWithRelationSql(List<String> ids) {
        return getByWithRelationSql(ids, DB_REALTIME_ID_BIMCORE);
    }

    public String getByWithRelationSql(List<String> values, String field) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        String template = """
                SELECT root.* ${relationArray}
                FROM "${schema}"."${table}" root
                     ${jointure}
                WHERE ${where}
                  AND root."_typeof" = '0'
                GROUP BY root."${_id_bimcore}";""";

        String relationArray = getRelationArraysObject(bce);
        relationArray = !relationArray.isBlank() ? "," + relationArray : relationArray;

        Map<String, Object> valuesMap = Map.of(
                "relationArray", relationArray,
                "schema", qf.getSchema(),
                "table", DB_REALTIME_PREFIX_ELEMTABLE + bce.getName(),
                "jointure", getJointures(bce),
                "_id_bimcore", DB_REALTIME_ID_BIMCORE,
                "where", getByWithRelationSqlWhereClause(values, field));

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return resolvedString;
    }

    protected String getByWithRelationSqlWhereClause(List<String> values, String field) {
        return StringUtils.isEmpty(field) || CollectionUtils.isEmpty(values) ?
                "1 = 1" :
                "root." + qf.addIdentifier(field) + " in (" + values.stream().map(it -> betweenSimpleQuote(formatValueToSQL(it))).collect(joining(",")) + ")";
    }

    protected String getRelationArraysObject(BimCoreModelElement bce) {
        Stream<String> relations = bce.getRelations().values()
                .stream()
                .map(getBimCoreModelRelationStringFunction());
        Stream<String> children = bce.getChildren()
                .stream()
                .flatMap(it -> it.getRelations().values().stream())
                .map(getBimCoreModelRelationStringFunction());
        return Stream.concat(relations, children).collect(joining(","));
    }

    protected String getRelationArraysObject(BimCoreModelElement bce, List<String> relations) {
        Stream<String> ownRelations = bce.getRelations().values()
                .stream()
                .filter(bcr -> relations.contains(bcr.getName()))
                .map(getBimCoreModelRelationStringFunction());
        Stream<String> childrenRelations = bce.getChildren()
                .stream()
                .flatMap(it -> it.getRelations().values().stream())
                .filter(bcr -> relations.contains(bcr.getName()))
                .map(getBimCoreModelRelationStringFunction());
        return Stream.concat(ownRelations, childrenRelations).collect(joining(","));
    }

    private static Function<BimCoreModelRelation, String> getBimCoreModelRelationStringFunction() {
        return bcr -> {
            String otherSide = bcr.isDirect() ? DB_REALTIME_REL_ID_BIMCORE_DESTINATION : DB_REALTIME_REL_ID_BIMCORE_ORIGIN;
            try {
                return "array_remove(array_agg(DISTINCT("
                        + addIdentifier(POSTGRESQL, DB_REALTIME_PREFIX_RELTABLE + bcr.getRelationTableName()) + "." + addIdentifier(POSTGRESQL, otherSide)
                        + ")), null) AS " + bcr.getName();
            } catch (DatabaseTypeException e) {
                throw new RuntimeException(e);
            }
        };
    }

    protected String getJointures(BimCoreModelElement bce) {
        return bce.getRelations().values()
                .stream()
                .map(this::ajouterLaJointure)
                .collect(joining("\n"));
    }

    protected String getJointures(BimCoreModelElement bce, List<String> relations) {
        return bce.getRelations().values()
                .stream()
                .filter(bcr -> relations.contains(bcr.getName()))
                .map(this::ajouterLaJointure)
                .collect(joining("\n"));
    }

    private String ajouterLaJointure(BimCoreModelRelation bcr) {
        String ownSide = bcr.isDirect() ? DB_REALTIME_REL_ID_BIMCORE_ORIGIN : DB_REALTIME_REL_ID_BIMCORE_DESTINATION;

        String template = """
                LEFT JOIN "${schema}"."${relTableName}"
                    ON "${schema}"."${relTableName}"."${ownSide}" = root."${_id_bimcore}\"""";
        Map<String, String> valuesMap = Map.of(
                "ownSide", ownSide,
                "relTableName", DB_REALTIME_PREFIX_RELTABLE + bcr.getRelationTableName(),
                "_id_bimcore", DB_REALTIME_ID_BIMCORE,
                "schema", qf.getSchema());

        // Build StringSubstitutor
        StringSubstitutor sub = new StringSubstitutor(valuesMap);

        // Replace
        String resolvedString = sub.replace(template);
        return resolvedString;
    }

    @SneakyThrows
    public void execute(String sql) {
        try (Connection con = dataSource.getConnection(); Statement stmtQuery = con.createStatement()) {
            stmtQuery.execute(sql);
        }
    }

    public List<T> geometry(GeometryFunction function, Geometry geometry) {
        BimCoreModelElement bce = model.getElementsBimCore().get(getClassT().getSimpleName());
        Optional<BimCoreModelVariable> geometryColumn = bce.getSpacialField();

        if (geometryColumn.isPresent()) {
            StringBuilder query = new StringBuilder("SELECT * FROM ").append(qf.addSchema(DB_REALTIME_PREFIX_ELEMTABLE + getEntityName()));
            query.append(" WHERE ");
            String colName = geometryColumn.get().getName();
            switch (function) {
                case contains ->
                        query.append("ST_CONTAINS(st_geometryfromtext('").append(geometry.toText()).append("'").append(", '").append(geometry.getSRID()).append("'), ").append(qf.addIdentifier(colName)).append(")");
                case covers ->
                        query.append("ST_COVERS(st_geometryfromtext('").append(geometry.toText()).append("'").append(", '").append(geometry.getSRID()).append("'), ").append(qf.addIdentifier(colName)).append(")");
                case within ->
                        query.append("ST_WITHIN(st_geometryfromtext('").append(geometry.toText()).append("'").append(", '").append(geometry.getSRID()).append("'), ").append(qf.addIdentifier(colName)).append(")");
                default -> throw new TechnicalException(function.name() + " not handled yet");
            }
            query.append(";");
            return execQuery(getClassT(), query.toString());
        } else {
            throw new ModelException("Class " + bce.getName() + " hasn't any geometric column");
        }
    }
}


