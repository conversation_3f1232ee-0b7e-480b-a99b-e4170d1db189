package com.bimcoresolutions.api.base.modules.updatemetier;

import com.bimcoresolutions.api.base.configuration.ConfigGrpc;
import com.bimcoresolutions.api.base.modules.servercom.commitmetier.CommitMetierExchange;
import com.bimcoresolutions.api.base.modules.servercom.commitmetier.RtCommitMetierHandler;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.api.base.util.exceptions.TimeoutException;
import com.bimcoresolutions.api.base.util.exceptions.UnauthorizedException;
import com.bimcoresolutions.util.base.FunctionalException;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.perimeters.CMPerimeters;
import com.bimcoresolutions.util.grpc.utilgrpc.generated.commitmetier.CommitMetierRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

import static java.util.UUID.randomUUID;

@Log4j2
@Service
@ConditionalOnProperty(prefix = "bim", name = "perimeters", havingValue = "true")
public class UpdatePerimeterService {

    private final long commitAcqTimeoutMs;
    private final long commitResultTimeoutMs;

    private final RtCommitMetierHandler rtCommitMetierHandler;
    @Getter
    private final Map<String, PerimeterComputer<?>> perimeterComputers = new HashMap<>();

    private final ObjectMapper objectMapper;

    public UpdatePerimeterService(ConfigGrpc confRpc, RtCommitMetierHandler rtCommitMetierHandler, ObjectMapper objectMapper) {
        this.rtCommitMetierHandler = rtCommitMetierHandler;
        this.objectMapper = objectMapper;

        this.commitAcqTimeoutMs = confRpc.getConnection().getCommitAcqTimeout();
        this.commitResultTimeoutMs = confRpc.getConnection().getCommitResultTimeout();
    }


    public void getPerimeterComputer(CMPerimeters updatePerimeters) {
        updatePerimeters.getPerimeters().forEach(
                k -> k.getAdd().forEach((s, l) -> {
                    PerimeterComputer<?> computer = perimeterComputers.get(s);
                    computer.computeOnUpdatePerimeter(updatePerimeters, l);
                }));
    }

    public String updatePerimeters(CMPerimeters updatePerimeters) {
        checkSecurity();
        getPerimeterComputer(updatePerimeters);
        try {
            return handleCommitToServer(updatePerimeters);
        } catch (FunctionalException ex) {
            log.warn("Commit on server TR failed", ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("Commit on server TR failed", ex);
            throw new TechnicalException(ex);
        }
    }

    void checkSecurity() {
        JwtAuthenticationToken user = (JwtAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        if (!user.getAuthorities().contains(new SimpleGrantedAuthority("ROLE_admin"))) {
            throw new UnauthorizedException();
        }
    }

    @SneakyThrows
    private String handleCommitToServer(CMPerimeters commitMetierJson) {
        // handlerLdap modify the commit so it is necessary to send commitMetierJson to server and not updateMetier
        String uuid = randomUUID().toString();
        CommitMetierRequest.Builder builder = CommitMetierRequest.newBuilder();
        builder.setUuid(uuid);
        builder.setContent(objectMapper.writeValueAsString(commitMetierJson));
        CommitMetierRequest commitMetierRequest = builder.build();
        log.debug("Request - CommitMetier : {}", () -> StringUtils.normalizeSpace(commitMetierRequest.toString()));

        // Synchronous send of the request to TR
        CommitMetierExchange commitMetierExchange = rtCommitMetierHandler.addRequest(commitMetierRequest);

        long start = Instant.now().toEpochMilli();
        // Waiting for acq
        try {
            synchronized (commitMetierExchange.getSynchroAcq()) {
                while (commitMetierExchange.getResponseAcq() == null && commitMetierExchange.getError() == null && (Instant.now().toEpochMilli() - start) < commitAcqTimeoutMs) {
                    // TODO : gestion du timeout parametre dans la conf + par l'appelant => .wait(tiemout)
                    commitMetierExchange.getSynchroAcq().wait(commitAcqTimeoutMs);
                }
            }
            if (commitMetierExchange.getError() != null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new FunctionalException("Failed to be processed : " + commitMetierExchange.getError());
            }
            if (commitMetierExchange.getResponseAcq() == null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new TimeoutException("UpdatePerimeter ACQ timeout");
            }
        } catch (InterruptedException e) {
            rtCommitMetierHandler.purgeRequest(uuid);
            throw new TechnicalException("Waiting during CommitPerimeter Acq Response has been interrupted", e);
        }

        // Waiting for the TR compute result
        try {
            synchronized (commitMetierExchange.getSynchroResult()) {
                while (commitMetierExchange.getResponseResult() == null && commitMetierExchange.getError() == null && (Instant.now()
                                                                                                                              .toEpochMilli() - start) < commitResultTimeoutMs) {
                    // TODO : gestion du timeout parametre dans la conf + par l'appelant => .wait(tiemout)
                    commitMetierExchange.getSynchroResult().wait(commitResultTimeoutMs);
                }
            }
            if (commitMetierExchange.getError() != null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new FunctionalException("Failed to be processed : " + commitMetierExchange.getError());
            }
            if (commitMetierExchange.getResponseResult() == null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new TimeoutException("CommitPerimeter Result timeout");
            }
        } catch (InterruptedException e) {
            rtCommitMetierHandler.purgeRequest(uuid);
            throw new TechnicalException("Waiting during CommitPerimeter Result Response has been interrupted", e);
        }

        String result = objectMapper.writeValueAsString(commitMetierExchange.getResponseResult().getResult());
        rtCommitMetierHandler.purgeRequest(uuid);
        return result;
    }

}
