package com.bimcoresolutions.api.base.modules.updatemetier;

import com.bimcoresolutions.api.base.configuration.ConfigGrpc;
import com.bimcoresolutions.api.base.modules.servercom.commitmetier.CommitMetierExchange;
import com.bimcoresolutions.api.base.modules.servercom.commitmetier.RtCommitMetierHandler;
import com.bimcoresolutions.api.base.util.exceptions.TechnicalException;
import com.bimcoresolutions.api.base.util.exceptions.TimeoutException;
import com.bimcoresolutions.util.base.FunctionalException;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.EntityBody;
import com.bimcoresolutions.util.grpc.utilgrpc.generated.commitmetier.CommitMetierRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.bimcoresolutions.util.base.realtime.StaticVariables.DB_REALTIME_PERIMETERS;
import static java.util.UUID.randomUUID;
import static org.apache.commons.collections4.CollectionUtils.isEmpty;

@Log4j2
@Service
public class UpdateMetierService {

    private final long commitAcqTimeoutMs;
    private final long commitResultTimeoutMs;

    private final ApplicationContext applicationContext;

    private final RtCommitMetierHandler rtCommitMetierHandler;

    private final ObjectMapper objectMapper;

    private final UpdateMetierSecurityService updateMetierSecurityService;

    private final boolean perimetersInstance;

    @Getter
    private final Map<String, PerimeterComputer<?>> perimeterComputers = new HashMap<>();

    public UpdateMetierService(ApplicationContext applicationContext, ConfigGrpc confRpc, RtCommitMetierHandler rtCommitMetierHandler, ObjectMapper objectMapper,
            UpdateMetierSecurityService updateMetierSecurityService, @Value("${bim.perimeters:false}") boolean perimetersInstance) {
        this.applicationContext = applicationContext;
        this.rtCommitMetierHandler = rtCommitMetierHandler;
        this.objectMapper = objectMapper;
        this.updateMetierSecurityService = updateMetierSecurityService;
        this.perimetersInstance = perimetersInstance;

        this.commitAcqTimeoutMs = confRpc.getConnection().getCommitAcqTimeout();
        this.commitResultTimeoutMs = confRpc.getConnection().getCommitResultTimeout();
    }

    @EventListener(ContextRefreshedEvent.class)
    public void registerPerimeterComputers() {
        if (perimetersInstance) {
            Map<String, PerimeterComputer> beans = applicationContext.getBeansOfType(PerimeterComputer.class);
            beans.forEach((beanName, bean) -> {
                String typeOfClass = getModelClassHandled(bean);
                if (typeOfClass != null) {
                    perimeterComputers.put(typeOfClass, bean);
                    log.info("PerimeterComputer found {}<{}>", bean.getClass().getSimpleName(), typeOfClass);
                }
            });
        }
    }

    public String getModelClassHandled(PerimeterComputer bean) {
        // Si le bean est proxifié, on récupère sa classe cible effective.
        Class<?> beanClass = AopUtils.getTargetClass(bean);

        // On récupère le type générique de la classe parente
        Type genericSuper = beanClass.getGenericSuperclass();
        if (genericSuper instanceof ParameterizedType parameterizedType) {
            Type[] typeArguments = parameterizedType.getActualTypeArguments();
            if (typeArguments.length > 0) {
                Type businessType = typeArguments[0];
                // Vérification que le type est bien une instance de Class
                if (businessType instanceof Class<?> businessClass) {
                    return businessClass.getSimpleName();
                }
            }
        } else {
            log.error("Problem registering Bean {} : A Bean of type {} must be a parametrized one", beanClass, PerimeterComputer.class);
        }
        return null;
    }

    public String updatev2(CMRequestv2 updateMetierDto) {
        Boolean isAdmin = updateMetierSecurityService.checkSecurity(updateMetierDto);
        if (perimetersInstance) {
            handlePerimeters(updateMetierDto, isAdmin);
        }
        try {
            return handleCommitToServer(updateMetierDto);
        } catch (FunctionalException ex) {
            log.warn("Commit on server TR failed", ex);
            throw ex;
        } catch (Exception ex) {
            log.warn("Commit on server TR failed", ex);
            throw new TechnicalException(ex);
        }
    }

    public void handlePerimeters(CMRequestv2 cmRequestv2, Boolean isAdmin) {
        handlePerimetersOnUpdate(cmRequestv2.getCommitMetier().getUpd(), isAdmin, cmRequestv2);
        handlePerimetersOnCreate(cmRequestv2.getCommitMetier().getCre(), isAdmin, cmRequestv2);
    }

    private void handlePerimetersOnUpdate(List<EntityBody> entitiesToUpdate, Boolean isAdmin, CMRequestv2 request) {
        if (!isEmpty(entitiesToUpdate)) {
            entitiesToUpdate.forEach(entityBody -> {
                PerimeterComputer<?> computer = perimeterComputers.get(entityBody.getClasseMet());
                if (computer == null) {
                    removePerimetersIfAdmin(entityBody, isAdmin);
                } else {
                   computer.computeOnUpdate(request, entityBody);
                }
            });
        }
    }

    private void handlePerimetersOnCreate(List<EntityBody> entitiesToCreate, Boolean isAdmin, CMRequestv2 request) {
        if (!isEmpty(entitiesToCreate)) {
            entitiesToCreate.forEach(entityBody -> {
                PerimeterComputer<?> computer = perimeterComputers.get(entityBody.getClasseMet());
                if (computer == null) {
                    removePerimetersIfAdmin(entityBody, isAdmin);
                } else {
                    entityBody.getContent().put(DB_REALTIME_PERIMETERS, computer.computeOnCreate(request, entityBody));
                }
            });
        }
    }

    private void removePerimetersIfAdmin(EntityBody entity, boolean isAdmin) {
        if (!isAdmin) {
            entity.getContent().remove(DB_REALTIME_PERIMETERS);
        }
    }

    @SneakyThrows
    private String handleCommitToServer(CMRequestv2 commitMetierJson) {
        // handlerLdap modify the commit so it is necessary to send commitMetierJson to server and not updateMetier
        String uuid = randomUUID().toString();
        CommitMetierRequest.Builder builder = CommitMetierRequest.newBuilder();
        builder.setUuid(uuid);
        builder.setContent(objectMapper.writeValueAsString(commitMetierJson));
        CommitMetierRequest commitMetierRequest = builder.build();
        log.debug("Request - CommitMetier : {}", () -> StringUtils.normalizeSpace(commitMetierRequest.toString()));

        // Synchronous send of the request to TR
        CommitMetierExchange commitMetierExchange = rtCommitMetierHandler.addRequest(commitMetierRequest);

        long start = Instant.now().toEpochMilli();
        // Waiting for acq
        try {
            synchronized (commitMetierExchange.getSynchroAcq()) {
                while (commitMetierExchange.getResponseAcq() == null && commitMetierExchange.getError() == null && (Instant.now().toEpochMilli() - start) < commitAcqTimeoutMs) {
                    // TODO : gestion du timeout parametre dans la conf + par l'appelant => .wait(tiemout)
                    commitMetierExchange.getSynchroAcq().wait(commitAcqTimeoutMs);
                }
            }
            if (commitMetierExchange.getError() != null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new FunctionalException("Failed to be processed : " + commitMetierExchange.getError());
            }
            if (commitMetierExchange.getResponseAcq() == null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new TimeoutException("UpdateMetier ACQ timeout");
            }
        } catch (InterruptedException e) {
            rtCommitMetierHandler.purgeRequest(uuid);
            throw new TechnicalException("Waiting during CommitMetier Acq Response has been interrupted", e);
        }

        // Waiting for the TR compute result
        try {
            synchronized (commitMetierExchange.getSynchroResult()) {
                while (commitMetierExchange.getResponseResult() == null && commitMetierExchange.getError() == null && (Instant.now()
                                                                                                                              .toEpochMilli() - start) < commitResultTimeoutMs) {
                    // TODO : gestion du timeout parametre dans la conf + par l'appelant => .wait(tiemout)
                    commitMetierExchange.getSynchroResult().wait(commitResultTimeoutMs);
                }
            }
            if (commitMetierExchange.getError() != null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new FunctionalException("Failed to be processed : " + commitMetierExchange.getError());
            }
            if (commitMetierExchange.getResponseResult() == null) {
                rtCommitMetierHandler.purgeRequest(uuid);
                throw new TimeoutException("CommitMetier Result timeout");
            }
        } catch (InterruptedException e) {
            rtCommitMetierHandler.purgeRequest(uuid);
            throw new TechnicalException("Waiting during CommitMetier Result Response has been interrupted", e);
        }

        String result = objectMapper.writeValueAsString(commitMetierExchange.getResponseResult().getResult());
        rtCommitMetierHandler.purgeRequest(uuid);
        return result;
    }

}

