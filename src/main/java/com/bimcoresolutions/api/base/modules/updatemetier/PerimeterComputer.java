package com.bimcoresolutions.api.base.modules.updatemetier;

import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.CMRequestv2;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.EntityBody;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.perimeters.CMPerimeters;
import lombok.Data;
import lombok.extern.log4j.Log4j2;

import java.util.List;
import java.util.Set;

@Log4j2
@Data
public abstract class PerimeterComputer<T extends Model> {

    public Set<Integer> computeOnCreate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        log.info("Rule on create not implemented yet for class {}<{}>", this.getClass(), entityBody.getClasseMet());
        return Set.of();
    }

    public void computeOnUpdate(CMRequestv2 cmRequestv2, EntityBody entityBody) {
        log.info("Rule on update not implemented yet for class {}<{}>", this.getClass(), entityBody.getClasseMet());
    }

    public void computeOnUpdatePerimeter(CMPerimeters cmPerimeters, List<String> ids) {
        log.info("Rule on update perimeters not implemented yet for class {}", this.getClass());
    }

}