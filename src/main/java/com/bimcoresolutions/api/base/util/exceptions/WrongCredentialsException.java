package com.bimcoresolutions.api.base.util.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(value = HttpStatus.UNAUTHORIZED)
public class WrongCredentialsException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * Constructeur de l'objet.
     */
    public WrongCredentialsException() {
        super();
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     */
    public WrongCredentialsException(String pMessage) {
        super(pMessage);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     */
    public WrongCredentialsException(Throwable pCause) {
        super(pCause);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     */
    public WrongCredentialsException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
    }
}