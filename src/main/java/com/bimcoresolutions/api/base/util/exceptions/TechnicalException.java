package com.bimcoresolutions.api.base.util.exceptions;

public class TechnicalException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private final boolean withoutStack;

    public boolean isWithoutStack() {
        return withoutStack;
    }

    /**
     * Constructeur de l'objet.
     */
    public TechnicalException() {
        super();
        this.withoutStack = false;
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     */
    public TechnicalException(String pMessage) {
        super(pMessage);
        this.withoutStack = false;
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     */
    public TechnicalException(Throwable pCause) {
        super(pCause);
        this.withoutStack = false;
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     */
    public TechnicalException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
        this.withoutStack = false;
    }

    /**
     * Constructeur de l'objet.
     *
     * @param withoutStack
     */
    public TechnicalException(boolean withoutStack) {
        super();
        this.withoutStack = withoutStack;
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param withoutStack
     */
    public TechnicalException(String pMessage, boolean withoutStack) {
        super(pMessage);
        this.withoutStack = withoutStack;
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     * @param withoutStack
     */
    public TechnicalException(Throwable pCause, boolean withoutStack) {
        super(pCause);
        this.withoutStack = withoutStack;
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     * @param withoutStack
     */
    public TechnicalException(String pMessage, Throwable pCause, boolean withoutStack) {
        super(pMessage, pCause);
        this.withoutStack = withoutStack;
    }
}