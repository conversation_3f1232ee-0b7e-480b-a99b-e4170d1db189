package com.bimcoresolutions.api.base.util.exceptions;

public class WrongArgumentValueException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * Constructeur de l'objet.
     */
    public WrongArgumentValueException() {
        super();
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     */
    public WrongArgumentValueException(String pMessage) {
        super(pMessage);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     */
    public WrongArgumentValueException(Throwable pCause) {
        super(pCause);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     */
    public WrongArgumentValueException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
    }
}