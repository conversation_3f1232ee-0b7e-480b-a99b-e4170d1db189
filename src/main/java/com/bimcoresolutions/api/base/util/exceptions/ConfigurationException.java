package com.bimcoresolutions.api.base.util.exceptions;

public class ConfigurationException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * Constructeur de l'objet.
     */
    public ConfigurationException() {
        super();
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     */
    public ConfigurationException(String pMessage) {
        super(pMessage);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     */
    public ConfigurationException(Throwable pCause) {
        super(pCause);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     */
    public ConfigurationException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
    }
}