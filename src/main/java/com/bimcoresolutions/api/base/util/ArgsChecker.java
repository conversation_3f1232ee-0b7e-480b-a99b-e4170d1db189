/**
 *
 */
package com.bimcoresolutions.api.base.util;

import org.reflections.ReflectionUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class ArgsChecker {
    private Set<String> validArgs;

    public Set<String> getInvalidArgs(Class<?> clazz, final Set<String> args) {
        Set<String> invalidArgs = new HashSet<>(args);
        validArgs = ReflectionUtils.getAllFields(clazz).stream().map(Field::getName).collect(Collectors.toSet());
        validArgs.add("limit");
        validArgs.add("offset");
        validArgs.add("order");
        validArgs.add("orderby");
        validArgs.add("pattern");
        validArgs.add("isnull");
        validArgs.add("isnotnull");
        invalidArgs.removeAll(validArgs);
        return invalidArgs;
    }
}