package com.bimcoresolutions.api.base.util.models;

import java.time.Instant;

public class ExceptionResponseMessage {

    private Instant instant;
    private String error;
    private String message;

    public ExceptionResponseMessage() {
    }

    public ExceptionResponseMessage(String error, String message) {
        this.instant = Instant.now();
        this.error = error;
        this.message = message;
    }



    public ExceptionResponseMessage(Instant instant, String error, String message) {
        this.instant = instant;
        this.error = error;
        this.message = message;
    }

    public Instant getInstant() {
        return instant;
    }

    public void setInstant(Instant instant) {
        this.instant = instant;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}