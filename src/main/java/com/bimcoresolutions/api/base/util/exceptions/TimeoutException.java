package com.bimcoresolutions.api.base.util.exceptions;

public class TimeoutException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * Constructeur de l'objet.
     */
    public TimeoutException() {
        super();
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     */
    public TimeoutException(String pMessage) {
        super(pMessage);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     */
    public TimeoutException(Throwable pCause) {
        super(pCause);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     */
    public TimeoutException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
    }
}