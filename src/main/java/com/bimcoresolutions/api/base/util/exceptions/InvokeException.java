package com.bimcoresolutions.api.base.util.exceptions;

public class InvokeException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * Constructeur de l'objet.
     */
    public InvokeException() {
        super();
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     */
    public InvokeException(String pMessage) {
        super(pMessage);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     */
    public InvokeException(Throwable pCause) {
        super(pCause);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     */
    public InvokeException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
    }
}