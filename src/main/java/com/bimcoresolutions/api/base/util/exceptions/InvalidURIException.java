package com.bimcoresolutions.api.base.util.exceptions;

public class InvalidURIException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * Constructeur de l'objet.
     */
    public InvalidURIException() {
        super("No Entity Found");
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     */
    public InvalidURIException(String pMessage) {
        super(pMessage);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pCause
     */
    public InvalidURIException(Throwable pCause) {
        super(pCause);
    }

    /**
     * Constructeur de l'objet.
     *
     * @param pMessage
     * @param pCause
     */
    public InvalidURIException(String pMessage, Throwable pCause) {
        super(pMessage, pCause);
    }
}