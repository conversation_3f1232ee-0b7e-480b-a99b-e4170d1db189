package com.bimcoresolutions.api.base;

import com.bimcoresolutions.api.base.security.keycloack.KeycloakProperties;
import com.bimcoresolutions.api.base.util.initSpecifique;
import lombok.extern.log4j.Log4j2;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import java.sql.DriverManager;
import java.util.TimeZone;

@Log4j2
@EntityScan({ "com.bimcoresolutions", "com.bimcitysolutions" })
@ComponentScan({ "com.bimcoresolutions", "com.bimcitysolutions" })
@SpringBootApplication
@EnableCaching
@EnableConfigurationProperties({ KeycloakSpringBootProperties.class, KeycloakProperties.class })
@ConfigurationPropertiesScan({ "com.bimcoresolutions", "com.bimcitysolutions" })
public class ApiServerApplication {

    public static void main(String[] args) {
        log.info("> Starting Server <");
        TimeZone.setDefault(TimeZone.getTimeZone("Etc/UTC"));
        DriverManager.setLoginTimeout(5);
        SpringApplication.run(ApiServerApplication.class, args);
    }

    /**
     * créer un init spécifique. La methode doit être non bloquante
     */
    @EventListener(ContextRefreshedEvent.class)
    @initSpecifique
    public void initSpecifique() {
        log.info("initSpecifique start...");
        // TODO Auto-generated method stub
        log.info("initSpecifique ok");
    }

}
