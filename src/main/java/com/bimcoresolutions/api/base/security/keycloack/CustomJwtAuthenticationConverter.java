package com.bimcoresolutions.api.base.security.keycloack;

import com.bimcoresolutions.api.base.util.exceptions.WrongPerimetersException;
import com.bimcoresolutions.util.base.filter.IntArrayFilter;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.collections4.CollectionUtils.isEmpty;

@Log4j2
@Component
public class CustomJwtAuthenticationConverter implements Converter<Jwt, AbstractAuthenticationToken> {

    private final List<String> backClients;
    private final Function<String, SimpleGrantedAuthority> toRole = role -> new SimpleGrantedAuthority("ROLE_" + role);
    private final PermissionsTokenDecoder permissionsTokenDecoder;
    private final PermissionsMatrixDecoder permissionsMatrixDecoder;
    @Getter
    ThreadLocal<Pair<Boolean, List<Integer>>> _perimeters = new ThreadLocal<>(); // à gauche isAdmin, à droite les perimeters

    public CustomJwtAuthenticationConverter(@Value("${bim.back-clients:}") List<String> backClients, PermissionsTokenDecoder permissionsTokenDecoder,
            PermissionsMatrixDecoder permissionsMatrixDecoder) {
        this.backClients = backClients;
        if (isEmpty(backClients)) {
            log.warn("No bim.back-clients given, as a consequence : No keycloak client registered as admin");
        } else {
            log.info("bim.back-clients given : {}", backClients);
        }
        this.permissionsTokenDecoder = permissionsTokenDecoder;
        this.permissionsMatrixDecoder = permissionsMatrixDecoder;
    }

    @Override
    public AbstractAuthenticationToken convert(@NonNull Jwt authenticationToken) {
        Collection<GrantedAuthority> authorities = new ArrayList<>();

        // Dans le cas d'un microservice back
        if (isFromBackClient(authenticationToken)) {
            authorities.add(new SimpleGrantedAuthority("ROLE_admin"));
            _perimeters.set(Pair.of(true, null));
            return new JwtAuthenticationToken(authenticationToken, authorities);

        }

        // Récupérer le Token de permissions du header 'Permissions'
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String permissionsToken = request.getHeader("Permissions");

            if (permissionsToken != null) {
                try {
                    // Récupérer les permissions (roles, etc.) du deuxième Token
                    Jwt decodedPermissionsToken = permissionsTokenDecoder.decode(permissionsToken);
                    authorities.addAll(extractAuthoritiesFromPermissionsToken(decodedPermissionsToken));
                } catch (JwtException e) {
                    throw new JwtException("Invalid Permissions token", e);
                }
            }
        }

        return new JwtAuthenticationToken(authenticationToken, authorities);
    }

    private boolean isFromBackClient(Jwt authenticationToken) {
        return backClients.contains((String) authenticationToken.getClaim("azp"));
    }

    // Extraire les roles du deuxième 'Token' de permissions
    private Collection<GrantedAuthority> extractAuthoritiesFromPermissionsToken(Jwt permissionsToken) {
        var token = new PermissionsToken(permissionsToken);
        Set<GrantedAuthority> authorities = new HashSet<>();
        Map<String, Object> bimClaim = token.getBimClaim();

        if (!bimClaim.isEmpty()) {
            // Récupérer les roles au niveau du marché
            List<String> markets = token.getMarketRoles(bimClaim);
            markets.stream()
                    .map(market -> new SimpleGrantedAuthority("ROLE_" + market)).forEach(authorities::add);

            // Définirs les droits des administrateur
            if(markets.contains("admin")){
                _perimeters.set(Pair.of(true, token.getPerimeters(bimClaim)));
            }else{
                _perimeters.set(Pair.of(false, token.getPerimeters(bimClaim)));
            }

            // Extraire la matrice de permissions du role
            Map<String, Object> permsMatrix = token.getRolePermissionsMatrix(bimClaim);
            Set<String> permissions = permissionsMatrixDecoder.extractPermissions(permsMatrix);

            Set<SimpleGrantedAuthority> permsAuthorities = permissions.stream()
                    .map(toRole)
                    .collect(Collectors.<SimpleGrantedAuthority>toSet());
            authorities.addAll(permsAuthorities);
        }
        return authorities;
    }

    public static IntArrayFilter comparePerimeters(String className, IntArrayFilter criteria, Pair<Boolean, List<Integer>> userPerimeter, boolean isPerimetersInstance,List<String> classMetierHP) {
        boolean isAdmin = userPerimeter.getLeft();
        if (classMetierHP.stream().anyMatch(className::equals)) {
            return null;
        }
        //Cas perimeter instance ON/OFF
        if (!isPerimetersInstance) {
            return null;
        }

        Pair<String, Collection<Integer>> perimeters = involvedPerimeters(criteria);

        // Cas admin
        if (isAdmin) {
            boolean noRequestedPerimeters = perimeters.getRight() == null || perimeters.getRight().isEmpty();
            boolean noRequestedType = perimeters.getLeft() == null;

            if (noRequestedPerimeters && noRequestedType) {
                return null;
            }
            return setType(perimeters.getLeft(), perimeters.getRight());
        }

        // Cas non-admin
        List<Integer> userPerimeters = userPerimeter.getRight();
        Collection<Integer> requestPerimeters = perimeters.getRight();
        log.info("userPerimeters : {} requestPerimeters :{}", userPerimeters, requestPerimeters);

        if (userPerimeters == null || userPerimeters.isEmpty()) {
            log.info("fonction userPerimeters == null || userPerimeters.isEmpty() userPerimeters : {} requestPerimeters :{}", userPerimeters, requestPerimeters);
            throw new WrongPerimetersException("Périmètre utilisateur inexistant (vide)");
        }

        if (requestPerimeters == null || requestPerimeters.isEmpty()) {
            return setType("in", userPerimeters);
        }

        if (new HashSet<>(userPerimeters).containsAll(requestPerimeters)) {
            log.info("FonctionSetype userPerimeters : {} requestPerimeters :{}", userPerimeters, requestPerimeters);
            return setType(perimeters.getLeft(), requestPerimeters);
        }
        throw new WrongPerimetersException("Impossible d'atteindre un périmètre non possédé");
    }

    public static IntArrayFilter setType(String type, Collection<Integer> perimeters) {
        IntArrayFilter perimetersFilter = new IntArrayFilter();
        switch (type) {
            case "equals":
                perimetersFilter.setEquals(new ArrayList<>(perimeters));
                break;
            case "notEquals":
                perimetersFilter.setNotEquals(new ArrayList<>(perimeters));
                break;
            case "in":
                perimetersFilter.setIn(new ArrayList<>(perimeters));
                break;
            case "notIn":
                perimetersFilter.setNotIn(new ArrayList<>(perimeters));
                break;
            case "contains":
                perimetersFilter.setContains(new ArrayList<>(perimeters));
                break;
        }
        return perimetersFilter;
    }

    public static Pair<String, Collection<Integer>> involvedPerimeters(IntArrayFilter criteria) {
        if (criteria == null) {
            return Pair.of(null, Collections.emptyList());
        }
        if (criteria.getEquals() != null) return Pair.of("equals", criteria.getEquals());
        if (criteria.getNotEquals() != null) return Pair.of("notEquals", criteria.getNotEquals());
        if (criteria.getIn() != null) return Pair.of("in", criteria.getIn());
        if (criteria.getNotIn() != null) return Pair.of("notIn", criteria.getNotIn());
        if (criteria.getContains() != null) return Pair.of("contains", criteria.getContains());
        return Pair.of(null, Collections.emptyList());
    }

}

