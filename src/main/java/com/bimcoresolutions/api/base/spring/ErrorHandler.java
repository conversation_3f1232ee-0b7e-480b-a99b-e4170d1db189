package com.bimcoresolutions.api.base.spring;

import com.bimcoresolutions.api.base.util.exceptions.*;
import com.bimcoresolutions.api.base.util.models.ExceptionResponseMessage;
import com.bimcoresolutions.util.base.FunctionalException;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import io.grpc.StatusRuntimeException;
import lombok.extern.log4j.Log4j2;
import org.springframework.core.annotation.Order;
import org.springframework.http.*;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.lang.reflect.UndeclaredThrowableException;

@Log4j2
@ControllerAdvice
@Order
public class ErrorHandler extends ResponseEntityExceptionHandler {

    // Spring internal exception handling

    protected ResponseEntity<Object> handleExceptionInternal(Exception ex, Object body, HttpHeaders headers, HttpStatus status, WebRequest request) {
        log.error("", ex);
        ExceptionResponseMessage erm = new ExceptionResponseMessage(ex.getClass().getName(), ex.getLocalizedMessage());
        return new ResponseEntity(erm, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.warn(ex);
        ExceptionResponseMessage erm = new ExceptionResponseMessage(ex.getClass().getName(), ex.getLocalizedMessage());
        return new ResponseEntity(erm, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleBindException(BindException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.warn(ex);
        ExceptionResponseMessage erm = new ExceptionResponseMessage(ex.getClass().getName(), ex.getLocalizedMessage());
        return new ResponseEntity(erm, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.warn(ex);
        ExceptionResponseMessage erm = new ExceptionResponseMessage(ex.getClass().getName(), ex.getLocalizedMessage());
        return new ResponseEntity(erm, headers, status);
    }

    // Custom Exception handling

    @ExceptionHandler(NoEntityFoundException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void handleNoEntityFound(NoEntityFoundException ex, WebRequest request) {
        log.warn(ex.getLocalizedMessage());
    }

    @ExceptionHandler(InvalidURIException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.NOT_FOUND)
    public ExceptionResponseMessage handleInvalidURIException(InvalidURIException ex, WebRequest request) {
        log.warn("", ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(WrongCredentialsException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    public ExceptionResponseMessage handleWrongCredentials(WrongCredentialsException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(WrongPerimetersException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.FORBIDDEN)
    public ExceptionResponseMessage handleWrongCredentials(WrongPerimetersException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(UnauthorizedException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    public ExceptionResponseMessage handleUnauthorized(UnauthorizedException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(AccessDeniedException.class)
    @ResponseBody
    @ResponseStatus(value = HttpStatus.UNAUTHORIZED)
    public ExceptionResponseMessage handleUnauthorized(AccessDeniedException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(WrongArgumentValueException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResponseMessage handleWrongArgumentValue(WrongArgumentValueException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(InvalidFormatException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResponseMessage handleInvalidFormat(InvalidFormatException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(PaginationRequestMalformedException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResponseMessage handlePaginationRequestMalformed(PaginationRequestMalformedException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(IllegaURIException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResponseMessage handleIllegalURI(IllegaURIException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResponseMessage handleIllegalArgument(IllegalArgumentException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(FunctionalException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ExceptionResponseMessage handleFunctional(FunctionalException ex, WebRequest request) {
        log.warn(ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(TimeoutException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.GATEWAY_TIMEOUT)
    public ExceptionResponseMessage handleTimeout(TimeoutException ex, WebRequest request) {
        log.warn(ex.getLocalizedMessage());
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(StatusRuntimeException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.GATEWAY_TIMEOUT)
    public ExceptionResponseMessage handleStatusRuntime(StatusRuntimeException ex, WebRequest request) {
        log.warn(ex.getLocalizedMessage());
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(InvokeException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionResponseMessage handleinvoke(InvokeException ex, WebRequest request) {
        if (log.isDebugEnabled()) {
            log.error("", ex.getCause().getCause());
        } else {
            log.error(ex.getCause().getCause().getLocalizedMessage());
        }
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getCause().getCause().getLocalizedMessage());
    }

    @ExceptionHandler(UndeclaredThrowableException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionResponseMessage handleUndeclaredThrowable(UndeclaredThrowableException ex, WebRequest request) {
        log.error("", ex.getCause());
        return new ExceptionResponseMessage(ex.getCause().getClass().getSimpleName(), ex.getCause().getLocalizedMessage());
    }

    @ExceptionHandler(TechnicalException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionResponseMessage handleTechnical(TechnicalException ex, WebRequest request) {
        if (ex.isWithoutStack()) {
            log.error(ex.getLocalizedMessage());
        } else {
            log.error("", ex);
        }
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(NullPointerException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionResponseMessage handleNullPointer(NullPointerException ex, WebRequest request) {
        log.error("", ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(RuntimeException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionResponseMessage handleRuntime(RuntimeException ex, WebRequest request) {
        log.error("", ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ExceptionResponseMessage handle(Exception ex, WebRequest request) {
        log.error("", ex);
        return new ExceptionResponseMessage(ex.getClass().getSimpleName(), ex.getLocalizedMessage());
    }

}
