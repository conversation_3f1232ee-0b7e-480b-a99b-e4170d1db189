package com.bimcoresolutions.api.base.configuration;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "database.settings")
@Data
public class ConfigSettings {

    private String database;
    private String username;
    private String password;
    private String address;
    private Integer port;

    public String getUrl() {
        return "jdbc:postgresql://" + this.address + ":" + this.port + "/" + this.database;
    }

}
