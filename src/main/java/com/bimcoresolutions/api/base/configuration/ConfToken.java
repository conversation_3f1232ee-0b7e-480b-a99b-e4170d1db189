package com.bimcoresolutions.api.base.configuration;

import com.bimcoresolutions.util.base.configuration.base.AConfiguration;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;
import java.util.UUID;

@Log4j2
@Data
@ConfigurationProperties(prefix = "bim.token")
public class ConfToken extends AConfiguration {

    public static final List<ConfPermission> DEFAULT_CONF_PERMISSION = List.of(new ConfPermission(ConfPermission.PERMISSION_KEY_ALL_CLASSMETIER, ConfPermission.EPermissionType.READ_WRITE));

    private String secret;
    private String admin;
    private List<ConfPermission> permission = DEFAULT_CONF_PERMISSION;

    @Override
    public boolean validate() {
        if (secret == null) {
            log.warn("Field 'token.secret' not given, secret used to generate jwt tokens is randomly generated");
            secret = UUID.randomUUID().toString();
        }
        return true;
    }

}
