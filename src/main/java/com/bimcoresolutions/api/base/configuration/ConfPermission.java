package com.bimcoresolutions.api.base.configuration;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
public class ConfPermission {

    public static final String PERMISSION_KEY_ALL_CLASSMETIER = "allClassMetier";

    private String key = "";
    private String name = "";
    private String group = "";
    private EPermissionType permission;

    public ConfPermission() {
    }

    public ConfPermission(String key, EPermissionType permission) {
        this.key = key;
        this.permission = permission;
    }

    public ConfPermission(String key, String name, EPermissionType permission) {
        this.key = key;
        this.name = name;
        this.permission = permission;
    }

    public ConfPermission(String key, String name, String group, EPermissionType permission) {
        this.key = key;
        this.name = name;
        this.group = group;
        this.permission = permission;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public EPermissionType getPermission() {
        return permission;
    }

    public void setPermission(EPermissionType permission) {
        this.permission = permission;
    }

    public enum EPermissionType {
        // @JsonProperty is for Jackson
        // @SerializedName is for Gson
        @JsonProperty(PermissionType.PERMISSION_TYPE_READ_WRITE) @SerializedName(PermissionType.PERMISSION_TYPE_READ_WRITE) READ_WRITE,
        @JsonProperty(PermissionType.PERMISSION_TYPE_READ) @SerializedName(PermissionType.PERMISSION_TYPE_READ) READ,
        @JsonProperty(PermissionType.PERMISSION_TYPE_WRITE) @SerializedName(PermissionType.PERMISSION_TYPE_WRITE) WRITE,
        @JsonProperty(PermissionType.PERMISSION_TYPE_NOACCES) @SerializedName(PermissionType.PERMISSION_TYPE_NOACCES) NOACCES

    }

    public static class PermissionType {
        public static final String PERMISSION_TYPE_READ_WRITE = "readWrite";
        public static final String PERMISSION_TYPE_READ = "read";
        public static final String PERMISSION_TYPE_WRITE = "write";
        public static final String PERMISSION_TYPE_NOACCES = "noacces";
    }
}
