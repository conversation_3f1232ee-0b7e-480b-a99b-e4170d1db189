package com.bimcoresolutions.api.base.configuration;

import com.bimcoresolutions.util.base.configuration.base.AConfiguration;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

@Log4j2
@EqualsAndHashCode(callSuper = true)
@Data
@Configuration
public class ConfigGrpc extends AConfiguration {

    public static final Long DEFAULT_TIMEOUT_ACQ_MILLI = 5000L;
    public static final Long DEFAULT_TIMEOUT_RESULT_MILLI = 30000L;
    public static final String GRPC_RESSOURCE_IDS = "id_metier";
    public static final String GRPC_RESSOURCE_COMAND = "comand";
    public static final String GRPC_RESSOURCE_COMMIT = "commit";
    public static final String GRPC_RESSOURCE_COMMIT_RESULT = "commit_result";

    private final ConfigurationClientGrpcApi connection;

    public ConfigGrpc() {
        this.connection = new ConfigurationClientGrpcApi(DEFAULT_TIMEOUT_ACQ_MILLI, DEFAULT_TIMEOUT_RESULT_MILLI);
    }

    @Autowired
    public ConfigGrpc(ConfigurationClientGrpcApi connection) {
        try {
            if (connection.getCommitAcqTimeout() != null) {
                log.info("commitAcqTimeout set to : {} ms", connection.getCommitAcqTimeout());
            } else {
                log.info("No valid custom commitAcqTimeout found in conf (bim.grpc.connection.commitAcqTimeout), falling back to default timeout : {}ms",
                        DEFAULT_TIMEOUT_ACQ_MILLI);
                connection.setCommitAcqTimeout(DEFAULT_TIMEOUT_ACQ_MILLI);
            }
        } catch (Exception e) {
            log.info("No valid custom commitAcqTimeout found in conf (bim.grpc.connection.commitAcqTimeout), falling back to default timeout : {}ms", DEFAULT_TIMEOUT_ACQ_MILLI);
            connection.setCommitAcqTimeout(DEFAULT_TIMEOUT_ACQ_MILLI);
        }

        try {
            if (connection.getCommitResultTimeout() != null) {
                log.info("commitResultTimeout set to : {} ms", connection.getCommitResultTimeout());
            } else {
                log.info("No valid custom commitResultTimeout found in conf (bim.grpc.connection.commitResultTimeout), falling back to default timeout : {}ms",
                        DEFAULT_TIMEOUT_RESULT_MILLI);
                connection.setCommitResultTimeout(DEFAULT_TIMEOUT_RESULT_MILLI);
            }
        } catch (Exception e) {
            log.info("No valid custom commitResultTimeout found in conf (bim.grpc.connection.commitResultTimeout), falling back to default timeout : {}ms",
                    DEFAULT_TIMEOUT_RESULT_MILLI);
            connection.setCommitResultTimeout(DEFAULT_TIMEOUT_RESULT_MILLI);
        }

        this.connection = connection;
    }

    @Override
    public boolean validate() {
        return connection.validate();
    }

}
