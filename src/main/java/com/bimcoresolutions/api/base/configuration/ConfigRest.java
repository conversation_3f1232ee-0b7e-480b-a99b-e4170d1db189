package com.bimcoresolutions.api.base.configuration;

import com.bimcoresolutions.api.base.util.exceptions.ConfigurationException;
import com.bimcoresolutions.util.base.configuration.base.AConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import javax.annotation.PostConstruct;
import java.util.List;

@ConfigurationProperties(prefix = "bim.rest")
public class ConfigRest extends AConfiguration {

    private List<String> allowedOrigins;
    private List<String> allowedMethods;
    private List<String> allowedHeaders;

    public List<String> getAllowedOrigins() {
        return allowedOrigins;
    }

    public void setAllowedOrigins(List<String> allowedOrigins) {
        this.allowedOrigins = allowedOrigins;
    }

    public List<String> getAllowedMethods() {
        return allowedMethods;
    }

    public void setAllowedMethods(List<String> allowedMethods) {
        this.allowedMethods = allowedMethods;
    }

    public List<String> getAllowedHeaders() {
        return allowedHeaders;
    }

    public void setAllowedHeaders(List<String> allowedHeaders) {
        this.allowedHeaders = allowedHeaders;
    }

    @Override
    @PostConstruct
    public boolean validate() {
        if (allowedOrigins == null || allowedOrigins.isEmpty()) {
            throw new ConfigurationException("Field 'rest.allowedOrigins' must not be null or empty.");
        }
        if (allowedMethods == null || allowedMethods.isEmpty()) {
            throw new ConfigurationException("Field 'rest.allowedMethods' must not be null or empty.");
        }
        if (allowedHeaders == null || allowedHeaders.isEmpty()) {
            throw new ConfigurationException("Field 'rest.allowedHeaders' must not be null or empty.");
        }
        return true;
    }
}
