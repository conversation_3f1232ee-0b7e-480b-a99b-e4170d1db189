package com.bimcoresolutions.api.base.configuration;

public final class DefaultParametersValues {

    private DefaultParametersValues() {
        throw new IllegalStateException("Utility class");
    }

    public static final int PARAM_CON_ISVALID_TIMEOUT = 10;
    public static final int PARAM_LOGIN_TIMEOUT = 2;

    public static final int PARAM_PRIORITY_COMMITMETIER = 6;
    public static final int PARAM_PRIORITY_INITNOTIF = 4;

    public static final int PARAM_INPUT_N0N1_MAXSIZE = 5000;
    public static final int PARAM_OUTPUT_N0N1_MAXSIZE = 5000;
    public static final int PARAM_INPUT_N1N2_MAXSIZE = 5000;
    public static final int PARAM_OUTPUT_N1N2_MAXSIZE = 5000;

    public static final int PARAM_INPUT_N0N1_MINSIZE = 10;
    public static final int PARAM_OUTPUT_N0N1_MINSIZE = 10;
    public static final int PARAM_INPUT_N1N2_MINSIZE = 10;
    public static final int PARAM_OUTPUT_N1N2_MINSIZE = 10;

    public static final int DELAY_10 = 10;
    public static final int DELAY_1000 = 1000;
}
