package com.bimcoresolutions.api.base.configuration;


import com.bimcoresolutions.util.base.filter.SpecificationBuilder;
import com.bimcoresolutions.util.base.sql.QueryFormer;
import com.bimcoresolutions.util.base.sql.enumerations.EDatabaseType;
import com.bimcoresolutions.util.model.conf.ConfigHisto;
import com.bimcoresolutions.util.model.conf.ConfigRealtime;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

import static com.bimcoresolutions.util.model.service.histo.HistoManager.SCHEMA_HISTO;
import static com.bimcoresolutions.util.model.service.realtime.RealTimeManager.SCHEMA_RT;

@Configuration
public class Datasources {

    private static final int MAXIMUM_POOL_SIZE_REALTIME = 30;
    private static final int SOCKET_TIMEOUT_REALTIME = 60;
    private static final int MAXIMUM_POOL_SIZE_HISTO = 20;
    private static final int SOCKET_TIMEOUT_HISTO = 60;
    private static final int MAXIMUM_POOL_SIZE_SETTINGS = 10;
    private static final int SOCKET_TIMEOUT_SETTINGS = 60;
    private static final String SCHEMA_SETTINGS = "settings";

    @Bean(name = "dsRealtime")
    @Primary
    public DataSource dataSourceRealtime(ConfigRealtime confApiRealtime) {
        // Datasource realtime
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(org.postgresql.Driver.class.getName());
        dataSource.setJdbcUrl(confApiRealtime.getUrl());
        dataSource.setUsername(confApiRealtime.getUserName());
        dataSource.setPassword(confApiRealtime.getPassword());
        dataSource.setMinimumIdle(2);
        dataSource.setMaximumPoolSize(MAXIMUM_POOL_SIZE_REALTIME);
        dataSource.addDataSourceProperty("socketTimeout", SOCKET_TIMEOUT_REALTIME);
        return dataSource;
    }

    @Bean(name = "dsHisto")
    public DataSource dataSourceHisto(ConfigHisto confApiHisto) {
        // Datasource realtime
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(org.postgresql.Driver.class.getName());
        dataSource.setJdbcUrl(confApiHisto.getUrl());
        dataSource.setUsername(confApiHisto.getUsername());
        dataSource.setPassword(confApiHisto.getPassword());
        dataSource.setMinimumIdle(2);
        dataSource.setMaximumPoolSize(MAXIMUM_POOL_SIZE_HISTO);
        dataSource.addDataSourceProperty("socketTimeout", SOCKET_TIMEOUT_HISTO);
        return dataSource;
    }

    @Bean(name = "dsSettings")
    public DataSource dataSourceSettings(ConfigSettings configSettings) {
        // Datasource settings
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(org.postgresql.Driver.class.getName());
        dataSource.setJdbcUrl(configSettings.getUrl());
        dataSource.setUsername(configSettings.getUsername());
        dataSource.setPassword(configSettings.getPassword());
        dataSource.setMinimumIdle(1);
        dataSource.setMaximumPoolSize(MAXIMUM_POOL_SIZE_SETTINGS);
        dataSource.addDataSourceProperty("socketTimeout", SOCKET_TIMEOUT_SETTINGS);
        return dataSource;
    }

    @Bean(name = "qfRealtime")
    @Primary
    public QueryFormer queryFormerRealtime() {
        return new QueryFormer(EDatabaseType.POSTGRESQL, SCHEMA_RT);
    }

    @Bean(name = "qfHisto")
    public QueryFormer queryFormerHisto() {
        return new QueryFormer(EDatabaseType.POSTGRESQL, SCHEMA_HISTO);
    }

    @Bean(name = "qfSettings")
    public QueryFormer queryFormerSettings() {
        return new QueryFormer(EDatabaseType.POSTGRESQL, SCHEMA_SETTINGS);
    }

    @Bean(name = "specRealtime")
    @Primary
    public SpecificationBuilder specificationBuilderRealtime(@Qualifier("qfRealtime") QueryFormer queryFormer) {
        return new SpecificationBuilder(queryFormer);
    }

    @Bean(name = "specHisto")
    public SpecificationBuilder specificationBuilderHisto(@Qualifier("qfHisto") QueryFormer queryFormer) {
        return new SpecificationBuilder(queryFormer);
    }

}
