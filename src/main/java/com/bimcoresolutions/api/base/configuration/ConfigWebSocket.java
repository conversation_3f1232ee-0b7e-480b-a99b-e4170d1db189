package com.bimcoresolutions.api.base.configuration;

import com.bimcoresolutions.api.base.util.exceptions.ConfigurationException;
import com.bimcoresolutions.util.base.configuration.base.AConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;


@ConfigurationProperties(prefix = "bim.restws")
public class ConfigWebSocket extends AConfiguration {

    private List<String> allowedOrigins;

    public List<String> getAllowedOrigins() {
        return allowedOrigins;
    }

    public void setAllowedOrigins(List<String> allowedOrigins) {
        this.allowedOrigins = allowedOrigins;
    }

    @Override
    public boolean validate() {
        if (allowedOrigins == null || allowedOrigins.isEmpty()) {
            throw new ConfigurationException("Field 'restws.allowedOrigins' must not be null or empty.");
        }
        return true;
    }

}
