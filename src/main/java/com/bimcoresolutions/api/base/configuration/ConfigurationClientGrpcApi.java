package com.bimcoresolutions.api.base.configuration;

import com.bimcoresolutions.util.base.configuration.base.AConfiguration;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@ConfigurationProperties("bim.grpc.connection")
public class ConfigurationClientGrpcApi extends AConfiguration {

    @JsonProperty(required = true)
    private String address;
    @JsonProperty(required = true)
    private int port;
    @JsonProperty(required = true)
    private String login;
    @JsonProperty(required = true)
    private String password;

    private Long commitAcqTimeout;
    private Long commitResultTimeout;

    public ConfigurationClientGrpcApi(Long commitAcqTimeout, Long commitResultTimeout) {
        this.commitAcqTimeout = commitAcqTimeout;
        this.commitResultTimeout = commitResultTimeout;
    }

    @Override
    public boolean validate() {
        return true;
    }

}
