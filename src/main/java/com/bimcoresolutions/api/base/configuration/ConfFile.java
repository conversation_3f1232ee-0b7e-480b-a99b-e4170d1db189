package com.bimcoresolutions.api.base.configuration;

import com.bimcoresolutions.util.base.configuration.base.AConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;


@ConfigurationProperties(prefix = "bim.file")
public class ConfFile extends AConfiguration {

    private String url = "";
    private String uploadDir = "/temp";

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUploadDir() {
        return uploadDir;
    }

    public void setUploadDir(String uploadDir) {
        this.uploadDir = uploadDir;
    }

    @Override
    public boolean validate() {
        return true;
    }

}
