package com.bimcoresolutions;

import com.bimcoresolutions.api.base.security.keycloack.conf.EntityMapping;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;

import java.util.List;

import static java.nio.charset.Charset.defaultCharset;
import static java.util.Objects.requireNonNull;

public abstract class BimTest {

    public static final EntityMapping ENTITY_MAPPING =
            new EntityMapping(
                    new EntityMapping.RoleMapping("Role", "code", "permission"),
                    new EntityMapping.UserMapping("User", "firstname", "lastname", "email", "active", "password", List.of("password"))
            );

    private final ObjectMapper objectMapper = JsonMapper.builder().findAndAddModules().build();

    @SneakyThrows
    protected <T> T fromFileToObject(final String fileName, Class<T> clazz) {
        return objectMapper.readValue(this.getClass().getResource(fileName), clazz);
    }

    @SneakyThrows
    protected String fromFileToString(final String fileName) {
        return IOUtils.toString(requireNonNull(this.getClass().getResource(fileName)), defaultCharset());
    }
}
