package com.bimcoresolutions.api.base.api;

import com.bimcoresolutions.util.base.model.util.enumeration.EBimCoreVariableType;
import com.bimcoresolutions.util.model.domain.structure.modeldefinition.*;
import com.vdurmont.semver4j.Semver;

import java.util.List;

import static java.util.stream.Collectors.joining;

public class Utils {
    public static final String ENTITY_NAME = "Camera";
    public static final String SECOND_ENTITY_NAME = "Alert";
    public static final String MODEL_NAME = "IAmTheModel";
    public static final String PACKAGE_NAME = "i.am.package.root";
    public static final Semver VERSION = new Semver("0.0.0");

    public static String trim(String result) {
        return result
                .lines()
                .map(it -> it.trim())
                .map(it -> it.replaceAll("\\r", ""))
                .collect(joining("\n"));
    }


    public static BimCoreModel generateBimModel() {
        BimCoreModel topModel = new BimCoreModel(MODEL_NAME);
        BimCoreModelElement bimCoreModelElement = new BimCoreModelElement(ENTITY_NAME, VERSION, false, "DynamicElementType", false);
        BimCoreModelVariable bimCoreModelElementGeometry = new BimCoreModelVariable("location", EBimCoreVariableType.GEOMETRY.getModelBimCoreType(), "default", false, false, false, "location", VERSION);
        BimCoreModelVariable bimCoreModelElementString = new BimCoreModelVariable("string", EBimCoreVariableType.STRING.getModelBimCoreType(), "default", false, false, false, "string", VERSION);
        BimCoreModelVariable bimCoreModelElementJson = new BimCoreModelVariable("presets", EBimCoreVariableType.JSON.getModelBimCoreType(), null, false, false, false, "presets", VERSION);
        BimCoreModelVariable bimCoreModelElementDate = new BimCoreModelVariable("date", EBimCoreVariableType.DATE.getModelBimCoreType(), null, false, false, false, "date", VERSION);
        bimCoreModelElement.getVariables().put("location", bimCoreModelElementGeometry);
        bimCoreModelElement.getVariables().put("string", bimCoreModelElementString);
        bimCoreModelElement.getVariables().put("presets", bimCoreModelElementJson);
        bimCoreModelElement.getVariables().put("date", bimCoreModelElementDate);
        topModel.getElementsBimCore().put(ENTITY_NAME, bimCoreModelElement);

        BimCoreModelElement secondBimCoreModelElement = new BimCoreModelElement(SECOND_ENTITY_NAME, VERSION, false, "DynamicElementType", false);
        BimCoreModelVariable secondBimCoreModelElementString = new BimCoreModelVariable("string", EBimCoreVariableType.STRING.getModelBimCoreType(), "default", false, false, false, "string", VERSION);
        secondBimCoreModelElement.getVariables().put("string", secondBimCoreModelElementString);
        topModel.getElementsBimCore().put(SECOND_ENTITY_NAME, secondBimCoreModelElement);

        BRelation bRelation = new BRelation(
                "Camera",
                "alerts",
                "Alert",
                "camera",
                ECardinality.CARD_1_N,
                List.of("comments"),
                "false"
        );
        topModel.getRelations().put("Camera_alerts", bRelation);

        topModel.validate();
        return topModel;
    }
}