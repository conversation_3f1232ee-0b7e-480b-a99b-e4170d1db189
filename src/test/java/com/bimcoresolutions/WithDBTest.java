package com.bimcoresolutions;


import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.BeforeAll;
import org.keycloak.adapters.springboot.KeycloakSpringBootProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@EnableConfigurationProperties(KeycloakSpringBootProperties.class)
@Testcontainers
@ActiveProfiles("test")
public abstract class WithDBTest extends BimTest {

    @MockBean
    SimpMessagingTemplate sn;

    protected static HikariDataSource DATASOURCE;

    // will be shared between test methods
    @Container
    public static final BimApiPostgreSQLContainer DB_CONTAINER = BimApiPostgreSQLContainer.getInstance();

    @DynamicPropertySource
    static void postgresqlProperties(DynamicPropertyRegistry registry) {
        // permet d'override au runtime les properties de src/main/test/resources/application.yml
        registry.add("database.system.address", () -> DB_CONTAINER.getHost());
        registry.add("database.system.port", () -> DB_CONTAINER.getMappedPort(5432));

        registry.add("database.realtime.address", () -> DB_CONTAINER.getHost());
        registry.add("database.realtime.port", () -> DB_CONTAINER.getMappedPort(5432));

        registry.add("database.histo.address", () -> DB_CONTAINER.getHost());
        registry.add("database.histo.port", () -> DB_CONTAINER.getMappedPort(5432));

        registry.add("database.settings.address", () -> DB_CONTAINER.getHost());
        registry.add("database.settings.port", () -> DB_CONTAINER.getMappedPort(5432));
    }

    @BeforeAll
    protected static void dataSource() {
        if (DATASOURCE == null) {
            HikariDataSource ds = new HikariDataSource();
            ds.setDriverClassName(DB_CONTAINER.getDriverClassName());
            ds.setJdbcUrl(DB_CONTAINER.getJdbcUrl());
            ds.setUsername(DB_CONTAINER.getUsername());
            ds.setPassword(DB_CONTAINER.getPassword());
            DATASOURCE = ds;
        }
    }

}

