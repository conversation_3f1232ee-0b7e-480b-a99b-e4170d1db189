package com.bimcoresolutions;

import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.utility.DockerImageName;

public class BimApiPostgreSQLContainer extends PostgreSQLContainer<BimApiPostgreSQLContainer> {
    public static final String IMAGE_VERSION = "xbgmsharp/timescaledb-postgis";
    public static final String DATABASE_NAME = "postgres";
    public static final String USERNAME = "postgres";
    public static final String PASSWORD = "password";
    private static BimApiPostgreSQLContainer container;

    private BimApiPostgreSQLContainer() {
        super(DockerImageName.parse(IMAGE_VERSION).asCompatibleSubstituteFor(PostgreSQLContainer.IMAGE));
    }

    public static BimApiPostgreSQLContainer getInstance() {
        if (container == null) {
            container = new BimApiPostgreSQLContainer();
            container.withDatabaseName(DATABASE_NAME)
                    .withUsername(USERNAME)
                    .withPassword(PASSWORD)
                    .withCommand("postgres", "-c", "max_connections=1000");
            container.addEnv("POSTGRES_MULTIPLE_EXTENSIONS", "postgis,hstore,postgis_topology,postgis_raster,pgrouting");
        }
        return container;
    }

    @Override
    public void start() {
        super.start();
    }

    @Override
    public void stop() {
        //do nothing, JVM handles shut down
    }
}