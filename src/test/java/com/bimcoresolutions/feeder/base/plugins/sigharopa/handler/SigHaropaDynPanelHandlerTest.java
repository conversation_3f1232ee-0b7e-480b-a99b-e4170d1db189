package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.dynpanel.DynPanelCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.DynPanelGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.DynPanel;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.DynPanelCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaDynPanelHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String MIVISU = "mivisu";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    SigHaropaDynPanelHandler sigHaropaDynPanelHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    DynPanelGenFeignClient dynPanelGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        dynPanelGenFeignClient = mock(DynPanelGenFeignClient.class);
        sigHaropaDynPanelHandler = new SigHaropaDynPanelHandler(bimApiClient, objectMapper, cmSender, dynPanelGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_dynpanel_sig() {
        String code = "1dynpanel";
        DynPanel dynPanel = new DynPanel().toBuilder().code(code).build();

        //mock
        PaginatedResponse<DynPanel> panelPaginatedResponse = new PaginatedResponse<>(List.of(dynPanel), new PaginatedResponse.Pagination());
        when(dynPanelGenFeignClient.get(new DynPanelCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("dynPanelSig.json");
        sigHaropaDynPanelHandler.handle(fileBody);
        verify(dynPanelGenFeignClient).get(new DynPanelCriteria(), null, null, null, null, null);
    }


    @Test
    @SneakyThrows
    void should_create_dynpanel() {
        String fileBody = fromFileToString("dynPanelSig.json");
        DynPanelCollection data = objectMapper.readValue(fileBody, DynPanelCollection.class);

        Map<String, Object> ids = Map.of(MIVISU, "1", SIG, "10001");
        Map<String, Object> messages = new HashMap<>();
        messages.put("0", Map.of("0", "N/A"));
        messages.put("1", Map.of("0", "N/A"));

        DynPanel dynPanel = new DynPanel().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .inhibition(false)
                .ismanual(false)
                .status(0)
                .messages(messages)
                .type("Panneau à message variable")
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .model("LACROIX")
                .name("PMV1")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaDynPanelHandler.treatSigDynPanel(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location").isEqualTo(dynPanel);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_dynpanel() {
        Map<String, Object> ids = Map.of(MIVISU, "1", SIG, "10001");

        DynPanel dynPanel = new DynPanel().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .model("LACROIX")
                .name("PMV1")
                .build();

        String fileBody = fromFileToString("dynPanelSig.json");
        DynPanelCollection data = objectMapper.readValue(fileBody, DynPanelCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaDynPanelHandler.treatSigDynPanel(List.of(dynPanel), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(dynPanel);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_dynpanel() {
        Map<String, Object> ids = Map.of(MIVISU, "1", SIG, "10001");

        DynPanel dynPanel = new DynPanel().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaDynPanelHandler.treatSigDynPanel(List.of(dynPanel), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(dynPanel.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}