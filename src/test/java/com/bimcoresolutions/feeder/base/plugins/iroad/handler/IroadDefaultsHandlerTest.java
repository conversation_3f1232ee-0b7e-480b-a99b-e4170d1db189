package com.bimcoresolutions.feeder.base.plugins.iroad.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ControllerApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.IntersectionApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.iroad.IroadUtils;
import com.bimcoresolutions.feeder.base.plugins.iroad.model.DefautIroad;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Controller;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Intersection;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static com.bimcoresolutions.feeder.base.plugins.iroad.handler.IroadTraficolorHandlerTest.IROAD;
import static java.nio.charset.Charset.defaultCharset;
import static java.util.Objects.requireNonNull;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

public class IroadDefaultsHandlerTest {
    private final ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
    IRoadDefaultsHandler iRoadDefaultHandler;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        AlarmApiClient alarmApiClient = mock(AlarmApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        IroadUtils iroadUtils = new IroadUtils();
        ControllerApiClient controllerApiClient = mock(ControllerApiClient.class);
        IntersectionApiClient intersectionApiClient = mock(IntersectionApiClient.class);
        iRoadDefaultHandler = new IRoadDefaultsHandler(bimApiClient, objectMapper, cmSender, iroadUtils, alarmApiClient, controllerApiClient, intersectionApiClient);
    }

    @SneakyThrows
    protected String fromFileToString(final String fileName) {
        return IOUtils.toString(requireNonNull(this.getClass().getResource(fileName)), defaultCharset());
    }

    @SneakyThrows
    @Test
    void should_create_intersection_iroad_alarm() {
        String temp = fromFileToString("iroadDefaults.json");
        List<DefautIroad> comingDefaults = objectMapper.readValue(temp, new TypeReference<>() {
        });

        Intersection intersection = new Intersection().toBuilder()
                ._id_bimcore("myIdBimcore")
                .code("myCode")
                .externalids(Map.of(IROAD, "831"))
                .build();

        Alarm expectedAlarm = new Alarm().toBuilder()
                .code("831_57_null")
                .externalids(Map.of(IROAD, "831"))
                .name("Autre")
                .intersection(List.of("myIdBimcore"))
                .metier(Map.of())
                .source(IROAD)
                .presence(true)
                .build();

        CrudOperation<Model> crudOperation = iRoadDefaultHandler.treatIntersectionDefaults(List.of(), comingDefaults, List.of(intersection));
        assertThat(crudOperation.getCreate()).hasSize(1);
        assertThat(crudOperation.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("lastupdate").isEqualTo(expectedAlarm);
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
    }

    @SneakyThrows
    @Test
    void should_update_intersection_iroad_alarm() {
        String temp = fromFileToString("iroadDefaults.json");
        List<DefautIroad> comingDefaults = objectMapper.readValue(temp, new TypeReference<>() {
        });

        Intersection intersection = new Intersection().toBuilder()
                ._id_bimcore("myIdBimcore")
                .code("myCode")
                .externalids(Map.of(IROAD, "831"))
                .build();

        Alarm alreadyExistingAlarm = new Alarm().toBuilder()
                .code("831_57_null")
                .externalids(Map.of(IROAD, "831"))
                .name("Alarme 2")
                .presence(false)
                .build();

        Alarm expectedAlarm = new Alarm().toBuilder()
                .code("831_57_null")
                .externalids(Map.of(IROAD, "831"))
                .name("Alarme 2")
                .presence(true)
                .build();

        CrudOperation<Model> crudOperation = iRoadDefaultHandler.treatIntersectionDefaults(List.of(alreadyExistingAlarm), comingDefaults, List.of(intersection));
        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("lastupdate").isEqualTo(expectedAlarm);
        assertThat(crudOperation.getDelete()).isEmpty();
    }


    @SneakyThrows
    @Test
    void should_create_controller_iroad_alarm() {
        String temp = fromFileToString("iroadDefaults.json");
        List<DefautIroad> comingDefaults = objectMapper.readValue(temp, new TypeReference<>() {
        });

        Controller controller = new Controller().toBuilder()
                ._id_bimcore("myIdBimcore")
                .code("myCode")
                .externalids(Map.of(IROAD, "870"))
                .build();

        Alarm expectedAlarm = new Alarm().toBuilder()
                .code("870_999_null")
                .externalids(Map.of(IROAD, "870"))
                .name("Déconnexion du contrôleur")
                .source(IROAD)
                .controller(List.of("myIdBimcore"))
                .metier(Map.of())
                .presence(true)
                .build();

        CrudOperation<Model> crudOperation = iRoadDefaultHandler.treatControllerDefaults(List.of(), comingDefaults, List.of(controller));
        assertThat(crudOperation.getCreate()).hasSize(1);
        assertThat(crudOperation.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("lastupdate").isEqualTo(expectedAlarm);
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
    }

    @SneakyThrows
    @Test
    void should_update_controller_iroad_alarm() {
        String temp = fromFileToString("iroadDefaults.json");
        List<DefautIroad> comingDefaults = objectMapper.readValue(temp, new TypeReference<>() {
        });

        Controller controller = new Controller().toBuilder()
                ._id_bimcore("myIdBimcore")
                .code("myCode")
                .externalids(Map.of(IROAD, "870"))
                .build();

        Alarm alreadyExistingAlarm = new Alarm().toBuilder()
                .code("870_999_null")
                .externalids(Map.of(IROAD, "870"))
                .name("Déconnexion du contrôleur")
                .source(IROAD)
                .metier(Map.of())
                .presence(false)
                .build();

        Alarm expectedAlarm = new Alarm().toBuilder()
                .code("870_999_null")
                .externalids(Map.of(IROAD, "870"))
                .name("Déconnexion du contrôleur")
                .source(IROAD)
                .metier(Map.of())
                .presence(true)
                .build();

        CrudOperation<Model> crudOperation = iRoadDefaultHandler.treatControllerDefaults(List.of(alreadyExistingAlarm), comingDefaults, List.of(controller));
        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("lastupdate").isEqualTo(expectedAlarm);
        assertThat(crudOperation.getDelete()).isEmpty();
    }

}