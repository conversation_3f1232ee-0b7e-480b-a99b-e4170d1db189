package com.bimcoresolutions.feeder.base.plugins.cityapp.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcityspe.ApiSpeDefaultTypeClient;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.DataplatformCityAppDefaultType;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.DefaultType;
import com.bimcoresolutions.feeder.base.plugins.cityapp.util.CityAppUtils;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Triple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class CityAppDefaultTypeHandlerTest extends BimTest {
    private static final TypeReference<List<DataplatformCityAppDefaultType>> typeReference = new TypeReference<>() {
    };
    private CityAppDefaultTypeHandler cityAppDefaultTypeHandler;
    private ApiSpeDefaultTypeClient apiSpeDefaultTypeClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        apiSpeDefaultTypeClient = mock(ApiSpeDefaultTypeClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        CityAppUtils cityAppUtils = new CityAppUtils();
        cityAppDefaultTypeHandler = new CityAppDefaultTypeHandler(bimApiClient, objectMapper, cmSender, apiSpeDefaultTypeClient, cityAppUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        UUID id = UUID.fromString("ad697d7f-357a-44f2-8dab-357a7a2d0bb8");
        DefaultType defaultType = new DefaultType().toBuilder()
                .id(id)
                .name("myName")
                .build();
        PaginatedResponse<DefaultType> resultList = new PaginatedResponse<>(List.of(defaultType), 0L, 1L, 1L);
        when(apiSpeDefaultTypeClient.listDefaultTypes()).thenReturn(resultList);

        String fileBody = fromFileToString("cityAppDefaultTypes.json");
        cityAppDefaultTypeHandler.handle(fileBody);

        verify(apiSpeDefaultTypeClient).listDefaultTypes();
    }

    @Test
    void should_create_cityapp_defaulttype() {
        List<DataplatformCityAppDefaultType> incomingDataplatformCityAppDefaultTypes = fromFileToObject("cityAppDefaultTypes.json", typeReference);
        Triple<List<DefaultType>, List<DefaultType>, List<UUID>> result = cityAppDefaultTypeHandler.treatDefaultTypes(List.of(), incomingDataplatformCityAppDefaultTypes);

        assertThat(result.getLeft()).hasSize(1);
        assertThat(result.getMiddle()).isEmpty();
        assertThat(result.getRight()).isEmpty();
    }

    @Test
    void should_update_cityapp_defaulttype() {
        UUID id = UUID.fromString("ad697d7f-357a-44f2-8dab-357a7a2d0bb8");
        List<DataplatformCityAppDefaultType> incomingDataplatformCityAppDefaultTypes = fromFileToObject("cityAppDefaultTypes.json", typeReference);
        DefaultType existingDefaultType = new DefaultType().toBuilder()
                .id(id)
                .externalids(Map.of("cityapp", "1"))
                .delay(2)
                .name("myName")
                .equipmenttype("LightingPoint")
                .code("myCode")
                .source(CityAppUtils.CITYAPP_SOURCE)
                .build();

        Triple<List<DefaultType>, List<DefaultType>, List<UUID>> result = cityAppDefaultTypeHandler.treatDefaultTypes(List.of(existingDefaultType), incomingDataplatformCityAppDefaultTypes);

        assertThat(result.getLeft()).isEmpty();
        assertThat(result.getMiddle()).hasSize(1);
        assertThat(result.getMiddle().getFirst()).usingRecursiveComparison().isEqualTo(existingDefaultType);
        assertThat(result.getRight()).isEmpty();

    }

    @Test
    void should_delete_cityapp_defaulttype() {
        UUID id = UUID.fromString("ad697d7f-357a-44f2-8dab-357a7a2d0bb8");
        List<DataplatformCityAppDefaultType> incomingDataplatformCityAppDefaultTypes = fromFileToObject("cityAppDefaultTypesToDelete.json", typeReference);

        DefaultType existingDefaultType = new DefaultType().toBuilder()
                .id(id)
                .externalids(Map.of("cityapp", "1"))
                .delay(2)
                .name("myName")
                .equipmenttype("LightingPoint")
                .code("myCode")
                .source("testU")
                .build();

        Triple<List<DefaultType>, List<DefaultType>, List<UUID>> result = cityAppDefaultTypeHandler.treatDefaultTypes(List.of(existingDefaultType), incomingDataplatformCityAppDefaultTypes);

        assertThat(result.getLeft()).isEmpty();
        assertThat(result.getMiddle()).isEmpty();
        assertThat(result.getRight()).hasSize(1);
        assertThat(result.getRight().getFirst()).isEqualTo(existingDefaultType.getId());

    }

}