package com.bimcoresolutions.feeder.base.plugins.cityapp.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.InterventionRequestApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.client.bimcityspe.ApiSpeDefaultTypeClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.DataplatformCityAppFailureReport;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.DefaultType;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.FactorInfoBim;
import com.bimcoresolutions.feeder.base.plugins.cityapp.util.CityAppUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.InterventionRequest;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class CityAppFailureReportHandlerTest extends BimTest {
    private static final TypeReference<List<DataplatformCityAppFailureReport>> typeReferenceFailureReport = new TypeReference<>() {
    };
    private static final String CITYAPP = "cityapp";
    private CityAppFailureReportHandler cityAppFailureReportHandler;
    private CabinetApiClient cabinetApiClient;
    private InterventionRequestApiClient interventionRequestApiClient;
    private LightingPointApiClient lightingPointApiClient;
    private ApiSpeDefaultTypeClient apiSpeDefaultTypeClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        cabinetApiClient = mock(CabinetApiClient.class);
        interventionRequestApiClient = mock(InterventionRequestApiClient.class);
        lightingPointApiClient = mock(LightingPointApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        apiSpeDefaultTypeClient = mock(ApiSpeDefaultTypeClient.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        CityAppUtils cityAppUtils = new CityAppUtils();
        cityAppFailureReportHandler = new CityAppFailureReportHandler(bimApiClient, lightingPointApiClient, cabinetApiClient, interventionRequestApiClient, apiSpeDefaultTypeClient, objectMapper, cmSender, cityAppUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        UUID id = UUID.randomUUID();
        InterventionRequest interventionRequest = InterventionRequest.builder().code("1").externalids(Map.of("cityapp", "1")).sourcecode("1").build();
        DefaultType defaultType = new DefaultType().toBuilder().id(id).name("myName").code("myCode").build();
        PaginatedResponse<DefaultType> resultList = new PaginatedResponse<>(List.of(defaultType), 0L, 1L, 1L);

        when(interventionRequestApiClient.getInterventionRequestByExternalIds(anyString(), anyList())).thenReturn(List.of(interventionRequest));
        when(apiSpeDefaultTypeClient.listDefaultTypes()).thenReturn(resultList);

        String fileBody = fromFileToString("cityAppFailureReport.json");
        cityAppFailureReportHandler.handle(fileBody);

        verify(interventionRequestApiClient).getInterventionRequestByExternalIds(anyString(), anyList());
        verify(apiSpeDefaultTypeClient).listDefaultTypes();
    }

    @Test
    void should_create_cityapp_cabinet_interventionrequest() {
        List<DataplatformCityAppFailureReport> incomingDataplatformCityAppFailureReport = fromFileToObject("cityAppFailureReport.json", typeReferenceFailureReport);
        UUID id = UUID.randomUUID();
        InterventionRequest expectedInterventionRequest = InterventionRequest.builder()
                .code(CITYAPP + "_20231005-001")
                .externalids(Map.of(CITYAPP, "20231005-001"))
                .cabinets(List.of("2563"))
                .sourcecode("20231005-001")
                .status("Completed")
                .creator(CITYAPP)
                .category("Point Lumineux Eteint")
                .sourcesystem(CITYAPP)
                .realenddate(new Date(1696507500000L))
                .deadline(new Date(1696629599999L))
                .infos(Map.of("impacttype", List.of(FactorInfoBim.builder().id("1").name("Third Party").code("ORT").comment("myComment").build())))
                .build();
        DefaultType existingDefaultType = DefaultType.builder()
                .id(id)
                .code("PLE")
                .equipmenttype("LightingPoint")
                .name("Point Lumineux Eteint")
                .build();

        Cabinet cabinet = Cabinet.builder()._id_bimcore("2563").code("cityapp_2563").externalids(Map.of(CITYAPP, "2563")).build();

        PaginatedResponse<DefaultType> resultList = new PaginatedResponse<>(List.of(existingDefaultType), 0L, 1L, 1L);
        when(cabinetApiClient.getCabinetsByExternalIds(CITYAPP, List.of("2563"))).thenReturn(List.of(cabinet));
        when(apiSpeDefaultTypeClient.listDefaultTypes()).thenReturn(resultList);

        CrudOperation<Model> crudOperation = cityAppFailureReportHandler.treatFailureReports(List.of(), incomingDataplatformCityAppFailureReport);

        assertThat(crudOperation.getCreate()).hasSize(1);
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getCreate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("lastupdate", "creationdate", "interventions", "location")
                .isEqualTo(expectedInterventionRequest);

    }

    @Test
    void should_create_cityapp_lighting_point_interventionrequest() {
        UUID id = UUID.randomUUID();
        List<DataplatformCityAppFailureReport> incomingDataplatformCityAppFailureReport = fromFileToObject("cityAppFailureReport.json", typeReferenceFailureReport);
        InterventionRequest expectedInterventionRequest = InterventionRequest.builder()
                .code(CITYAPP + "_20231005-001")
                .externalids(Map.of(CITYAPP, "20231005-001"))
                .lightingpoints(List.of("2563"))
                .creator(CITYAPP)
                .sourcecode("20231005-001")
                .status("Completed")
                .category("Point Lumineux Eteint")
                .sourcesystem(CITYAPP)
                .realenddate(new Date(1696507500000L))
                .deadline(new Date(1696629599999L))
                .infos(Map.of("impacttype", List.of(FactorInfoBim.builder().id("1").name("Third Party").code("ORT").comment("myComment").build())))
                .build();
        DefaultType existingDefaultType = DefaultType.builder()
                .id(id)
                .code("PLE")
                .equipmenttype("LightingPoint")
                .name("Point Lumineux Eteint")
                .build();

        LightingPoint lightingPoint = LightingPoint.builder()._id_bimcore("2563").code("cityapp_2563").externalids(Map.of(CITYAPP, "2563")).build();

        PaginatedResponse<DefaultType> resultList = new PaginatedResponse<>(List.of(existingDefaultType), 0L, 1L, 1L);
        when(lightingPointApiClient.getLightingPointsByExternalIds(CITYAPP, List.of("2563"))).thenReturn(List.of(lightingPoint));
        when(apiSpeDefaultTypeClient.listDefaultTypes()).thenReturn(resultList);

        CrudOperation<Model> crudOperation = cityAppFailureReportHandler.treatFailureReports(List.of(), incomingDataplatformCityAppFailureReport);

        assertThat(crudOperation.getCreate()).hasSize(1);
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getCreate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("lastupdate", "creationdate", "interventions", "location")
                .isEqualTo(expectedInterventionRequest);

    }

    @Test
    void should_update_cityapp_cabinet_interventionrequest() {
        UUID id = UUID.randomUUID();
        List<DataplatformCityAppFailureReport> incomingDataplatformCityAppFailureReport = fromFileToObject("cityAppFailureReport.json", typeReferenceFailureReport);

        InterventionRequest interventionRequest = InterventionRequest.builder()
                .code(CITYAPP + "_20231005-001")
                .externalids(Map.of(CITYAPP, "20231005-001"))
                .cabinets(List.of("2563"))
                .sourcecode("20231005-001")
                .status("InProgress")
                .category("Point Lumineux Eteint")
                .sourcesystem(CITYAPP)
                .creator(CITYAPP)
                .realenddate(new Date(1696507500000L))
                .deadline(new Date(1696629599999L))
                .infos(Map.of("impacttype", List.of(FactorInfoBim.builder().id("1").name("Third Party").code("ORT").comment("myComment").build())))
                .build();

        InterventionRequest expectedInterventionRequest = InterventionRequest.builder()
                .code(CITYAPP + "_20231005-001")
                .externalids(Map.of(CITYAPP, "20231005-001"))
                .cabinets(List.of("2563"))
                .sourcecode("20231005-001")
                .status("Completed")
                .category("Point Lumineux Eteint")
                .sourcesystem(CITYAPP)
                .creator(CITYAPP)
                .realenddate(new Date(1696507500000L))
                .deadline(new Date(1696629599999L))
                .infos(Map.of("impacttype", List.of(FactorInfoBim.builder().id("1").name("Third Party").code("ORT").comment("myComment").build())))
                .build();

        DefaultType existingDefaultType = DefaultType.builder()
                .id(id)
                .code("PLE")
                .equipmenttype("LightingPoint")
                .name("Point Lumineux Eteint")
                .build();

        Cabinet cabinet = Cabinet.builder()._id_bimcore("2563").code("cityapp_2563").externalids(Map.of(CITYAPP, "2563")).build();

        PaginatedResponse<DefaultType> resultList = new PaginatedResponse<>(List.of(existingDefaultType), 0L, 1L, 1L);
        when(cabinetApiClient.getCabinetsByExternalIds(CITYAPP, List.of("2563"))).thenReturn(List.of(cabinet));
        when(apiSpeDefaultTypeClient.listDefaultTypes()).thenReturn(resultList);

        CrudOperation<Model> crudOperation = cityAppFailureReportHandler.treatFailureReports(List.of(interventionRequest), incomingDataplatformCityAppFailureReport);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("lastupdate", "creationdate", "interventions", "location")
                .isEqualTo(expectedInterventionRequest);

    }

    @Test
    void should_update_cityapp_lighting_point_interventionrequest() {
        UUID id = UUID.randomUUID();
        List<DataplatformCityAppFailureReport> incomingDataplatformCityAppFailureReport = fromFileToObject("cityAppFailureReport.json", typeReferenceFailureReport);

        InterventionRequest interventionRequest = InterventionRequest.builder()
                .code(CITYAPP + "_20231005-001")
                .externalids(Map.of(CITYAPP, "20231005-001"))
                .lightingpoints(List.of("2563"))
                .sourcecode("20231005-001")
                .status("InProgress")
                .creator(CITYAPP)
                .category("Point Lumineux Eteint")
                .realenddate(new Date(1696507500000L))
                .deadline(new Date(1696629599999L))
                .sourcesystem(CITYAPP)
                .infos(Map.of("impacttype", List.of(FactorInfoBim.builder().id("1").name("Third Party").code("ORT").comment("myComment").build())))
                .build();

        InterventionRequest expectedInterventionRequest = InterventionRequest.builder()
                .code(CITYAPP + "_20231005-001")
                .externalids(Map.of(CITYAPP, "20231005-001"))
                .lightingpoints(List.of("2563"))
                .sourcecode("20231005-001")
                .status("Completed")
                .creator(CITYAPP)
                .category("Point Lumineux Eteint")
                .realenddate(new Date(1696507500000L))
                .deadline(new Date(1696629599999L))
                .sourcesystem(CITYAPP)
                .infos(Map.of("impacttype", List.of(FactorInfoBim.builder().id("1").name("Third Party").code("ORT").comment("myComment").build())))
                .build();
        DefaultType existingDefaultType = DefaultType.builder()
                .id(id)
                .code("PLE")
                .equipmenttype("LightingPoint")
                .name("Point Lumineux Eteint")
                .build();

        LightingPoint lightingPoint = LightingPoint.builder()._id_bimcore("2563").code("cityapp_2563").externalids(Map.of(CITYAPP, "2563")).build();

        PaginatedResponse<DefaultType> resultList = new PaginatedResponse<>(List.of(existingDefaultType), 0L, 1L, 1L);
        when(lightingPointApiClient.getLightingPointsByExternalIds(CITYAPP, List.of("2563"))).thenReturn(List.of(lightingPoint));
        when(apiSpeDefaultTypeClient.listDefaultTypes()).thenReturn(resultList);

        CrudOperation<Model> crudOperation = cityAppFailureReportHandler.treatFailureReports(List.of(interventionRequest), incomingDataplatformCityAppFailureReport);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("lastupdate", "creationdate", "interventions", "location")
                .isEqualTo(expectedInterventionRequest);

    }

}