package com.bimcoresolutions.feeder.base.plugins.waze.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.NotifEventApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.waze.WazeUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.NotifEvent;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

import static java.util.Map.entry;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class WazeAlertHandlerTest extends BimTest {
    WazeAlertHandler wazeAlertHandler;
    BimApiClient bimApiClient;
    NotifEventApiClient notifEventApiClient;
    CMSender cmSender;
    ObjectMapper objectMapper;
    private WazeUtils wazeUtils;

    @BeforeEach
    void init() {
        wazeUtils = new WazeUtils();
        cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        notifEventApiClient = mock(NotifEventApiClient.class);
        wazeAlertHandler = new WazeAlertHandler(
                bimApiClient,
                notifEventApiClient,
                objectMapper,
                cmSender,
                wazeUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_waze_alert() {
        NotifEvent notifEvent = new NotifEvent();
        CrudOperation<Model> crud = mock(CrudOperation.class);
        notifEvent.setExternalids(Map.ofEntries(
                entry("waze", "123456")
        ));

        when(notifEventApiClient.getNotifEventByExternalIds(anyString(), anyList())).thenReturn(List.of(notifEvent));
        Mockito.doNothing().when(cmSender).commit(crud);

        String fileBody = fromFileToString("alertWaze.json");
        wazeAlertHandler.handle(fileBody);

        verify(notifEventApiClient).getNotifEventByExternalIds(anyString(), anyList());
    }
}
