package com.bimcoresolutions.feeder.base.plugins.iroad.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CountingStationApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.iroad.IroadUtils;
import com.bimcoresolutions.feeder.base.plugins.iroad.model.IroadTraficolorDP;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CountingStation;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Geometry;
import org.springframework.cloud.openfeign.support.PageJacksonModule;
import org.springframework.cloud.openfeign.support.SortJacksonModule;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.nio.charset.Charset.defaultCharset;
import static java.util.Objects.requireNonNull;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class IroadTraficolorHandlerTest {

    public static final String IROAD = "iroad";
    public static final String ID_ALARM = "ID_ALARM";
    private IroadTraficolorHandler iroadTraficolorHandler;
    private BimApiClient bimApiClient;
    private AlarmApiClient alarmApiClient;
    private CountingStationApiClient countingStationApiClient;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        countingStationApiClient = mock(CountingStationApiClient.class);
        ObjectMapper objectMapper = objectMapper();
        IroadUtils iroadUtils = new IroadUtils();
        CMSender cmSender = mock(CMSender.class);
        iroadTraficolorHandler = new IroadTraficolorHandler(bimApiClient, cmSender, objectMapper, iroadUtils, alarmApiClient, countingStationApiClient);
    }

    public ObjectMapper objectMapper() {
        SimpleModule module = new SimpleModule();
        module.addDeserializer(Geometry.class, new com.bimcoresolutions.util.base.serialisation.GeometryDeserializer());
        return new ObjectMapper()
                .findAndRegisterModules()
                .setSerializationInclusion(JsonInclude.Include.NON_EMPTY)
                .registerModule(new PageJacksonModule())
                .registerModule(new SortJacksonModule())
                .registerModule(module);
    }

    @Test
    void should_treat_message_test() throws JsonProcessingException {
        //given
        Map<String, Object> externalIds = new HashMap<>();
        externalIds.put(IROAD, "SCOM003");

        Map<String, Object> externalIds2 = new HashMap<>();
        externalIds2.put(IROAD, "Cam 14.1 Nord");

        Map<String, Object> externalIds3 = new HashMap<>();
        externalIds3.put(IROAD, "PTM103");

        Alarm existingAlarm = new Alarm();
        existingAlarm.set_id_bimcore(ID_ALARM + 1);
        existingAlarm.setCode("SCOM003error");
        existingAlarm.setExternalids(externalIds);

        Alarm existingAlarmTobeUpdated = new Alarm(); // expected update presence true --> false
        existingAlarmTobeUpdated.set_id_bimcore(ID_ALARM + 2);
        existingAlarmTobeUpdated.setCode("PTM103error");
        existingAlarmTobeUpdated.setExternalids(externalIds3);

        CountingStation existingCountingStationSCO = new CountingStation().toBuilder()
                ._id_bimcore("Element_R_01")
                .externalids(externalIds)
                .alarms(List.of(existingAlarm.get_id_bimcore()))
                .status(-1)
                .build();

        CountingStation existingCountingStationCam = new CountingStation().toBuilder()
                ._id_bimcore("Element_R_02")
                .externalids(externalIds2)
                .status(0) // expected update 0 --> -1
                .build();

        CountingStation existingCountingStationPMT = new CountingStation().toBuilder()
                ._id_bimcore("Element_R_03")
                .externalids(externalIds3)
                .alarms(List.of(existingAlarmTobeUpdated.get_id_bimcore()))
                .status(-1) // expected update -1 --> 1
                .build();

        List<IroadTraficolorDP> incomingIroadTraficolorDPs = fromFileToObject(
                "messageDataplatform_iroad_traficolor_empty_counters.json",
                new TypeReference<>() {
                }
        );

        //mock
        when(countingStationApiClient.getCountingStationByExternalIds(eq(IROAD), any()))
                .thenReturn(List.of(existingCountingStationSCO, existingCountingStationCam, existingCountingStationPMT));
        when(alarmApiClient.getAlarmByExternalIds(eq(IROAD), any())).thenReturn(List.of(existingAlarm, existingAlarmTobeUpdated));

        //when
        iroadTraficolorHandler.handle(fromFileToString("messageDataplatform_iroad_traficolor_empty_counters.json")); // test interaction bimApiClient
        CrudOperation<Model> result = iroadTraficolorHandler.executeRulesIroadTraficolor(
                incomingIroadTraficolorDPs,
                List.of(existingCountingStationSCO, existingCountingStationCam, existingCountingStationPMT),
                List.of(existingAlarm, existingAlarmTobeUpdated)
        );

        verify(countingStationApiClient).getCountingStationByExternalIds(eq(IROAD), any());
        verify(alarmApiClient).getAlarmByExternalIds(eq(IROAD), any());

        //then
        // 1 new alarm (Cam 14.1 Nord)
        assertThat(result.getCreate()).hasSize(1);
        // 2 updated countingstations (Cam 14.1 Nord --> status -1 & PTM103 --> status 1) + 1 updated alarm (PTM103 --> presence false)
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getDelete()).isEmpty();
    }

    private final ObjectMapper objectMapper = JsonMapper.builder().findAndAddModules().build();

    protected <T> T fromFileToObject(final String fileName, TypeReference<T> typeReference) {
        try {
            return objectMapper.readValue(this.getClass().getResource(fileName), typeReference);
        } catch (Exception e) {
            String message = "pas trouvé le fichier " + fileName + " dans le repertoire " + this.getClass().getResource(".");
            throw new RuntimeException(message, e);
        }
    }

    @Test
    void should_treat_message_empty_counters() throws JsonProcessingException {
        //given
        Map<String, Object> externalIds2 = new HashMap<>();
        externalIds2.put(IROAD, "Cam 14.1 Nord");

        Map<String, Object> externalIds3 = new HashMap<>();
        externalIds3.put(IROAD, "PTM103");

        Alarm existingAlarm = new Alarm();
        existingAlarm.set_id_bimcore(ID_ALARM + 2);
        existingAlarm.setCode("PTM103error");
        existingAlarm.setExternalids(externalIds3);

        CountingStation existingCountingStationCam = new CountingStation().toBuilder()
                .externalids(externalIds2)
                ._id_bimcore("Element_R_02")
                .status(0)
                .build();

        CountingStation existingCountingStationPMT = new CountingStation().toBuilder()
                ._id_bimcore("Element_R_03")
                .externalids(externalIds3)
                .alarms(List.of(existingAlarm.get_id_bimcore()))
                .status(-1)
                .build();

        List<IroadTraficolorDP> incomingIroadTraficolorDPs = fromFileToObject(
                "messageDataplatform_iroad_traficolor_empty_counters.json",
                new TypeReference<>() {
                });

        //mock
        when(countingStationApiClient.getCountingStationByExternalIds(eq(IROAD), any()))
                .thenReturn(List.of(existingCountingStationCam, existingCountingStationPMT));
        when(alarmApiClient.getAlarmByExternalIds(eq(IROAD), any())).thenReturn(List.of(existingAlarm));

        //when
        iroadTraficolorHandler.handle(fromFileToString("messageDataplatform_iroad_traficolor_empty_counters.json")); // test interaction bimApiClient
        CrudOperation<Model> result = iroadTraficolorHandler.executeRulesIroadTraficolor(
                incomingIroadTraficolorDPs,
                List.of(existingCountingStationCam, existingCountingStationPMT),
                List.of(existingAlarm)
        );

        verify(countingStationApiClient).getCountingStationByExternalIds(eq(IROAD), any());
        verify(alarmApiClient).getAlarmByExternalIds(eq(IROAD), any());

        //then
        // 1 new alarm (Cam 14.1 Nord)
        assertThat(result.getCreate()).hasSize(1);
        // 1 updated countingstation (Cam 14.1 Nord --> status -1)
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getDelete()).isEmpty();
    }

    @SneakyThrows
    protected String fromFileToString(final String fileName) {
        return IOUtils.toString(requireNonNull(this.getClass().getResource(fileName)), defaultCharset());
    }


}
