package com.bimcoresolutions.feeder.base.plugins.mivisu.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.DynPanelApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.mivisu.MivisuUtils;
import com.bimcoresolutions.feeder.base.plugins.mivisu.model.MivisuEntity;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.DynPanel;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.mock;

public class MivisuEntityHandlerIntegrationTest extends BimTest {

    public static final String ID_BIMCORE_DYNPANEL = "id_bimcore_dynpanel";
    public static final String CODE_DYNPANEL = "dynpanel";
    public static final String MIVISU = "mivisu";
    public static final Integer SUBSTRING_PATH = 8; //JS1/1/S/mivisuName

    public static final DynPanel DYNPANEL_ROUTE_INDUS = new DynPanel().toBuilder()._id_bimcore(ID_BIMCORE_DYNPANEL).externalids(Map.of(MIVISU, "route_indus"))
            .messages(Map.of("0", "TEST"))
            .status(0)
            .location(new GeometryFactory().createPoint(new Coordinate(1.0982894897460938d, 49.44600275120271d)))
            .code("1" + CODE_DYNPANEL)
            .build();

    public static final DynPanel DYNPANEL_BREQUE = new DynPanel().toBuilder()
            ._id_bimcore(ID_BIMCORE_DYNPANEL)
            .externalids(Map.of(MIVISU, "breque"))
            .messages(Map.of("0", "TEST"))
            .status(0)
            .location(new GeometryFactory().createPoint(new Coordinate(1.0982894897460938d, 49.44600275120271d)))
            .code("2" + CODE_DYNPANEL)
            .build();

    public static final DynPanel DYNPANEL_CANAL_BOSSIERE = new DynPanel().toBuilder()
            ._id_bimcore(ID_BIMCORE_DYNPANEL)
            .externalids(Map.of(MIVISU, "canal_bossiere"))
            .messages(Map.of("0", "TEST"))
            .status(0)
            .location(new GeometryFactory().createPoint(new Coordinate(1.0982894897460938d, 49.44600275120271d)))
            .code("3" + CODE_DYNPANEL)
            .build();
    public static final DynPanel DYNPANEL_EUROPE = new DynPanel().toBuilder()
            ._id_bimcore(ID_BIMCORE_DYNPANEL)
            .externalids(Map.of(MIVISU, "europe"))
            .messages(Map.of("0", "TEST"))
            .status(0)
            .location(new GeometryFactory().createPoint(new Coordinate(1.0982894897460938d, 49.44600275120271d)))
            .code("4" + CODE_DYNPANEL)
            .build();

    public static final DynPanel DYNPANEL_BREXIT = new DynPanel().toBuilder()
            ._id_bimcore(ID_BIMCORE_DYNPANEL)
            .externalids(Map.of(MIVISU, "brexit"))
            .messages(Map.of("0", "TEST"))
            .status(0)
            .location(new GeometryFactory().createPoint(new Coordinate(1.0982894897460938d, 49.44600275120271d)))
            .code("5" + CODE_DYNPANEL)
            .build();

    public static final List<DynPanel> LIST_DYNPANELS = List.of(DYNPANEL_EUROPE, DYNPANEL_CANAL_BOSSIERE, DYNPANEL_BREQUE, DYNPANEL_BREXIT, DYNPANEL_ROUTE_INDUS);

    MivisuEntityHandler mivisuEntityHandler;

    ObjectMapper objectMapper;

    @BeforeEach
    void init() {
        objectMapper = new ObjectMapper().findAndRegisterModules();
        mivisuEntityHandler = new MivisuEntityHandler(mock(BimApiClient.class), objectMapper, mock(CMSender.class), new MivisuUtils(), mock(AlarmApiClient.class),
                mock(DynPanelApiClient.class));
    }

    @Test
    @SneakyThrows
    void should_update_mivisu_message_and_status() {
        String fileBody = fromFileToString("mivisu_message_integration.json");
        List<MivisuEntity> data = objectMapper.readValue(fileBody, new TypeReference<>() {
        });

        data.replaceAll(item -> {
            item.setMivisuName(item.getMivisuName().substring(SUBSTRING_PATH));
            return item;
        });

        //when
        CrudOperation<Model> result = mivisuEntityHandler.treatMivisuEntities(data, LIST_DYNPANELS, List.of());

        //then
        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).hasSize(1);
        Assertions.assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_create_alarm_mivisu_message() {
        String fileBody = fromFileToString("mivisu_message_alarm.json");
        List<MivisuEntity> data = objectMapper.readValue(fileBody, new TypeReference<>() {
        });

        data.replaceAll(item -> {
            item.setMivisuName(item.getMivisuName().substring(SUBSTRING_PATH));
            return item;
        });

        //when
        CrudOperation<Model> result = mivisuEntityHandler.treatMivisuEntities(data, LIST_DYNPANELS, List.of());

        //then
        Assertions.assertThat(result.getCreate()).hasSize(1);
        Assertions.assertThat(result.getUpdate()).hasSize(1);
        Assertions.assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_alarm_mivisu_message() {
        String fileBody = fromFileToString("mivisu_message_integration.json");
        List<MivisuEntity> data = objectMapper.readValue(fileBody, new TypeReference<>() {
        });

        data.replaceAll(item -> {
            item.setMivisuName(item.getMivisuName().substring(SUBSTRING_PATH));
            return item;
        });

        Map<String, Object> map = Map.of("mivisu", "europe");
        Alarm alarm = Alarm.builder().code("europeErreur").externalids(map).presence(true).build();
        List<Alarm> alarms = List.of(alarm);

        //when
        CrudOperation<Model> result = mivisuEntityHandler.treatMivisuEntities(data, LIST_DYNPANELS, alarms);

        //then
        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).hasSize(2);
        Assertions.assertThat(result.getDelete()).isEmpty();
    }
}
