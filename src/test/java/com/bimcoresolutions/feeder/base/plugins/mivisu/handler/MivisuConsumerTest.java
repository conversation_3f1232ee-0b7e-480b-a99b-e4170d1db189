package com.bimcoresolutions.feeder.base.plugins.mivisu.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.DynPanelApiClient;
import com.bimcoresolutions.feeder.base.client.bimcityspe.PreconfiguredMessageApiClient;
import com.bimcoresolutions.feeder.base.plugins.mivisu.MivisuConsumer;
import com.bimcoresolutions.feeder.base.plugins.mivisu.MivisuUtils;
import com.bimcoresolutions.feeder.base.plugins.mivisu.model.MivisuEntity;
import com.bimcoresolutions.feeder.base.plugins.mivisu.model.PreConfiguredMessage;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.DynPanel;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.context.ApplicationContext;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

class MivisuConsumerTest extends BimTest {

    MivisuConsumer mivisuConsumer;
    BimApiClient bimApiClient;
    AlarmApiClient alarmApiClient;
    DynPanelApiClient dynPanelApiClient;
    PreconfiguredMessageApiClient preconfiguredMessageApiClient;
    CMSender cMSender;
    MivisuEntityHandler mivisuEntityHandler;
    MivisuPreconfiguredMessageHandler mivisuPreconfiguredMessageHandler;

    ObjectMapper objectMapper;

    private static final int SUBSTRING_PATH_PREPARAMETER_MESSAGE = 14; //JS1/1/S/mivisuName

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        dynPanelApiClient = mock(DynPanelApiClient.class);
        preconfiguredMessageApiClient = mock(PreconfiguredMessageApiClient.class);
        cMSender = mock(CMSender.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        mivisuEntityHandler = new MivisuEntityHandler(bimApiClient, objectMapper, cMSender, new MivisuUtils(), alarmApiClient, dynPanelApiClient);
        mivisuPreconfiguredMessageHandler = new MivisuPreconfiguredMessageHandler(bimApiClient, objectMapper, cMSender, preconfiguredMessageApiClient);
        mivisuConsumer = new MivisuConsumer(objectMapper, mock(ApplicationContext.class));
        mivisuConsumer.addMivisuHandler("entity", mivisuEntityHandler);
        mivisuConsumer.addMivisuHandler("preconfigured_message", mivisuPreconfiguredMessageHandler);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_mivisu() {
        String code = "1pmv";
        Map<String, Object> ids = Map.of("mivisu", "brexit");
        DynPanel dynPanel = new DynPanel().toBuilder().code(code).externalids(ids).build();
        // On parametre le dynPanel pour que ce soit les messages qui soient differents
        dynPanel.setStatus(1);
        dynPanel.setMessages(new HashMap<>());
        List<DynPanel> list = List.of(dynPanel);

        //mock
        Mockito.when(dynPanelApiClient.getDynPanelByExternalIds(Mockito.anyString(), Mockito.anyList())).thenReturn(list);

        //when
        String fileBody = fromFileToString("mivisu_message.json");
        mivisuConsumer.consume(fileBody.getBytes(StandardCharsets.UTF_8));

        Mockito.verify(cMSender).commitWithRelations(any(), any());
    }

    @Test
    @SneakyThrows
    void should_build_6_preconfigured_message() {
        String temp = fromFileToString("preparameter_message.json");
        String jsonBody = new String(temp.getBytes(StandardCharsets.UTF_8));
        List<PreConfiguredMessage> preConfiguredMessages = new ArrayList<>();
        List<MivisuEntity> entities = objectMapper.readValue(jsonBody, new TypeReference<>() {
        });

        entities.forEach(x -> {
            Optional<PreConfiguredMessage> optionalPreConfiguredMessage = mivisuPreconfiguredMessageHandler.buildPreConfiguredMessage(x);
            optionalPreConfiguredMessage.ifPresent(preConfiguredMessages::add);
        });

        Assertions.assertThat(preConfiguredMessages).hasSize(1);

    }

    @Test
    @SneakyThrows
    void should_create_preconfigured_message() {

        String temp = fromFileToString("preparameter_message.json");
        String jsonBody = new String(temp.getBytes(StandardCharsets.UTF_8));
        List<MivisuEntity> entities = objectMapper.readValue(jsonBody, new TypeReference<>() {
        });
        List<MivisuEntity> entitiesTemp = objectMapper.readValue(jsonBody, new TypeReference<>() {
        });

        entitiesTemp.replaceAll(x -> {
            x.setMivisuName(x.getMivisuName().substring(SUBSTRING_PATH_PREPARAMETER_MESSAGE));
            return x;
        });

        List<PreConfiguredMessage> preConfiguredMessageList = new ArrayList<>();
        entitiesTemp.forEach(x -> {
            Optional<PreConfiguredMessage> optionalPreConfiguredMessage = mivisuPreconfiguredMessageHandler.buildPreConfiguredMessage(x);
            optionalPreConfiguredMessage.ifPresent(preConfiguredMessageList::add);
        });

        Assertions.assertThat(preConfiguredMessageList).hasSize(1);

        Pair<List<PreConfiguredMessage>, List<PreConfiguredMessage>> expected = Pair.of(List.of(), preConfiguredMessageList);

        Mockito.doReturn(preConfiguredMessageList).when(preconfiguredMessageApiClient).getPreConfiguredMessageList();

        Pair<List<PreConfiguredMessage>, List<PreConfiguredMessage>> result = mivisuPreconfiguredMessageHandler.buildCreateOrUpdateList(entities);

        Assertions.assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }
}
