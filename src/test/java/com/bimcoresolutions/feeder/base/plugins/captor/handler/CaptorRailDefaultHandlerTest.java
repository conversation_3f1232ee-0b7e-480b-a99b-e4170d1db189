package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.RailCrossingApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.RailwayApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.rail.raildefault.RailDefault;
import com.bimcoresolutions.feeder.base.plugins.captor.model.rail.raildefault.RailDefaultCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailCrossing;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Railway;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorRailDefaultHandlerTest extends BimTest {
    public static final String CAPTOR = "captor";
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    public static final LineString line_string = new GeometryFactory().createLineString(new Coordinate[]{
            new Coordinate(1.09d, 49.45d),
            new Coordinate(1.19d, 49.55d),
            new Coordinate(1.29d, 49.65d)
    });
    BimApiClient bimApiClient;
    AlarmApiClient alarmApiClient;
    RailwayApiClient railwayApiClient;
    RailCrossingApiClient railCrossingApiClient;
    ObjectMapper objectMapper;
    CaptorRailDefaultHandler captorRailDefaultHandler;
    CaptorUtils captorUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        railwayApiClient = mock(RailwayApiClient.class);
        railCrossingApiClient = mock(RailCrossingApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorRailDefaultHandler = new CaptorRailDefaultHandler(bimApiClient, objectMapper, cmSender, captorUtils, alarmApiClient, railwayApiClient, railCrossingApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_raildefault() {
        Map<String, Object> railway_external_ids = Map.of("captor", "RFPZ205");
        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .externalids(railway_external_ids)
                .build();

        Map<String, Object> railCrossing_external_ids = Map.of("captor", "PN63ter");
        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .externalids(railCrossing_external_ids)
                .build();

        List<String> externalIds = List.of("PN63ter", "RFPZ205");

        Alarm alarmRailway = new Alarm().toBuilder()
                .code("RFPZ205error")
                .externalids(railway_external_ids)
                ._id_bimcore("ID_ALARM")
                .build();

        Alarm alarmRailCrossing = new Alarm().toBuilder()
                .code("PN63tererror")
                .externalids(railCrossing_external_ids)
                ._id_bimcore("ID_ALARM")
                .build();

        //mock
        when(railwayApiClient.getRailwayByExternalIds("captor", externalIds)).thenReturn(List.of(railway));
        when(railCrossingApiClient.getRailCrossingByExternalIds("captor", externalIds)).thenReturn(List.of(railCrossing));
        when(alarmApiClient.getAlarmByExternalIds("captor", externalIds)).thenReturn(List.of(alarmRailway, alarmRailCrossing));

        //when
        String fileBody = fromFileToString("railDefaultCaptor.json");
        captorRailDefaultHandler.handle(fileBody);

        verify(railwayApiClient).getRailwayByExternalIds("captor", externalIds);
        verify(railCrossingApiClient).getRailCrossingByExternalIds("captor", externalIds);
        verify(alarmApiClient).getAlarmByExternalIds("captor", externalIds);
    }

    @Test
    @SneakyThrows
    void should_create_alarm_railwaydefault_message() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ205");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN63ter");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ205")
                .status(1)
                .operatingstate(0)
                .externalids(ids_railway)
                .type("railway")
                .build();

        Railway expected_railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ205")
                .status(-1)
                .operatingstate(0)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN63ter")
                .status(0)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        String fileBody = fromFileToString("railDefaultCaptor_railway.json");
        RailDefaultCollection data = objectMapper.readValue(fileBody, RailDefaultCollection.class);
        List<RailDefault> comingRailDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorRailDefaultHandler.treatRailDefault(
                List.of(railway), List.of(railCrossing), comingRailDefaults, List.of());

        //then
        assertThat(result.get(Alarm.class).getCreate()).hasSize(1); // RailDefault alarm is created
        assertThat(result.get(Railway.class).getUpdate()).hasSize(1); // railway's status is updated
        assertThat(result.get(Railway.class).getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expected_railway);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_alarm_railwaydefault_message() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ205");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN63ter");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ205")
                .status(1)
                .operatingstate(1)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN63ter")
                .status(1)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        Alarm alarmRailway = new Alarm().toBuilder()
                .code("RFPZ205error6")
                .externalids(Map.of(CAPTOR, "RFPZ205"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.TRUE)
                .railway(List.of(railway.get_id_bimcore()))
                .build();

        String fileBody = fromFileToString("railDefaultCaptor_railway.json");
        RailDefaultCollection data = objectMapper.readValue(fileBody, RailDefaultCollection.class);
        List<RailDefault> comingRailDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorRailDefaultHandler.treatRailDefault(
                List.of(railway), List.of(railCrossing), comingRailDefaults, List.of(alarmRailway));

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(2); // railway's status is updated + railDefault alarm is updated (already exists)
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_create_alarm_railcrossingdefault_message() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ205");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN63ter");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ205")
                .status(0)
                .operatingstate(1)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN63ter")
                .status(1)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        RailCrossing expected_railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN63ter")
                .status(-1)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        String fileBody = fromFileToString("railDefaultCaptor_railCrossing.json");
        RailDefaultCollection data = objectMapper.readValue(fileBody, RailDefaultCollection.class);
        List<RailDefault> comingRailDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorRailDefaultHandler.treatRailDefault(
                List.of(railway), List.of(railCrossing), comingRailDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // RailDefault alarm is created
        assertThat(result.getUpdate()).hasSize(1); // railCrossing's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expected_railCrossing);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_alarm_railcrossingdefault_message() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ205");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN63ter");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ205")
                .status(0)
                .operatingstate(1)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN63ter")
                .status(1)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        Alarm alarmRailCrossing = new Alarm().toBuilder()
                .code("PN63tererror1")
                .externalids(Map.of(CAPTOR, "PN63ter"))
                ._id_bimcore("ID_ALARM1")
                .presence(Boolean.TRUE)
                .railcrossing(List.of(railCrossing.get_id_bimcore()))
                .build();

        Alarm alarmRailCrossing2 = new Alarm().toBuilder()
                .code("PN63tererror2")
                .externalids(Map.of(CAPTOR, "PN63ter"))
                ._id_bimcore("ID_ALARM2")
                .presence(Boolean.FALSE)
                .railcrossing(List.of(railCrossing.get_id_bimcore()))
                .build();

        Alarm alarmRailCrossing3 = new Alarm().toBuilder()
                .code("PN63tererror3")
                .externalids(Map.of(CAPTOR, "PN63ter"))
                ._id_bimcore("ID_ALARM3")
                .presence(Boolean.FALSE)
                .railcrossing(List.of(railCrossing.get_id_bimcore()))
                .build();

        String fileBody = fromFileToString("railDefaultCaptor_railCrossing.json");
        RailDefaultCollection data = objectMapper.readValue(fileBody, RailDefaultCollection.class);
        List<RailDefault> comingRailDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorRailDefaultHandler.treatRailDefault(
                List.of(railway), List.of(railCrossing), comingRailDefaults, List.of(alarmRailCrossing, alarmRailCrossing2, alarmRailCrossing3));

        //then
        assertThat(result.getCreate()).hasSize(1); // new alarm is created for alarm status 9 (did not exist)
        assertThat(result.getUpdate()).hasSize(2); // railCrossing's status is updated + current active railDefault alarm (presence true) is updated : presence false
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_create_alarm_railwaydefault_and_railcrossingdefault_message() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ205");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN63ter");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ205")
                .status(1)
                .operatingstate(0)
                .externalids(ids_railway)
                .type("railway")
                .build();

        Railway expected_railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ205")
                .status(-1)
                .operatingstate(0)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN63ter")
                .status(1)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        RailCrossing expected_railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN63ter")
                .status(-1)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        String fileBody = fromFileToString("railDefaultCaptor.json");
        RailDefaultCollection data = objectMapper.readValue(fileBody, RailDefaultCollection.class);
        List<RailDefault> comingRailDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorRailDefaultHandler.treatRailDefault(
                List.of(railway), List.of(railCrossing), comingRailDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(2); // 2x RailDefault alarms created
        assertThat(result.getUpdate()).hasSize(2); // railway and railcrossing status are updated

        assertThat(result.get(Railway.class).getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expected_railway);
        assertThat(result.get(RailCrossing.class).getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expected_railCrossing);
        assertThat(result.getDelete()).isEmpty();
    }

}