package com.bimcoresolutions.feeder.base.plugins.slv.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlertApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.configuration.SpringBeanConfiguration;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.slv.SlvUtils;
import com.bimcoresolutions.feeder.base.plugins.slv.model.DataplatformSlvFailure;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

class SlvAlarmHandlerTest extends BimTest {
    public static final String CITYAPP = "cityapp";
    public static final String RANDOM_ID = "random id";
    public static final String ALERT_CATEGORY_NAME = "Communication Failure";
    private static final ObjectMapper objectMapper = new SpringBeanConfiguration().objectMapper();
    private SlvFailureHandler slvFailureHandler;
    private final Map<String, Object> testLamps = new HashMap<>();
    private LightingPoint existingLightingPointToBeUpdated;
    private Map<String, Object> externalIds;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        AlarmApiClient alarmApiClient = mock(AlarmApiClient.class);
        LightingPointApiClient lightingPointApiClient = mock(LightingPointApiClient.class);
        AlertApiClient alertApiClient = mock(AlertApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        SlvUtils slvUtils = new SlvUtils();
        slvFailureHandler = new SlvFailureHandler(bimApiClient, lightingPointApiClient, alertApiClient, objectMapper, cmSender, slvUtils, alarmApiClient);

        // ===== Test lamps =====
        //Init test lamp 1
        Map<String, Object> externalIdsLampOne = new HashMap<>();
        externalIdsLampOne.put(CITYAPP, "8450");

        Map<String, Object> lampOne = new HashMap<>();
        lampOne.put("model", "");
        lampOne.put("externalids", externalIdsLampOne);

        //Init test lamp 2
        Map<String, Object> externalIdsLampTwo = new HashMap<>();
        externalIdsLampTwo.put(CITYAPP, "8454");

        Map<String, Object> lampTwo = new HashMap<>();
        lampOne.put("model", "test");
        lampTwo.put("externalids", externalIdsLampTwo);

        //Init test lamps
        testLamps.put("NEW_thing_thing_555_L1", lampOne);
        testLamps.put("NEW_thing_thing_555_L2", lampTwo);

        // ===== Test externalIds =====
        externalIds = new HashMap<>();
        externalIds.put(CITYAPP, "100");

        // ===== Test LightingPoint =====
        existingLightingPointToBeUpdated = new LightingPoint().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("cityapp_100")
                .externalids(externalIds)
                .status(0)
                .inhibition(false)
                .lamps(testLamps)
                .build();
    }

    @Test
    void should_create_slv_alarm_and_update_light_status() {
        //given
        List<DataplatformSlvFailure> incomingSlvFailures = fromFileToObject(
                "failureSlvCreateAlarm.json",
                new TypeReference<>() {
                });

        //when
        CrudOperation<Model> result = slvFailureHandler.treatSlvAlarmsAndLightingPoints(
                List.of(existingLightingPointToBeUpdated), incomingSlvFailures, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getDelete()).isEmpty();

        Alarm expectedCreatedAlarm = (Alarm) result.getCreate().stream()
                .filter(a -> a.getClass().equals(Alarm.class))
                .findFirst()
                .get();
        assertThat(expectedCreatedAlarm.getCode()).isEqualTo("100-NEW_thing_thing_555_L1-PowerSupplyFailure");
        assertTrue(expectedCreatedAlarm.getPresence());

        LightingPoint expectedUpdatedLightingPoint = (LightingPoint) result.getUpdate().stream()
                .filter(a -> a.getClass().equals(LightingPoint.class))
                .findFirst()
                .get();
        assertThat(expectedUpdatedLightingPoint.getStatus()).isEqualTo(-1);
    }

    @Test
    void should_update_slv_alarm_and_light_status() {
        //given
        List<DataplatformSlvFailure> incomingSlvFailures = fromFileToObject(
                "failureSlvUpdateAlarm.json",
                new TypeReference<>() {
                });

        Alarm existingAlarmToBeUpdated = new Alarm().toBuilder()
                .code("100-NEW_thing_thing_555_L1-PowerSupplyFailure")
                .externalids(externalIds)
                .presence(true)
                .build();

        //when
        CrudOperation<Model> result = slvFailureHandler.treatSlvAlarmsAndLightingPoints(
                List.of(existingLightingPointToBeUpdated), incomingSlvFailures, List.of(existingAlarmToBeUpdated));

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(2);
        assertThat(result.getDelete()).isEmpty();

        Alarm expectedUpdatedAlarm = (Alarm) result.getUpdate().stream()
                .filter(a -> a.getClass().equals(Alarm.class))
                .findFirst()
                .get();
        assertThat(expectedUpdatedAlarm.getCode()).isEqualTo("100-NEW_thing_thing_555_L1-PowerSupplyFailure");
        assertFalse(expectedUpdatedAlarm.getPresence());

        LightingPoint expectedUpdatedLightingPoint = (LightingPoint) result.getUpdate().stream()
                .filter(a -> a.getClass().equals(LightingPoint.class))
                .findFirst()
                .get();
        assertThat(expectedUpdatedLightingPoint.getStatus()).isEqualTo(1);
    }

    @Test
    void should_create_slv_alert() {
        //given
        List<DataplatformSlvFailure> incomingSlvFailures = fromFileToObject(
                "failureSlvCreateAlert.json",
                new TypeReference<>() {
                });

        //when
        CrudOperation<Model> result = slvFailureHandler.treatSlvAlarmsAndLightingPoints(
                List.of(existingLightingPointToBeUpdated), incomingSlvFailures, List.of());

        // equivalent to bimapiclient addMissingIds for alarms in handle method
        result.get(Alarm.class).getCreate().stream().findFirst().ifPresent(a -> a.set_id_bimcore(RANDOM_ID));

        slvFailureHandler.treatSlvAlerts(
                List.of(existingLightingPointToBeUpdated), List.of(), result, new HashMap<>(), ALERT_CATEGORY_NAME);

        //then
        assertThat(result.getCreate()).hasSize(2); // 1 alarm & 1 alert
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getDelete()).isEmpty();

        Alert expectedCreatedAlert = (Alert) result.getCreate().stream()
                .filter(a -> a.getClass().equals(Alert.class))
                .findFirst()
                .get();
        assertThat(expectedCreatedAlert.getName()).isEqualTo(ALERT_CATEGORY_NAME);
        assertThat(expectedCreatedAlert.getLightingpoint()).isEqualTo(List.of(existingLightingPointToBeUpdated.get_id_bimcore()));
    }

    @Test
    void should_not_create_slv_alert() {
        //given
        List<DataplatformSlvFailure> incomingSlvFailures = fromFileToObject(
                "failureSlvCreateAlert.json",
                new TypeReference<>() {
                });

        // Alert already exists on this equipment, no Alert creation expected
        Alert existingAlert = new Alert().toBuilder()
                .category(ALERT_CATEGORY_NAME)
                .lightingpoint(List.of(RANDOM_ID))
                .build();

        //when
        CrudOperation<Model> result = slvFailureHandler.treatSlvAlarmsAndLightingPoints(
                List.of(existingLightingPointToBeUpdated), incomingSlvFailures, List.of());

        // equivalent to bimapiclient addMissingIds for alarms in handle method
        result.get(Alarm.class).getCreate().stream().findFirst().ifPresent(a -> a.set_id_bimcore(RANDOM_ID));

        slvFailureHandler.treatSlvAlerts(
                List.of(existingLightingPointToBeUpdated), List.of(existingAlert), result, new HashMap<>(), ALERT_CATEGORY_NAME);

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getDelete()).isEmpty();

        Optional<Model> optionalCreatedAlert = result.getCreate().stream()
                .filter(a -> a.getClass().equals(Alert.class))
                .findAny();
        assertTrue(optionalCreatedAlert.isEmpty());
    }

}