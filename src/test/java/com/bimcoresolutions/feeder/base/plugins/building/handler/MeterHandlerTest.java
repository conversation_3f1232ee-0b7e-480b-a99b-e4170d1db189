package com.bimcoresolutions.feeder.base.plugins.building.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.MeterApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.building.model.MeterDP;
import com.bimcoresolutions.feeder.base.plugins.building.util.BuildingUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Meter;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.n52.jackson.datatype.jts.JtsModule;

import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class MeterHandlerTest extends BimTest {
    MeterHandler meterHandler;
    BuildingUtils buildingUtils;
    BimApiClient bimApiClient;
    MeterApiClient meterApiClient;
    CMSender cmSender;
    ObjectMapper objectMapper;

    @BeforeEach
    void init() {
        cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        meterApiClient = mock(MeterApiClient.class);
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JtsModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        buildingUtils = new BuildingUtils(bimApiClient, mock(MeterApiClient.class));
        meterHandler = new MeterHandler(bimApiClient, objectMapper, cmSender, meterApiClient, buildingUtils);
    }

    @Test
    @SneakyThrows
    void should_create_meter() {
        String fileBody = fromFileToString("meters.json");
        List<MeterDP> meters = objectMapper.readValue(fileBody, new TypeReference<>() {
        });
        CrudOperation<Meter> result = meterHandler.treatMeters(meters, List.of());

        Assertions.assertThat(result.getCreate()).hasSize(1);
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_meter() {
        Meter meter = new Meter();
        meter.setCode("C19SE817503");
        meter.setExternalids(new HashMap<>());

        when(meterApiClient.getMetersByCode(anyList())).thenReturn(List.of(meter));

        String fileBody = fromFileToString("meters.json");
        List<MeterDP> meters = objectMapper.readValue(fileBody, new TypeReference<>() {
        });
        CrudOperation<Meter> result = meterHandler.treatMeters(meters, List.of(meter));

        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).hasSize(1);
        Assertions.assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_meter() {
        Meter meter = new Meter();
        meter.setCode("I23JE051248");

        when(meterApiClient.getMetersByCode(anyList())).thenReturn(List.of(meter));

        CrudOperation<Meter> result = meterHandler.treatMeters(List.of(), List.of(meter));

        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).hasSize(1);
    }
}
