package com.bimcoresolutions.feeder.base.plugins.sogelink.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.EventApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sogelink.model.DataplatformSogelinkEvent;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Event;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class SogelinkEventHandlerTest extends BimTest {
    public static final String SOGELINK = "sogelink";
    public static final String RANDOM_ID = "random id";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    private SogelinkEventHandler sogelinkEventHandler;
    private EventApiClient eventApiClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        eventApiClient = mock(EventApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        sogelinkEventHandler = new SogelinkEventHandler(bimApiClient, eventApiClient, objectMapper, cmSender);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        Event event = new Event().toBuilder().code("myCode").build();
        String fileBody = fromFileToString("eventSogelink.json");

        //mock
        when(eventApiClient.getEventByExternalIds(eq(SOGELINK), any())).thenReturn(List.of(event));
        when(eventApiClient.getEventCategoriesByLabel(anyString())).thenReturn(List.of("Travaux"));

        //when
        sogelinkEventHandler.handle(fileBody);

        verify(eventApiClient).getEventByExternalIds(eq(SOGELINK), any());
        verify(eventApiClient).getEventCategoriesByLabel(anyString());
    }

    @Test
    void should_create_sogelink_event() {
        //given
        List<DataplatformSogelinkEvent> incomingSogelinkEventDPs = fromFileToObject("eventSogelink.json", new TypeReference<>() {
        });

        //when
        CrudOperation<Model> result = sogelinkEventHandler.treatEvent(List.of(), incomingSogelinkEventDPs, "Travaux");

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    void should_update_sogelink_event() {
        //given
        Map<String, Object> externalIds = new HashMap<>();
        externalIds.put(SOGELINK, "75040229");

        List<DataplatformSogelinkEvent> incomingSogelinkEventDPs = fromFileToObject("eventSogelink.json", new TypeReference<>() {
        });

        Event existingEventToBeUpdated = new Event().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("75040229")
                .externalids(externalIds)
                .label("test DT")
                .comment("to be updated")
                .category("test_different")
                .creator("CITEOS")
                .source(SOGELINK)
                .build();

        Event expectedEventUpdate = new Event().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("75040229")
                .externalids(externalIds)
                .location(point)
                .source(SOGELINK)
                .creator("CITEOS")
                .metier(Map.of("metiers", List.of("Eclairage Public")))
                .label("2020052001819TIG DT")
                .site("NR")
                .comment("Extension du centre pénitentiaire")
                .category("Travaux")
                .creationdate(new Date(1000))
                .realstartdate(new Date(2000))
                .realenddate(new Date(3000))
                .build();

        //when
        CrudOperation<Model> result = sogelinkEventHandler.treatEvent(List.of(existingEventToBeUpdated), incomingSogelinkEventDPs, "Travaux");

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location", "laststatuschangedate").isEqualTo(expectedEventUpdate);
        assertThat(result.getDelete()).isEmpty();
    }

}