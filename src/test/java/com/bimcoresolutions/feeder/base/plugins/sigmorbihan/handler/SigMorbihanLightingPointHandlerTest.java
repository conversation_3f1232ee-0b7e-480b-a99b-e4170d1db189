package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.SigMorbihanUtils;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.lightingpoint.LightingPointSig;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.lightingpoint.LightingPointSigCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

import static java.util.Map.entry;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class SigMorbihanLightingPointHandlerTest extends BimTest {
    SigMorbihanLightingPointHandler sigMorbihanLightingPointHandler;
    LightingPointApiClient lightingPointApiClient;
    CabinetApiClient cabinetApiClient;
    SigMorbihanUtils sigMorbihanUtils;
    CMSender cmSender;
    ObjectMapper objectMapper;
    BimApiClient bimApiClient;

    @BeforeEach
    void init() {
        cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        lightingPointApiClient = mock(LightingPointApiClient.class);
        cabinetApiClient = mock(CabinetApiClient.class);
        sigMorbihanUtils = spy(new SigMorbihanUtils(bimApiClient));
        sigMorbihanLightingPointHandler = new SigMorbihanLightingPointHandler(bimApiClient, objectMapper, cmSender, lightingPointApiClient, cabinetApiClient, sigMorbihanUtils);
    }

    @Test
    @SneakyThrows
    void should_create_lightingpoint() {
        CrudOperation<LightingPoint> crud = mock(CrudOperation.class);
        Cabinet cabinet = new Cabinet().toBuilder()
                ._id_bimcore("id_test")
                .code("MorbihanSig_7010")
                .owner("Test")
                .externalids(Map.ofEntries(
                        entry("MorbihanSig","7010")
                ))
                .build();

        doNothing().when(cmSender).commit(crud);
        doNothing().when(sigMorbihanUtils).updateCabinetForRelation(any(),any(),anySet());
        when(cabinetApiClient.getCabinetsByExternalIds((anyString()), anyList())).thenReturn(List.of(cabinet));
        when(lightingPointApiClient.getLightingPointsByExternalIds((anyString()), anyList())).thenReturn(List.of());

        String fileBody = fromFileToString("lightingpointsSig.json");
        LightingPointSigCollection input = objectMapper.readValue(fileBody, LightingPointSigCollection.class);
        List<LightingPointSig> comingLps = input.getFeatures();
        int created = 0;
        int updated = 0;
        int deleted = 0;
        int processed = 0;
        int lpsCount = input.getFeaturesCount();

        Map<String,Integer> result = sigMorbihanLightingPointHandler.treatSlicedLps(comingLps,created,updated,deleted,processed,lpsCount);

        Assertions.assertThat(result.get("created")).isEqualTo(1);
        Assertions.assertThat(result.get("updated")).isEqualTo(0);
        Assertions.assertThat(result.get("deleted")).isEqualTo(0);
    }

    @Test
    @SneakyThrows
    void should_update_lightingpoint() {
        LightingPoint lightingPoint = new LightingPoint().toBuilder()
                .externalids(Map.ofEntries(
                        entry("Cabinet", "7010"),
                        entry("MorbihanSig", "524083"))
                )
                ._id_bimcore("test_id_lp")
                .build();
        CrudOperation<LightingPoint> crud = mock(CrudOperation.class);
        Cabinet cabinet = new Cabinet().toBuilder()
                ._id_bimcore("id_test")
                .code("MorbihanSig_7010")
                .owner("Test")
                .externalids(Map.ofEntries(
                        entry("MorbihanSig","7010")
                ))
                .build();

        Mockito.doNothing().when(cmSender).commit(crud);
        when(cabinetApiClient.getCabinetsByExternalIds((anyString()), anyList())).thenReturn(List.of(cabinet));
        when(lightingPointApiClient.getLightingPointsByExternalIds((anyString()), anyList())).thenReturn(List.of(lightingPoint));

        String fileBody = fromFileToString("lightingpointsSig.json");
        LightingPointSigCollection input = objectMapper.readValue(fileBody, LightingPointSigCollection.class);
        List<LightingPointSig> comingLps = input.getFeatures();
        int created = 0;
        int updated = 0;
        int deleted = 0;
        int processed = 0;
        int lpsCount = input.getFeaturesCount();

        Map<String,Integer> result = sigMorbihanLightingPointHandler.treatSlicedLps(comingLps,created,updated,deleted,processed,lpsCount);

        Assertions.assertThat(result.get("created")).isEqualTo(0);
        Assertions.assertThat(result.get("updated")).isEqualTo(1);
        Assertions.assertThat(result.get("deleted")).isEqualTo(0);
    }

    @Test
    @SneakyThrows
    void should_delete_lightingpoint() {
        LightingPoint lightingPoint = new LightingPoint().toBuilder()
                .externalids(Map.ofEntries(
                        entry("Cabinet", "7010"),
                        entry("MorbihanSig", "524083"))
                )
                ._id_bimcore("test_id_lp")
                .build();
        CrudOperation<LightingPoint> crud = mock(CrudOperation.class);

        Mockito.doNothing().when(cmSender).commit(crud);
        when(lightingPointApiClient.getLightingPointsByExternalIds((anyString()), anyList())).thenReturn(List.of(lightingPoint));

        String fileBody = fromFileToString("lightingpointSigDelete.json");
        LightingPointSigCollection input = objectMapper.readValue(fileBody, LightingPointSigCollection.class);
        List<LightingPointSig> comingLps = input.getFeatures();
        int created = 0;
        int updated = 0;
        int deleted = 0;
        int processed = 0;
        int lpsCount = input.getFeaturesCount();

        Map<String,Integer> result = sigMorbihanLightingPointHandler.treatSlicedLps(comingLps,created,updated,deleted,processed,lpsCount);

        Assertions.assertThat(result.get("created")).isEqualTo(0);
        Assertions.assertThat(result.get("updated")).isEqualTo(0);
        Assertions.assertThat(result.get("deleted")).isEqualTo(1);
    }
}
