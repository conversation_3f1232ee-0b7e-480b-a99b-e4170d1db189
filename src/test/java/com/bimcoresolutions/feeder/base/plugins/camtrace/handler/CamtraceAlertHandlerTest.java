package com.bimcoresolutions.feeder.base.plugins.camtrace.handler;

import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.camtrace.model.CamtraceAlert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Camera;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class CamtraceAlertHandlerTest {

    @Nested
    class ToCamtraceAlarms {

        @Test
        void should_ignore_if_no_camera() {
            // Given
            List<CamtraceAlert> incomingAlerts = List.of(
                    CamtraceAlert.builder()
                            .id(1L)
                            .name("myName")
                            .active(true)
                            .errors(List.of(
                                    new CamtraceAlert.CamtraceError("mosaic", "Not connected"),
                                    new CamtraceAlert.CamtraceError("rtsp", "Not connected")))
                            .build()
            );

            List<Alarm> existingAlarms = List.of();
            List<Camera> existingCameras = List.of();

            CrudOperation<Alarm> expected = new CrudOperation<>();

            // When
            CrudOperation<Alarm> result = CamtraceAlertHandler.treatAlert(incomingAlerts, existingAlarms, existingCameras);

            // Then
            assertThat(result).isEqualTo(expected);
        }

        @Test
        void should_create_if_camera_found_and_no_last_image() {
            // Given
            List<CamtraceAlert> incomingAlerts = List.of(
                    CamtraceAlert.builder()
                            .id(1L)
                            .name("myName")
                            .active(true)
                            .errors(List.of(
                                    new CamtraceAlert.CamtraceError("mosaic", "Not connected"),
                                    new CamtraceAlert.CamtraceError("rtsp", "Not connected")))
                            .build()
            );

            List<Alarm> existingAlarms = List.of();
            List<Camera> existingCameras = List.of(
                    Camera.builder()
                            ._id_bimcore("id_camera_1")
                            .code(CamtraceCameraHandler.buildCode(1L))
                            .build()
            );

            // When
            CrudOperation<Alarm> result = CamtraceAlertHandler.treatAlert(incomingAlerts, existingAlarms, existingCameras);

            // Then
            assertThat(result.getCreate()).hasSize(2);
            assertThat(result.getUpdate()).isEmpty();
            assertThat(result.getDelete()).isEmpty();
        }

        @Test
        void should_update_if_camera_and_last_image_found() {
            // Given
            List<CamtraceAlert> incomingAlerts = List.of(
                    CamtraceAlert.builder()
                            .id(1L)
                            .name("myName")
                            .active(true)
                            .errors(List.of(new CamtraceAlert.CamtraceError("mosaic", "Not connected")))
                            .build()
            );

            List<Alarm> existingAlarms = List.of(
                    Alarm.builder()
                            ._id_bimcore("id_alert_1")
                            .code(CamtraceAlertHandler.buildCode(1L, "mosaic"))
                            .presence(false)
                            .build());
            List<Camera> existingCameras = List.of(
                    Camera.builder()
                            ._id_bimcore("id_camera_1")
                            .code(CamtraceCameraHandler.buildCode(1L))
                            .build()
            );

            // When
            CrudOperation<Alarm> result = CamtraceAlertHandler.treatAlert(incomingAlerts, existingAlarms, existingCameras);

            // Then
            assertThat(result.getCreate()).isEmpty();
            assertThat(result.getUpdate()).hasSize(1);
            assertThat(result.getDelete()).isEmpty();
        }

        @Test
        void should_turn_off_if_camera_and_alarm_up_but_not_in_input() {
            // Given
            List<CamtraceAlert> incomingAlerts = List.of();

            List<Alarm> existingAlarms = List.of(
                    Alarm.builder()
                            ._id_bimcore("id_alert_1")
                            .code(CamtraceAlertHandler.buildCode(1L, "mosaic"))
                            .presence(true)
                            .build());
            List<Camera> existingCameras = List.of(
                    Camera.builder()
                            ._id_bimcore("id_camera_1")
                            .code(CamtraceCameraHandler.buildCode(1L))
                            .build()
            );

            // When
            CrudOperation<Alarm> result = CamtraceAlertHandler.treatAlert(incomingAlerts, existingAlarms, existingCameras);

            // Then
            assertThat(result.getCreate()).isEmpty();
            assertThat(result.getUpdate()).hasSize(1);
            assertThat(result.getUpdate().stream().findFirst().get().getPresence()).isFalse();
            assertThat(result.getDelete()).isEmpty();
        }

    }

}
