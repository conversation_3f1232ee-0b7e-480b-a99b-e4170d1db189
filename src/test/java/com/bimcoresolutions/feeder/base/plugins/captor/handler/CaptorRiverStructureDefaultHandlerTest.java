package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.RiverStructureApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructuredefault.RiverStructureDefault;
import com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructuredefault.RiverStructureDefaultCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RiverStructure;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorRiverStructureDefaultHandlerTest extends BimTest {

    public static final String CAPTOR = "captor";
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    AlarmApiClient alarmApiClient;
    RiverStructureApiClient riverStructureApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorRiverStructureDefaultHandler captorRiverStructureDefaultHandler;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        riverStructureApiClient = mock(RiverStructureApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorRiverStructureDefaultHandler = new CaptorRiverStructureDefaultHandler(bimApiClient, objectMapper, cmSender, captorUtils, alarmApiClient, riverStructureApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_riverstructuredefault() {
        Map<String, Object> ids = Map.of("captor", "PONT_QUINETTE_AMONT");
        RiverStructure riverStructure = new RiverStructure().toBuilder().externalids(ids).build();

        List<String> externalIds = List.of("PONT_QUINETTE_AMONT");

        Alarm alarmRiverStructure = new Alarm().toBuilder()
                .code("PONT_QUINETTE_AMONTerror")
                .externalids(ids)
                ._id_bimcore("ID_ALARM")
                .build();

        //mock
        when(riverStructureApiClient.getRiverStructureByExternalsIds("captor", externalIds)).thenReturn(List.of(riverStructure));
        when(alarmApiClient.getAlarmByExternalIds("captor", externalIds)).thenReturn(List.of(alarmRiverStructure));

        //when
        String fileBody = fromFileToString("riverStructureDefaultCaptor_alarm.json");
        captorRiverStructureDefaultHandler.handle(fileBody);

        verify(riverStructureApiClient).getRiverStructureByExternalsIds("captor", externalIds);
        verify(alarmApiClient).getAlarmByExternalIds("captor", externalIds);
    }

    @Test
    @SneakyThrows
    void should_create_alarm_riverstructuredefault_message() {
        Map<String, Object> ids = Map.of(CAPTOR, "PONT_QUINETTE_AMONT");

        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1riverstructure")
                .inhibition(false)
                .location(point)
                .name("de Tancarville (ancienne)")
                .status(0)
                .operatingstate(0)
                .lockstate(0)
                .externalids(ids)
                .type("Pont")
                .build();

        RiverStructure expected = new RiverStructure().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1riverstructure")
                .inhibition(false)
                .location(point)
                .name("de Tancarville (ancienne)")
                .status(1)
                .operatingstate(0)
                .lockstate(0)
                .externalids(ids)
                .type("Pont")
                .build();

        String fileBody = fromFileToString("riverStructureDefaultCaptor_alarm.json");
        RiverStructureDefaultCollection data = objectMapper.readValue(fileBody, RiverStructureDefaultCollection.class);
        List<RiverStructureDefault> comingRiverStructureDefault = data.getItems();

        //when
        CrudOperation<Model> result = captorRiverStructureDefaultHandler.treatRiverStructureDefault(
                List.of(riverStructure),
                comingRiverStructureDefault,
                List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // riverstructuredefault alarm is created
        assertThat(result.getUpdate()).hasSize(1); // riverstructure's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location").isEqualTo(expected);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_alarm_riverstructuredefault_message() {
        Map<String, Object> ids = Map.of(CAPTOR, "PONT_QUINETTE_AMONT");

        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1riverstructure")
                .inhibition(false)
                .location(point)
                .name("de Tancarville (ancienne)")
                .status(0)
                .operatingstate(0)
                .lockstate(0)
                .externalids(ids)
                .type("Pont")
                .build();

        Alarm alarmRiverStructure = new Alarm().toBuilder()
                .code("PONT_QUINETTE_AMONTerror")
                .externalids(Map.of(CAPTOR, "PONT_QUINETTE_AMONT"))
                ._id_bimcore("ID_ALARM")
                .riverstructure(List.of(riverStructure.get_id_bimcore()))
                .presence(Boolean.FALSE)
                .build();

        String fileBody = fromFileToString("riverStructureDefaultCaptor.json");
        RiverStructureDefaultCollection data = objectMapper.readValue(fileBody, RiverStructureDefaultCollection.class);
        List<RiverStructureDefault> comingRiverStructureDefault = data.getItems();

        //when
        CrudOperation<Model> result = captorRiverStructureDefaultHandler.treatRiverStructureDefault(
                List.of(riverStructure),
                comingRiverStructureDefault,
                List.of(alarmRiverStructure));

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(2); // riverstructure's status is updated + riverstructuredefault alarm is updated (already exists)
        assertThat(result.getDelete()).isEmpty();
    }

}