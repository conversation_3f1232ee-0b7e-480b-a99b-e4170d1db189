package com.bimcoresolutions.feeder.base.plugins.pip.handler;

import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.pip.PipUtils;
import com.bimcoresolutions.feeder.base.plugins.pip.model.DataplatformAssetDevice;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.EnvironmentalSensor;
import com.bimcoresolutions.util.base.model.Model;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

class PipAssetDeviceHandlerTest {
    public static final String PIP = "pip";
    @Mock
    private PipUtils pipUtils;
    @InjectMocks
    private PipAssetDeviceHandler pipAssetDeviceHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void rulesPipSensor_WithNewSensor_ShouldReturnCreateOperation() {
        // Given: a new device with no corresponding environmental sensor
        DataplatformAssetDevice newDevice = new DataplatformAssetDevice(
                "newDeviceId", "customerId", "serialNumber", "deviceTypeId",
                "LevelSense-01", "networkType", "networkId", "lifecycleState",
                Map.of("Latitude", 48.8566, "Longitude", 2.3522),
                "2023-10-10T12:00:00Z", -70, "state", "Healthy"
        );
        Map<String, EnvironmentalSensor> existingSensors = Map.of();

        EnvironmentalSensor createdSensor = EnvironmentalSensor.builder()
                .code("newDeviceId")
                .name("serialNumber")
                .type("LevelSense-01")
                .location(null)
                .status(1)
                .build();

        when(pipUtils.createEnvironmentalSensor(newDevice)).thenReturn(createdSensor);

        // When: calling rulesPipSensor with the new device
        CrudOperation<Model> result = pipAssetDeviceHandler.rulesPipSensor(List.of(newDevice), existingSensors);

        // Then: a new sensor should be created, with no updates
        assertEquals(1, result.getCreate().size());
        assertTrue(result.getCreate().contains(createdSensor));
        assertEquals(0, result.getUpdate().size());

        verify(pipUtils, times(1)).createEnvironmentalSensor(newDevice);
    }

    @Test
    @SneakyThrows
    void rulesPipSensor_WithExistingSensor_ShouldReturnUpdateOperation() {
        // Given: an existing sensor that matches the incoming device
        DataplatformAssetDevice incomingDevice = new DataplatformAssetDevice(
                "existingDeviceId", "customerId", "serialNumber", "deviceTypeId",
                "LevelSense-01", "networkType", "networkId", "lifecycleState",
                Map.of("Latitude", 48.8566, "Longitude", 2.3522),
                "2023-10-10T12:00:00Z", -70, "state", "Warning"
        );

        EnvironmentalSensor existingSensor = EnvironmentalSensor.builder()
                .code("existingDeviceId")
                .name("Old Serial")
                .type("LevelSense-01")
                .status(1)
                .build();

        EnvironmentalSensor updatedSensor = EnvironmentalSensor.builder()
                .code("existingDeviceId")
                .name("serialNumber")
                .type("LevelSense-01")
                .status(-1)
                .build();

        Map<String, EnvironmentalSensor> existingSensors = Map.of("existingDeviceId", existingSensor);

        when(pipUtils.updateEnvironmentalSensor(existingSensor, incomingDevice)).thenReturn(updatedSensor);

        // When: calling rulesPipSensor with the matching device
        CrudOperation<Model> result = pipAssetDeviceHandler.rulesPipSensor(List.of(incomingDevice), existingSensors);

        // Then: no sensors should be created, but an existing one should be updated
        assertEquals(0, result.getCreate().size());
        assertEquals(1, result.getUpdate().size());
        assertTrue(result.getUpdate().contains(updatedSensor));

        verify(pipUtils, times(1)).updateEnvironmentalSensor(existingSensor, incomingDevice);
    }

    @Test
    void rulesPipAlarm_WithCreatedSensor_ShouldReturnCreateOperationForAlarm() {
        // Given: a newly created sensor that requires an alarm
        EnvironmentalSensor newSensor = EnvironmentalSensor.builder()
                .code("newSensorId")
                .name("New Sensor")
                .type("GeoSense-01")
                .status(-1)
                .build();

        Map<String, Alarm> existingAlarms = Map.of();

        Alarm createdAlarm = Alarm.builder()
                .code("pip_error_newSensorId")
                .name("Environmental Sensor newSensorId")
                .presence(true)
                .build();

        when(pipUtils.createAlarm(newSensor)).thenReturn(createdAlarm);

        // When: calling rulesPipAlarm with the newly created sensor
        CrudOperation<Model> result = pipAssetDeviceHandler.rulesPipAlarm(List.of(newSensor), List.of(), existingAlarms);

        // Then: a new alarm should be created, with update for adding alarm to the sensor
        assertEquals(1, result.getCreate().size());
        assertTrue(result.getCreate().contains(createdAlarm));
        assertEquals(1, result.getUpdate().size());

        verify(pipUtils, times(1)).createAlarm(newSensor);
    }

    @Test
    void rulesPipAlarm_WithUpdatedSensorAndExistingAlarm_ShouldReturnUpdateOperationForAlarm() {
        // Given: an updated sensor that matches an existing alarm
        EnvironmentalSensor updatedSensor = EnvironmentalSensor.builder()
                .code("existingSensorId")
                .name("Updated Sensor")
                .type("GeoSense-01")
                .status(-1)
                .externalids(Map.of(PIP, "existingSensorId"))
                .build();

        Alarm existingAlarm = Alarm.builder()
                .code("pip_error_existingSensorId")
                .name("Environmental Sensor existingSensorId")
                .presence(false)
                .build();

        Map<String, Alarm> existingAlarms = Map.of("existingSensorId", existingAlarm);

        // When: calling rulesPipAlarm with the updated sensor
        CrudOperation<Model> result = pipAssetDeviceHandler.rulesPipAlarm(List.of(), List.of(updatedSensor), existingAlarms);

        // Then: the existing alarm should be updated, with no new alarm created
        assertEquals(0, result.getCreate().size());
        assertEquals(1, result.getUpdate().size());
        assertTrue(result.getUpdate().contains(existingAlarm));

        // Verify that the alarm's presence is updated to true
        assertTrue(existingAlarm.getPresence());

        verify(pipUtils, never()).createAlarm(any(EnvironmentalSensor.class));
    }
}
