package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.transformerstation.TransformerStationCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.TransformerStationGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.TransformerStation;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.TransformerStationCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaTransformerStationHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String CAPTOR = "captor";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    SigHaropaTransformerStationHandler sigHaropaTransformerStationHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    TransformerStationGenFeignClient transformerStationGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        transformerStationGenFeignClient = mock(TransformerStationGenFeignClient.class);
        sigHaropaTransformerStationHandler = new SigHaropaTransformerStationHandler(bimApiClient, objectMapper, cmSender, transformerStationGenFeignClient, sigHaropaUtils);
    }


    @Test
    @SneakyThrows
    void should_treat_transformerStation_sig() {
        String code = "1transformerStation";
        TransformerStation transformerStation = new TransformerStation().toBuilder().code(code).build();

        //mock
        PaginatedResponse<TransformerStation> panelPaginatedResponse = new PaginatedResponse<>(List.of(transformerStation), new PaginatedResponse.Pagination());
        when(transformerStationGenFeignClient.get(new TransformerStationCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("transformerStationSig.json");
        sigHaropaTransformerStationHandler.handle(fileBody);
        verify(transformerStationGenFeignClient).get(new TransformerStationCriteria(), null, null, null, null, null);
    }


    @Test
    @SneakyThrows
    void should_create_transformerStation() {
        String fileBody = fromFileToString("transformerStationSig.json");
        TransformerStationCollection data = objectMapper.readValue(fileBody, TransformerStationCollection.class);

        Map<String, Object> ids = Map.of(CAPTOR, "ELP203", SIG, "10001");
        TransformerStation transformerStation = new TransformerStation().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .status(0)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .externalids(ids)
                .build();

        //when
        CrudOperation<Model> result = sigHaropaTransformerStationHandler.treatSigTransformerStations(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(transformerStation);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_transformerStation() {
        Map<String, Object> ids = Map.of(CAPTOR, "ELP203", SIG, "10001");

        TransformerStation transformerStation = new TransformerStation().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .status(0)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("transformerStationSig.json");
        TransformerStationCollection data = objectMapper.readValue(fileBody, TransformerStationCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaTransformerStationHandler.treatSigTransformerStations(List.of(transformerStation), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(transformerStation);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_transformerStation() {
        Map<String, Object> ids = Map.of(CAPTOR, "ELP203", SIG, "10001");

        TransformerStation transformerStation = new TransformerStation().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .status(0)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .externalids(ids)
                .build();

        //when
        CrudOperation<Model> result = sigHaropaTransformerStationHandler.treatSigTransformerStations(List.of(transformerStation), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(transformerStation.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}