package com.bimcoresolutions.feeder.base.plugins.grafana.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.NotifEventApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.grafana.model.alert.GrafanaAlertDetail;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.NotifEvent;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

public class DefaultGrafanaHandlerTest extends BimTest {
    private final ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
    DefaultGrafanaHandler defaultGrafanaHandler;
    NotifEventApiClient notifEventApiClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        notifEventApiClient = mock(NotifEventApiClient.class);
        defaultGrafanaHandler = new DefaultGrafanaHandler(objectMapper, bimApiClient, cmSender, notifEventApiClient);
    }

    @SneakyThrows
    @Test
    void should_create_notifevent_grafana() {
        String temp = fromFileToString("defaultGrafana.json");
        GrafanaAlertDetail grafanaAlertDetail = objectMapper.readValue(temp, new TypeReference<>() {});
        NotifEvent expectedNotifEvent = new NotifEvent().toBuilder()
                .category("Test Etat Pont Rouge Fermé")
                .code("Test Etat Pont Rouge Fermé_2025-07-15T03:30:00Z")
                .apparitiondate(Date.from(Instant.parse("2025-07-15T03:30:00Z")))
                .externalids(Map.of("grafana", "Test Etat Pont Rouge Fermé"))
                .name("Test Etat Pont Rouge Fermé")
                .source("grafana")
                .status("ToApprove")
                .build();

        CrudOperation<Model> crudOperation = defaultGrafanaHandler.executeGrafanaAlertIntoToApprove(List.of(grafanaAlertDetail), List.of());
        assertThat(crudOperation.getCreate()).hasSize(1);
        assertThat(crudOperation.getCreate().stream().toList().getFirst()).usingRecursiveComparison().isEqualTo(expectedNotifEvent);
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_notifevent_grafana() {
        String temp = fromFileToString("defaultOkGrafana.json");
        GrafanaAlertDetail grafanaAlertDetail = objectMapper.readValue(temp, new TypeReference<>() {});

        NotifEvent notifEvent = new NotifEvent().toBuilder()
                .category("Test Etat Pont Rouge Fermé")
                .code("Test Etat Pont Rouge Fermé_2025-07-15T03:30:00Z")
                .apparitiondate(Date.from(Instant.parse("2025-07-15T03:30:00Z")))
                .externalids(Map.of("grafana", "Test Etat Pont Rouge Fermé"))
                .name("Test Etat Pont Rouge Fermé")
                .source("grafana")
                .status("ToApprove")
                .build();

        NotifEvent expectedNotifEvent = new NotifEvent().toBuilder()
                .category("Test Etat Pont Rouge Fermé")
                .code("Test Etat Pont Rouge Fermé_2025-07-15T03:30:00Z")
                .apparitiondate(Date.from(Instant.parse("2025-07-15T03:30:00Z")))
                .externalids(Map.of("grafana", "Test Etat Pont Rouge Fermé"))
                .name("Test Etat Pont Rouge Fermé")
                .source("grafana")
                .status("Unapproved")
                .build();

        CrudOperation<Model> crudOperation = defaultGrafanaHandler.executeGrafanaAlertIntoUnApproved(List.of(grafanaAlertDetail), List.of(notifEvent));
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().isEqualTo(expectedNotifEvent);
        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
    }
}