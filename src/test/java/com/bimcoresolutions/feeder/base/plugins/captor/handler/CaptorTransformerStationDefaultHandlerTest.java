package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.TransformerStationApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.transformerstation.TransformerStationDefault;
import com.bimcoresolutions.feeder.base.plugins.captor.model.transformerstation.TransformerStationDefaultCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.TransformerStation;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorTransformerStationDefaultHandlerTest extends BimTest {
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final String CAPTOR = "captor";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    AlarmApiClient alarmApiClient;
    TransformerStationApiClient transformerStationApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorTransformerStationDefaultHandler captorTransformerStationDefaultHandler;

    @BeforeEach
    void init() {
        CMSender cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        transformerStationApiClient = mock(TransformerStationApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorTransformerStationDefaultHandler = new CaptorTransformerStationDefaultHandler(bimApiClient, transformerStationApiClient, objectMapper, cmSender, captorUtils, alarmApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_transformerStationdefault() {
        Map<String, Object> ids = Map.of(CAPTOR, "ELP203");
        TransformerStation transformerStation = new TransformerStation().toBuilder().externalids(ids).build();

        List<String> externalIds = List.of("ELP203");

        Alarm alarmTransformerStation = new Alarm().toBuilder()
                .code("ELP203error")
                .externalids(ids)
                ._id_bimcore("ID_ALARM")
                .build();

        //mock
        when(transformerStationApiClient.getTransformerStationByExternalIds(CAPTOR, externalIds)).thenReturn(List.of(transformerStation));
        when(alarmApiClient.getAlarmByExternalIds(CAPTOR, externalIds)).thenReturn(List.of(alarmTransformerStation));

        //when
        String fileBody = fromFileToString("transformerStationDefaultCaptor.json");
        captorTransformerStationDefaultHandler.handle(fileBody);

        verify(transformerStationApiClient).getTransformerStationByExternalIds(CAPTOR, externalIds);
        verify(alarmApiClient).getAlarmByExternalIds(CAPTOR, externalIds);
    }

    @Test
    @SneakyThrows
    void should_create_alarm_transformerStation() {
        Map<String, Object> ids = Map.of(CAPTOR, "ELP203");

        TransformerStation transformerStation = new TransformerStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1transformerStation")
                .inhibition(false)
                .location(point)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .status(0)
                .externalids(ids)
                .build();

        TransformerStation expected = new TransformerStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1transformerStation")
                .inhibition(false)
                .location(point)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .status(1)
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("transformerStationDefaultCaptor.json");
        TransformerStationDefaultCollection data = objectMapper.readValue(fileBody, TransformerStationDefaultCollection.class);
        List<TransformerStationDefault> comingTransformerStationDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorTransformerStationDefaultHandler.treatTransformerStationDefault(
                List.of(transformerStation), comingTransformerStationDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // TransformerStationDefault alarm is created
        assertThat(result.getUpdate()).hasSize(1); // transformerStation's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expected);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_transformerStation_status() {
        Map<String, Object> ids = Map.of(CAPTOR, "ELP203");

        TransformerStation transformerStation = new TransformerStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1transformerStation")
                .inhibition(false)
                .location(point)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .status(-1)
                .externalids(ids)
                .build();


        TransformerStation expectedTransformerStation = new TransformerStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1transformerStation")
                .inhibition(false)
                .location(point)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .status(1)
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("transformerStationDefaultCaptor.json");
        TransformerStationDefaultCollection data = objectMapper.readValue(fileBody, TransformerStationDefaultCollection.class);
        List<TransformerStationDefault> comingTransformerStationDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorTransformerStationDefaultHandler.treatTransformerStationDefault(
                List.of(transformerStation), comingTransformerStationDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // TransformerStationDefault alarm is created
        assertThat(result.getUpdate()).hasSize(1); // transformerStation's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expectedTransformerStation);
        assertThat(result.getDelete()).isEmpty();

    }

    @Test
    @SneakyThrows
    void should_extinguish_alarm_transformerStationdefault() {
        Map<String, Object> ids = Map.of(CAPTOR, "ELP203");

        TransformerStation transformerStation = new TransformerStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1transformerStation")
                .inhibition(false)
                .location(point)
                .name("POSTE 203 PCC ECLUSE FRANCOIS 1ER")
                .status(-1)
                .externalids(ids)
                .build();

        Alarm alarmTransformerStation = new Alarm().toBuilder()
                .code("ELP203error")
                .externalids(Map.of(CAPTOR, "ELP203"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.TRUE)
                .transformerstation(List.of(transformerStation.get_id_bimcore()))
                .build();

        Alarm expectedAlarmTransformerStation = new Alarm().toBuilder()
                .code("ELP203error")
                .externalids(Map.of(CAPTOR, "ELP203"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.FALSE)
                .transformerstation(List.of(transformerStation.get_id_bimcore()))
                .build();

        String fileBody = fromFileToString("transformerStationDefaultCaptor.json");
        TransformerStationDefaultCollection data = objectMapper.readValue(fileBody, TransformerStationDefaultCollection.class);
        List<TransformerStationDefault> comingTransformerStationDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorTransformerStationDefaultHandler.treatTransformerStationDefault(
                List.of(transformerStation), comingTransformerStationDefaults, List.of(alarmTransformerStation));
        CrudOperation<Alarm> resultAlarm = result.get(Alarm.class);

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(2); //transformerStation's status is updated + transformerStationDefault alarm is updated (already exists)
        assertThat(resultAlarm.getUpdate()).hasSize(1);
        assertThat(resultAlarm.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expectedAlarmTransformerStation);
        assertThat(result.getDelete()).isEmpty();
    }

}