package com.bimcoresolutions.feeder.base.plugins.building.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.BuildingApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.MeterApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.building.model.BuildingDP;
import com.bimcoresolutions.feeder.base.plugins.building.util.BuildingUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Building;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Meter;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.n52.jackson.datatype.jts.JtsModule;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class BuildingHandlerTest extends BimTest {
    BuildingHandler buildingHandler;
    BuildingUtils buildingUtils;
    BimApiClient bimApiClient;
    MeterApiClient meterApiClient;
    BuildingApiClient buildingApiClient;
    CMSender cmSender;
    ObjectMapper objectMapper;

    @BeforeEach
    void init() {
        cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        buildingApiClient = mock(BuildingApiClient.class);
        meterApiClient = mock(MeterApiClient.class);
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JtsModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        buildingUtils = new BuildingUtils(bimApiClient, meterApiClient);
        buildingHandler = new BuildingHandler(bimApiClient, objectMapper, cmSender, buildingApiClient, buildingUtils, meterApiClient);
    }

    @Test
    @SneakyThrows
    void should_create_building() {
        String fileBody = fromFileToString("buildings.json");
        List<BuildingDP> buildings = objectMapper.readValue(fileBody, new TypeReference<>() {
        });
        CrudOperation<Model> result = buildingHandler.treatBuildings(buildings, List.of());

        Assertions.assertThat(result.getCreate()).hasSize(1);
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_building_and_set_building_to_meter_and_update_meter() {
        Building building = new Building();
        building.setCode("Arsenal-F1-1-BâtA");
        building.setExternalids(Map.of(BuildingUtils.METER_ID,"C19SE817503"));
        building.set_id_bimcore("test2");

        Meter meter = new Meter();
        meter.setCode("C19SE817503");
        meter.setExternalids(Map.of(BuildingUtils.METER_ID,"C19SE817503"));
        meter.set_id_bimcore("test1");

        when(buildingApiClient.getBuildingsByExternalIds(anyString(),anyList())).thenReturn(List.of(building));
        when(meterApiClient.getMetersByExternalIds(anyString(),anyList())).thenReturn(List.of(meter));

        String fileBody = fromFileToString("buildings.json");
        List<BuildingDP> buildings = objectMapper.readValue(fileBody, new TypeReference<>() {
        });
        CrudOperation<Model> result = buildingHandler.treatBuildings(buildings, List.of(building));
        CrudOperation<Building> resultBuilding = result.get(Building.class);
        CrudOperation<Meter> resultMeter = result.get(Meter.class);

        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).hasSize(2);
        Assertions.assertThat(result.getDelete()).isEmpty();

        Assertions.assertThat(resultBuilding.getUpdate()).hasSize(1);
        Assertions.assertThat(resultMeter.getUpdate()).hasSize(1);
    }

    @Test
    @SneakyThrows
    void should_update_building_and_not_meter() {
        Building building = new Building();
        building.setCode("Arsenal-F1-1-BâtA");
        building.setExternalids(Map.of(BuildingUtils.METER_ID,"C19SE817503"));
        building.set_id_bimcore("test2");

        Meter meter = new Meter();
        meter.setCode("C19SE817503");
        meter.setExternalids(Map.of(BuildingUtils.METER_ID,"C19SE817503"));
        meter.set_id_bimcore("test1");
        meter.setBuildings(List.of(building.get_id_bimcore()));

        when(buildingApiClient.getBuildingsByExternalIds(anyString(),anyList())).thenReturn(List.of(building));
        when(meterApiClient.getMetersByExternalIds(anyString(),anyList())).thenReturn(List.of(meter));

        String fileBody = fromFileToString("buildings.json");
        List<BuildingDP> buildings = objectMapper.readValue(fileBody, new TypeReference<>() {
        });
        CrudOperation<Model> result = buildingHandler.treatBuildings(buildings, List.of(building));
        CrudOperation<Building> resultBuilding = result.get(Building.class);
        CrudOperation<Meter> resultMeter = result.get(Meter.class);

        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).hasSize(1);
        Assertions.assertThat(result.getDelete()).isEmpty();

        Assertions.assertThat(resultBuilding.getUpdate()).hasSize(1);
        Assertions.assertThat(resultMeter.getUpdate()).hasSize(0);
    }

    @Test
    @SneakyThrows
    void should_delete_building() {
        Building building = new Building();
        building.setCode("Arsenal-F1-1-BâtB");

        when(buildingApiClient.getBuildingsByExternalIds(anyString(),anyList())).thenReturn(List.of(building));

        CrudOperation<Model> result = buildingHandler.treatBuildings(List.of(), List.of(building));

        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).hasSize(1);
    }
}
