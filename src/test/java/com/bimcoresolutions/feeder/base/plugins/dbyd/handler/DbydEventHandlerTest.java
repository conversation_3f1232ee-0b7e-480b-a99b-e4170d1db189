package com.bimcoresolutions.feeder.base.plugins.dbyd.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.EventApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.connectorstate.ConnectorStateService;
import com.bimcoresolutions.feeder.base.plugins.dbyd.DbydUtils;
import com.bimcoresolutions.feeder.base.plugins.dbyd.model.DataplatformDbydEvent;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Event;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class DbydEventHandlerTest extends BimTest {
    public static final String DBYD = "dbyd";
    public static final String RANDOM_ID = "random id";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    private DbydEventHandler dbydEventHandler;
    private ConnectorStateService connectorStateServiceMock;
    private EventApiClient eventApiClient;

    @BeforeEach
    void init() {
        connectorStateServiceMock = mock(ConnectorStateService.class);
        BimApiClient bimApiClient = mock(BimApiClient.class);
        eventApiClient = mock(EventApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        DbydUtils dbydUtils = new DbydUtils();
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        dbydEventHandler = new DbydEventHandler(bimApiClient, eventApiClient, objectMapper, cmSender, dbydUtils, connectorStateServiceMock);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        Event event = new Event().toBuilder().code("myCode").build();
        String fileBody = fromFileToString("eventDbyd.json");

        //mock
        when(eventApiClient.getEventByExternalIds(eq(DBYD), any())).thenReturn(List.of(event));
        when(eventApiClient.getEventCategoriesByLabel(anyString())).thenReturn(List.of("Travaux"));
        when(connectorStateServiceMock.treatConnectorState(anyList(), any())).thenReturn(new CrudOperation<>());

        //when
        dbydEventHandler.handle(fileBody);

        verify(eventApiClient).getEventByExternalIds(eq(DBYD), any());
        verify(eventApiClient).getEventCategoriesByLabel(anyString());
        verify(connectorStateServiceMock).handleConnectorState(any(), anyString());
    }

    @Test
    void should_create_dbyd_event() {
        List<DataplatformDbydEvent> incomingdbydEventDPs = fromFileToObject("eventDbyd.json", new TypeReference<>() {
        });

        //when
        CrudOperation<Model> result = dbydEventHandler.processEvent(List.of(), incomingdbydEventDPs, "Travaux");

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    void should_update_dbyd_event() {
        //given
        Map<String, Object> externalIds = new HashMap<>();
        externalIds.put(DBYD, "12345678");

        List<DataplatformDbydEvent> incomingDbydEventDPs = fromFileToObject(
                "eventDbyd.json",
                new TypeReference<>() {
                });

        Event existingEventToBeUpdated = new Event().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("12345678")
                .externalids(externalIds)
                .label("test")
                .comment("+331234567890 <EMAIL>")
                .realenddate(getDatefromZDTString("2022-05-28T00:00:00.000Z"))
                .source(DBYD)
                .build();

        Event expectedEventUpdate = new Event().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("12345678")
                .externalids(externalIds)
                .source(DBYD)
                .label("12345678")
                .site("line 1 Nanterre IDF 93742 FR")
                .comment("+331234567890 <EMAIL>")
                .creator("John Smith Vinci")
                .creationdate(getDatefromZDTString("2024-05-28T13:15:53.311Z"))
                .realstartdate(getDatefromZDTString("2024-05-28T00:00:00.000Z"))
                .realenddate(getDatefromZDTString("2024-05-28T00:00:00.000Z"))
                .build();

        //when
        CrudOperation<Model> result = dbydEventHandler.processEvent(List.of(existingEventToBeUpdated), incomingDbydEventDPs, "Travaux");

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location", "laststatuschangedate", "status").isEqualTo(expectedEventUpdate);
        assertThat(result.getDelete()).isEmpty();
    }

    private Date getDatefromZDTString(String value) {
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX");
        final ZonedDateTime zonedDateTime = ZonedDateTime.parse(value, formatter);
        return Date.from(zonedDateTime.toInstant());
    }

}