package com.bimcoresolutions.feeder.base.plugins.sigmorbihan.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.SigMorbihanUtils;
import com.bimcoresolutions.feeder.base.plugins.sigmorbihan.model.cabinet.CabinetSigCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class SigMorbihanCabinetHandlerTest extends BimTest {
    SigMorbihanCabinetHandler sigMorbihanCabinetHandler;
    SigMorbihanUtils sigMorbihanUtils;
    BimApiClient bimApiClient;
    CabinetApiClient cabinetApiClient;
    CMSender cmSender;
    ObjectMapper objectMapper;

    @BeforeEach
    void init() {
        cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        cabinetApiClient = mock(CabinetApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        sigMorbihanUtils = new SigMorbihanUtils(bimApiClient);
        sigMorbihanCabinetHandler = new SigMorbihanCabinetHandler(bimApiClient, objectMapper, cmSender, cabinetApiClient, sigMorbihanUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_cabinet() {
        Cabinet cabinet = new Cabinet();
        CrudOperation<Model> crud = mock(CrudOperation.class);
        String extId = "5671";
        cabinet.setExternalids(Map.of(SigMorbihanUtils.MORBIHANSIG, extId));

        when(cabinetApiClient.getCabinetsByExternalIds((anyString()), anyList())).thenReturn(List.of(cabinet));
        Mockito.doNothing().when(cmSender).commit(crud);

        String fileBody = fromFileToString("cabinetsSig.json");
        sigMorbihanCabinetHandler.handle(fileBody);

        Mockito.verify(cabinetApiClient).getCabinetsByExternalIds(anyString(), anyList());
    }


    @Test
    @SneakyThrows
    void should_create_cabinet() {
        String fileBody = fromFileToString("cabinetsSig.json");
        CabinetSigCollection input = objectMapper.readValue(fileBody, CabinetSigCollection.class);
        CrudOperation<Cabinet> result = sigMorbihanCabinetHandler.treatCabinets(input.getFeatures(), List.of());

        Assertions.assertThat(result.getCreate()).hasSize(1);
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_cabinet() {
        Cabinet cabinet = new Cabinet();
        String extId = "9619";
        cabinet.setExternalids(Map.of(SigMorbihanUtils.MORBIHANSIG, extId));
        cabinet.setCode(SigMorbihanUtils.MORBIHANSIG + "_" + extId);

        when(cabinetApiClient.getCabinetsByExternalIds((anyString()), anyList())).thenReturn(List.of(cabinet));

        String fileBody = fromFileToString("cabinetsSig.json");
        CabinetSigCollection input = objectMapper.readValue(fileBody, CabinetSigCollection.class);
        CrudOperation<Cabinet> result = sigMorbihanCabinetHandler.treatCabinets(input.getFeatures(), List.of(cabinet));

        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).hasSize(1);
        Assertions.assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_create_cabinet_talq() {
        when(cabinetApiClient.getCabinetsByExternalIds((anyString()), anyList())).thenReturn(List.of());

        String fileBody = fromFileToString("cabinetsSigTalq.json");
        CabinetSigCollection input = objectMapper.readValue(fileBody, CabinetSigCollection.class);
        CrudOperation<Cabinet> result = sigMorbihanCabinetHandler.treatCabinets(input.getFeatures(), List.of());

        Assertions.assertThat(result.getCreate()).hasSize(1);
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).isEmpty();
        result.getCreate().forEach(cabinet -> {
            Assertions.assertThat(cabinet.getExternalids().get(SigMorbihanUtils.EXT_ID_TALQ)).isEqualTo("BH#-#123456789012345");
            Assertions.assertThat(cabinet.getExternalids().get(SigMorbihanUtils.EXT_ID_TALQ_GENERIC_CONSO)).isEqualTo(List.of("talq_conso_v0¤BH#-#123456789012345"));
        });
    }

    @Test
    @SneakyThrows
    void should_create_cabinet_not_talq() {
        when(cabinetApiClient.getCabinetsByExternalIds((anyString()), anyList())).thenReturn(List.of());

        String fileBody = fromFileToString("cabinetsSig.json");
        CabinetSigCollection input = objectMapper.readValue(fileBody, CabinetSigCollection.class);
        CrudOperation<Cabinet> result = sigMorbihanCabinetHandler.treatCabinets(input.getFeatures(), List.of());

        Assertions.assertThat(result.getCreate()).hasSize(1);
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).isEmpty();
        result.getCreate().forEach(cabinet -> {
            Assertions.assertThat(cabinet.getExternalids().get(SigMorbihanUtils.EXT_ID_TALQ)).isNull();
            Assertions.assertThat(cabinet.getExternalids().get(SigMorbihanUtils.EXT_ID_TALQ_GENERIC_CONSO)).isNull();
        });
    }

    @Test
    @SneakyThrows
    void should_delete_cabinet() {
        Cabinet cabinet = new Cabinet();
        String extId = "9619";
        cabinet.setExternalids(Map.of(SigMorbihanUtils.MORBIHANSIG, extId));
        cabinet.setCode(SigMorbihanUtils.MORBIHANSIG + "_" + extId);
        when(cabinetApiClient.getCabinetsByExternalIds((anyString()), anyList())).thenReturn(List.of(cabinet));

        String fileBody = fromFileToString("cabinetsSigDelete.json");
        CabinetSigCollection input = objectMapper.readValue(fileBody, CabinetSigCollection.class);
        CrudOperation<Cabinet> result = sigMorbihanCabinetHandler.treatCabinets(input.getFeatures(), List.of(cabinet));

        Assertions.assertThat(result.getCreate()).isEmpty();
        Assertions.assertThat(result.getUpdate()).isEmpty();
        Assertions.assertThat(result.getDelete()).hasSize(1);
    }
}