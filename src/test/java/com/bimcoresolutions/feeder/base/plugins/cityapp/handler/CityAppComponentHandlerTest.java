package com.bimcoresolutions.feeder.base.plugins.cityapp.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.DataplatformCityAppComponent;
import com.bimcoresolutions.feeder.base.plugins.cityapp.util.CityAppUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CityAppComponentHandlerTest extends BimTest {
    private static final TypeReference<List<DataplatformCityAppComponent>> typeReferenceComponent = new TypeReference<>() {
    };
    private CityAppComponentHandler cityAppComponentHandler;
    private LightingPointApiClient lightingPointApiClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        lightingPointApiClient = mock(LightingPointApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        CityAppUtils cityAppUtils = new CityAppUtils();
        cityAppComponentHandler = new CityAppComponentHandler(bimApiClient, objectMapper, cmSender, cityAppUtils, lightingPointApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        LightingPoint lightingPoint = new LightingPoint().toBuilder().code("18").build();
        when(lightingPointApiClient.getLightingPointsByExternalIds(anyString(), anyList())).thenReturn(List.of(lightingPoint));

        String fileBody = fromFileToString("cityAppComponents.json");
        cityAppComponentHandler.handle(fileBody);

        verify(lightingPointApiClient).getLightingPointsByExternalIds(anyString(), anyList());
    }

    @Test
    void should_update_cityapp_lightingpoint_component() {
        List<DataplatformCityAppComponent> incomingDataplatformCityAppComponent = fromFileToObject("cityAppComponents.json", typeReferenceComponent);

        LightingPoint existingLightingPoint = new LightingPoint().toBuilder()
                .code("cityapp_15")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(1)
                .forced(0)
                .operatingstate(0)
                .build();

        Map<String, Object> lamps = new HashMap<>();
        if (existingLightingPoint.getLamps() != null) {
            lamps = new HashMap<>(existingLightingPoint.getLamps());
        }
        Map<String, Object> externalIds = Map.of("cityapp", "18");
        Map<String, Object> data = Map.of("model", "WE-EF VFL540 LED 144W", "externalids", externalIds);
        lamps.put("SL552003L1", data);

        existingLightingPoint.setLamps(lamps);

        CrudOperation<Model> crudOperation = cityAppComponentHandler.treatComponentsForLightingPoints(List.of(existingLightingPoint), incomingDataplatformCityAppComponent);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(existingLightingPoint);

    }

}