package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.CommitMetierClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ControllerApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.IntersectionApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.intersection.IntersectionCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Controller;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Intersection;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.idmetier.IMResultBody;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaIntersectionHandlerTest extends BimTest {

    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String ID_BIMCORE_CONTROLLER = "id_bimcore_controller";
    public static final String SIG = "sig";
    public static final String IROAD = "iroad";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    SigHaropaIntersectionHandler sigHaropaIntersectionHandler;
    BimApiClient bimApiClient;
    IntersectionApiClient intersectionApiClient;
    ControllerApiClient controllerApiClient;
    CommitMetierClient commitMetierClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        intersectionApiClient = mock(IntersectionApiClient.class);
        controllerApiClient = mock(ControllerApiClient.class);
        commitMetierClient = mock(CommitMetierClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        sigHaropaIntersectionHandler = new SigHaropaIntersectionHandler(bimApiClient, objectMapper, cmSender, intersectionApiClient, controllerApiClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_intersection_sig() {
        String code = "1intersection";
        Intersection intersection = new Intersection().toBuilder().code(code).build();
        //mock
        PaginatedResponse<Intersection> panelPaginatedResponse = new PaginatedResponse<>(List.of(intersection), new PaginatedResponse.Pagination());
        when(intersectionApiClient.getAllIntersections()).thenReturn(panelPaginatedResponse.getItems());

        //when
        String fileBody = fromFileToString("intersectionSig.json");
        sigHaropaIntersectionHandler.handle(fileBody);
        verify(intersectionApiClient).getAllIntersections();
    }

    /*@Test
    @SneakyThrows
    void should_create_intersection() {
        String fileBody = fromFileToString("intersectionSig.json");
        IntersectionCollection data = objectMapper.readValue(fileBody, IntersectionCollection.class);

        Map<String, Object> ids = Map.of(IROAD, "165", SIG, "10001");
        Intersection expectedIntersection = new Intersection().toBuilder()
                ._id_bimcore("_0")
                .externalids(ids)
                .inhibition(false)
                .status(0)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .controller(List.of(ID_BIMCORE_CONTROLLER))
                .name("C05")
                .site("Avenue 16e Port")
                .build();

        Map<String, Object> idsController = Map.of(IROAD, "16", SIG, "10002");
        Controller controller = new Controller().toBuilder()
                ._id_bimcore(ID_BIMCORE_CONTROLLER)
                .externalids(idsController)
                .location(point)
                .code(SIGHAROPA_ + "10002")
                .name("C05")
                .site("Avenue 16e Port")
                .build();
        Controller expectedController = new Controller().toBuilder()
                ._id_bimcore(ID_BIMCORE_CONTROLLER)
                .externalids(idsController)
                .location(point)
                .code(SIGHAROPA_ + "10002")
                .name("C05")
                .site("Avenue 16e Port")
                .intersections(List.of("_0"))
                .build();

        //when
        when(commitMetierClient.getIds((any()))).thenReturn(Map.of("Intersection", new IMResultBody("", 0, 1000)));
        CrudOperation<Model> result = sigHaropaIntersectionService.treatSigIntersections(List.of(), data.getFeatures(), List.of(controller));

        //then
        CrudOperation<Intersection> resultIntersection = result.get(Intersection.class);
        assertThat(resultIntersection.getCreate()).hasSize(1);
        assertThat(resultIntersection.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate")
                .isEqualTo(expectedIntersection);

        CrudOperation<Controller> resultController = result.get(Controller.class);
        assertThat(resultController.getUpdate()).hasSize(1);
        assertThat(resultController.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate")
                .isEqualTo(expectedController);
        assertThat(result.getDelete()).isEmpty();
    }*/

    @Test
    @SneakyThrows
    void should_update_intersection() {
        Map<String, Object> ids = Map.of(IROAD, "165", SIG, "10001");

        Intersection intersection = new Intersection().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .name("C05")
                .site("Avenue 16e Port")
                .controller(List.of(ID_BIMCORE_CONTROLLER))
                .build();

        Map<String, Object> idsController = Map.of(IROAD, "16", SIG, "10002");
        Controller controller = new Controller().toBuilder()
                ._id_bimcore(ID_BIMCORE_CONTROLLER)
                .externalids(idsController)
                .location(point)
                .code(SIGHAROPA_ + "10002")
                .name("C05")
                .site("Avenue 16e Port")
                .build();

        Controller expectedController = new Controller().toBuilder()
                ._id_bimcore(ID_BIMCORE_CONTROLLER)
                .externalids(idsController)
                .location(point)
                .code(SIGHAROPA_ + "10002")
                .name("C05")
                .site("Avenue 16e Port")
                .intersections(List.of(RANDOM_ID))
                .build();

        String fileBody = fromFileToString("intersectionSig.json");
        IntersectionCollection data = objectMapper.readValue(fileBody, IntersectionCollection.class);

        //when
        when(commitMetierClient.getIds(any())).thenReturn(Map.of("Intersection", new IMResultBody("", 0, 1000)));
        CrudOperation<Model> result = sigHaropaIntersectionHandler.treatSigIntersections(List.of(intersection), data.getFeatures(), List.of(controller));

        //then
        CrudOperation<Intersection> resultIntersection = result.get(Intersection.class);
        CrudOperation<Controller> resultController = result.get(Controller.class);
        assertThat(result.getUpdate()).hasSize(2);
        AssertionsForClassTypes.assertThat(resultController.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "installationdate", "location").isEqualTo(expectedController);
        AssertionsForClassTypes.assertThat(resultIntersection.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "installationdate", "location").isEqualTo(intersection);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_intersection() {
        Map<String, Object> ids = Map.of(IROAD, "165", SIG, "10001");

        Intersection intersection = new Intersection().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaIntersectionHandler.treatSigIntersections(List.of(intersection), List.of(), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(intersection.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}