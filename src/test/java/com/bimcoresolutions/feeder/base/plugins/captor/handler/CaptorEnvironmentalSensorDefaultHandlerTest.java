package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.EnvironmentalSensorApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.environmentalsensordefault.EnvironmentalSensorDefault;
import com.bimcoresolutions.feeder.base.plugins.captor.model.environmentalsensordefault.EnvironmentalSensorDefaultCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.EnvironmentalSensor;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorEnvironmentalSensorDefaultHandlerTest extends BimTest {
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final String CAPTOR = "captor";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    AlarmApiClient alarmApiClient;
    EnvironmentalSensorApiClient environmentalSensorApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorEnvironmentalSensorDefaultHandler captorEnvironmentalSensorDefaultHandler;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        environmentalSensorApiClient = mock(EnvironmentalSensorApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorEnvironmentalSensorDefaultHandler = new CaptorEnvironmentalSensorDefaultHandler(bimApiClient, objectMapper, cmSender, captorUtils, alarmApiClient,
                environmentalSensorApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_environmentalsensordefault() {
        Map<String, Object> ids = Map.of("captor", "P2000");
        EnvironmentalSensor environmentalSensor = new EnvironmentalSensor().toBuilder().externalids(ids).type("maree").build();

        List<String> externalIds = List.of("P2000");

        Alarm alarmEnvironmentalSensor = new Alarm().toBuilder()
                .code("P2000mareeerror")
                .externalids(ids)
                ._id_bimcore("ID_ALARM")
                .build();

        //mock
        when(environmentalSensorApiClient.getEnvironmentalSensorByExternalsIds("captor", externalIds)).thenReturn(List.of(environmentalSensor));
        when(alarmApiClient.getAlarmByExternalIds("captor", externalIds)).thenReturn(List.of(alarmEnvironmentalSensor));

        //when
        String fileBody = fromFileToString("environmentalSensorDefaultCaptor_alarm.json");
        captorEnvironmentalSensorDefaultHandler.handle(fileBody);

        verify(environmentalSensorApiClient).getEnvironmentalSensorByExternalsIds("captor", externalIds);
        verify(alarmApiClient).getAlarmByExternalIds("captor", externalIds);
    }

    @Test
    @SneakyThrows
    void should_create_alarm_environmentalsensordefault_message() {
        Map<String, Object> ids = Map.of(CAPTOR, "P2000");

        EnvironmentalSensor environmentalSensor = new EnvironmentalSensor().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1environmentalsensor")
                .inhibition(false)
                .location(point)
                .name("P2000")
                .status(0)
                .externalids(ids)
                .type("maree")
                .build();

        EnvironmentalSensor expected = new EnvironmentalSensor().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1environmentalsensor")
                .inhibition(false)
                .location(point)
                .name("P2000")
                .status(1)
                .externalids(ids)
                .type("maree")
                .build();

        String fileBody = fromFileToString("environmentalSensorDefaultCaptor_alarm.json");
        EnvironmentalSensorDefaultCollection data = objectMapper.readValue(fileBody, EnvironmentalSensorDefaultCollection.class);
        List<EnvironmentalSensorDefault> comingEnvironmentalSensorDefaults = data.getItems().stream().peek(c -> {
            if (c.getType().equalsIgnoreCase("hauteur")) {
                c.setType("maree");
            }
        }).toList();

        //when
        CrudOperation<Model> result = captorEnvironmentalSensorDefaultHandler.treatEnvironmentalSensorDefault(
                List.of(environmentalSensor), comingEnvironmentalSensorDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // EnvironmentalSensorDefault alarm is created
        assertThat(result.getUpdate()).hasSize(1); // environmentalSensor's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expected);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_alarm_environmentalsensordefault_message() {
        Map<String, Object> ids = Map.of(CAPTOR, "P2000");

        EnvironmentalSensor environmentalSensor = new EnvironmentalSensor().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1environmentalsensor")
                .inhibition(false)
                .location(point)
                .name("P2000")
                .status(0)
                .externalids(ids)
                .type("maree")
                .build();

        Alarm alarmRiverStructure = new Alarm().toBuilder()
                .code("P2000mareeerror")
                .externalids(Map.of(CAPTOR, "P2000"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.TRUE)
                .environmentalsensor(List.of(environmentalSensor.get_id_bimcore()))
                .build();

        String fileBody = fromFileToString("environmentalSensorDefaultCaptor_alarm.json");
        EnvironmentalSensorDefaultCollection data = objectMapper.readValue(fileBody, EnvironmentalSensorDefaultCollection.class);
        List<EnvironmentalSensorDefault> comingEnvironmentalSensorDefaults = data.getItems().stream().peek(c -> {
            if (c.getType().equalsIgnoreCase("hauteur")) {
                c.setType("maree");
            }
        }).toList();

        //when
        CrudOperation<Model> result = captorEnvironmentalSensorDefaultHandler.treatEnvironmentalSensorDefault(
                List.of(environmentalSensor), comingEnvironmentalSensorDefaults, List.of(alarmRiverStructure));

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(2); // environmentalSensor's status is updated + environmentalSensorDefault alarm is updated (already exists)
        assertThat(result.getDelete()).isEmpty();
    }

}