package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.railway.RailwayCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.RailwayGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Railway;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailwayCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaRailWayHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String CAPTOR = "captor";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    SigHaropaRailWayHandler sigHaropaRailWayHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    RailwayGenFeignClient railwayGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        railwayGenFeignClient = mock(RailwayGenFeignClient.class);
        sigHaropaRailWayHandler = new SigHaropaRailWayHandler(bimApiClient, objectMapper, cmSender, railwayGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_railway_sig() {
        String code = "1railway";
        Railway railway = new Railway().toBuilder().code(code).build();

        //mock
        PaginatedResponse<Railway> panelPaginatedResponse = new PaginatedResponse<>(List.of(railway), new PaginatedResponse.Pagination());
        when(railwayGenFeignClient.get(new RailwayCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("railwaySig.json");
        sigHaropaRailWayHandler.handle(fileBody);
        verify(railwayGenFeignClient).get(new RailwayCriteria(), null, null, null, null, null);
    }

    @Test
    @SneakyThrows
    void should_create_railway() {
        String fileBody = fromFileToString("railwaySig.json");
        RailwayCollection data = objectMapper.readValue(fileBody, RailwayCollection.class);

        Map<String, Object> ids = Map.of(CAPTOR, "10001", SIG, "10001");
        Railway railway = new Railway().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("10001")
                .operatingstate(0)
                .status(0)
                .externalids(ids)
                .type("railway")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaRailWayHandler.treatSigRailWays(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(railway);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_railway() {
        Map<String, Object> ids = Map.of(CAPTOR, "10001", SIG, "10001");
        Railway railway = new Railway().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("10001")
                .operatingstate(0)
                .site("Route du Mole Central")
                .status(0)
                .externalids(ids)
                .type("railway")
                .build();

        String fileBody = fromFileToString("railwaySig.json");
        RailwayCollection data = objectMapper.readValue(fileBody, RailwayCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaRailWayHandler.treatSigRailWays(List.of(railway), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(railway);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_railway() {
        Map<String, Object> ids = Map.of(CAPTOR, "10001", SIG, "10001");
        Railway railway = new Railway().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("10001")
                .operatingstate(0)
                .site("Route du Mole Central")
                .status(0)
                .externalids(ids)
                .type("railway")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaRailWayHandler.treatSigRailWays(List.of(railway), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(railway.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}