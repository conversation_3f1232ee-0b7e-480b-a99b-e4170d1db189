package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.RailCrossingApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.RailwayApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.rail.railstate.RailState;
import com.bimcoresolutions.feeder.base.plugins.captor.model.rail.railstate.RailStateCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailCrossing;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Railway;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class CaptorRailStateHandlerTest extends BimTest {
    public static final String CAPTOR = "captor";
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    public static final LineString line_string = new GeometryFactory().createLineString(new Coordinate[]{
            new Coordinate(1.09d, 49.45d),
            new Coordinate(1.19d, 49.55d),
            new Coordinate(1.29d, 49.65d)
    });
    BimApiClient bimApiClient;
    RailwayApiClient railwayApiClient;
    RailCrossingApiClient railCrossingApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorRailStateHandler captorRailStateHandler;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        railwayApiClient = mock(RailwayApiClient.class);
        railCrossingApiClient = mock(RailCrossingApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorRailStateHandler = new CaptorRailStateHandler(bimApiClient, objectMapper, cmSender, captorUtils, railwayApiClient, railCrossingApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_railstate() {
        Map<String, Object> railway_external_ids = Map.of("captor", "PN302");
        Railway railway = new Railway().toBuilder().externalids(railway_external_ids).build();

        Map<String, Object> railCrossing_external_ids = Map.of("captor", "PN302");
        RailCrossing railCrossing = new RailCrossing().toBuilder().externalids(railCrossing_external_ids).build();

        Set<Model> toUp = Set.of(railway, railCrossing);
        CrudOperation<Model> crudOperation = new CrudOperation<>(Set.of(), toUp, Set.of());

        List<String> externalIds = List.of("PN302", "RFPZ1451");

        //mock
        when(railwayApiClient.getRailwayByExternalIds("captor", externalIds)).thenReturn(List.of(railway));
        when(railCrossingApiClient.getRailCrossingByExternalIds("captor", externalIds)).thenReturn(List.of(railCrossing));

        //when
        String fileBody = fromFileToString("railStateCaptor.json");
        captorRailStateHandler.handle(fileBody);

        verify(railwayApiClient).getRailwayByExternalIds("captor", externalIds);
        verify(railCrossingApiClient).getRailCrossingByExternalIds("captor", externalIds);
    }

    @Test
    @SneakyThrows
    void should_update_railwaystate() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ1451");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN302");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ1451")
                .status(1)
                .operatingstate(1)
                .externalids(ids_railway)
                .type("railway")
                .build();

        Railway expected_railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ1451")
                .status(1)
                .operatingstate(0)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN302")
                .status(0)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        String fileBody = fromFileToString("railStateCaptor_railway.json");
        RailStateCollection data = objectMapper.readValue(fileBody, RailStateCollection.class);

        List<RailState> comingRailState = data.getItems();

        //when
        CrudOperation<Model> result = captorRailStateHandler.treatRailState(List.of(railway), List.of(railCrossing), comingRailState);

        //then
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location").isEqualTo(expected_railway);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_railcrossing_state() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ1451");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN302");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ1451")
                .status(0)
                .operatingstate(0)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN302")
                .status(0)
                .operatingstate(0)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        RailCrossing expected_railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN302")
                .status(0)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        String fileBody = fromFileToString("railStateCaptor_railCrossing.json");
        RailStateCollection data = objectMapper.readValue(fileBody, RailStateCollection.class);

        List<RailState> comingRailState = data.getItems();

        //when
        CrudOperation<Model> result = captorRailStateHandler.treatRailState(List.of(railway), List.of(railCrossing), comingRailState);

        //then
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(expected_railCrossing);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_railway_and_railcrossing_state() {
        Map<String, Object> ids_railway = Map.of(CAPTOR, "RFPZ1451");
        Map<String, Object> ids_railcross = Map.of(CAPTOR, "PN302");

        Railway railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ1451")
                .status(0)
                .operatingstate(1)
                .externalids(ids_railway)
                .type("railway")
                .build();

        Railway expected_railway = new Railway().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railway")
                .inhibition(false)
                .location(line_string)
                .name("RFPZ1451")
                .status(0)
                .operatingstate(0)
                .externalids(ids_railway)
                .type("railway")
                .build();

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN302")
                .status(0)
                .operatingstate(0)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        RailCrossing expected_railCrossing = new RailCrossing().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1railcrossing")
                .inhibition(false)
                .location(point)
                .name("PN302")
                .status(0)
                .operatingstate(1)
                .externalids(ids_railcross)
                .type("railcross")
                .build();

        String fileBody = fromFileToString("railStateCaptor.json");
        RailStateCollection data = objectMapper.readValue(fileBody, RailStateCollection.class);

        List<RailState> comingRailState = data.getItems();

        //when
        CrudOperation<Model> result = captorRailStateHandler.treatRailState(List.of(railway), List.of(railCrossing), comingRailState);

        //then
        assertThat(result.getUpdate()).hasSize(2);

        CrudOperation<Railway> railwayCrudOperation = result.get(Railway.class);
        assertThat(railwayCrudOperation.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(expected_railway);

        CrudOperation<RailCrossing> railcrossingCrudOperation = result.get(RailCrossing.class);
        assertThat(railcrossingCrudOperation.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(expected_railCrossing);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

}