package com.bimcoresolutions.feeder.base.plugins.camtrace.model;

import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static com.bimcoresolutions.feeder.base.plugins.camtrace.handler.CamtraceAlertHandler.CAMTRACE;
import static org.assertj.core.api.Assertions.assertThat;

public class CamtraceAlertTest {

    @Nested
    class ToCamtraceAlarms {

        @Test
        void should_map() {
            // Given
            CamtraceAlert input = CamtraceAlert.builder()
                    .id(1L)
                    .name("myName")
                    .active(true)
                    .errors(List.of(
                            new CamtraceAlert.CamtraceError("mosaic", "Not connected"),
                            new CamtraceAlert.CamtraceError("rtsp", "Not connected")))
                    .build();

            Map<String, CamtraceAlarm> expected = Map.of(
                    CAMTRACE + "_1_mosaic",
                    new CamtraceAlarm(1L, "myName", true, "mosaic", "Not connected"),
                    CAMTRACE + "_1_rtsp",
                    new CamtraceAlarm(1L, "myName", true, "rtsp", "Not connected")
                    );

            // When
            Map<String, CamtraceAlarm> result = input.toCamtraceAlarms();

            // Then
            assertThat(result).isEqualTo(expected);
        }

    }


}
