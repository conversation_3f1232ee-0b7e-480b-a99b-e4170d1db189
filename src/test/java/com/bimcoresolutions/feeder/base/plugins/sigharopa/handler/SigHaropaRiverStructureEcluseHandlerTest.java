package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.riverstructure.RiverStructureCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.service.SigHaropaRiverStructureService;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.RiverStructureGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RiverStructure;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static org.mockito.Mockito.*;

class SigHaropaRiverStructureEcluseHandlerTest extends BimTest {
    SigHaropaRiverStructureEcluseHandler sigHaropaRiverStructureEcluseHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    RiverStructureGenFeignClient riverStructureGenFeignClient;
    SigHaropaRiverStructureService sigHaropaRiverStructureService;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        riverStructureGenFeignClient = mock(RiverStructureGenFeignClient.class);
        sigHaropaRiverStructureService = mock(SigHaropaRiverStructureService.class);
        sigHaropaRiverStructureEcluseHandler = new SigHaropaRiverStructureEcluseHandler(bimApiClient, objectMapper, cmSender, riverStructureGenFeignClient, sigHaropaRiverStructureService);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_riverstructure_pont_sig() {
        String code = "1riverstructure";
        RiverStructure riverStructure = new RiverStructure().toBuilder().code(code).type("pont").build();
        Set<Model> toAdd = Set.of(riverStructure);
        Set<Model> toUp = Set.of(riverStructure);
        CrudOperation<Model> crudOperation = new CrudOperation<>(toAdd, toUp, Set.of());

        //mock
        PaginatedResponse<RiverStructure> panelPaginatedResponse = new PaginatedResponse<>(List.of(riverStructure), new PaginatedResponse.Pagination());
        when(riverStructureGenFeignClient.get(any(), any(), any(), any(), any(), any())).thenReturn(panelPaginatedResponse);
        when(sigHaropaRiverStructureService.treatSigRiverStructures(anyList(), anyList(), anyString())).thenReturn(crudOperation);

        //when
        String fileBody = fromFileToString("riverStructureSig_pont.json");
        sigHaropaRiverStructureEcluseHandler.handle(fileBody);

        RiverStructureCollection dataPont = objectMapper.readValue(fileBody, RiverStructureCollection.class);
        dataPont.getFeatures().forEach(comingRiverStructure -> comingRiverStructure.setTypeObject(dataPont.getTypeObject()));

        verify(riverStructureGenFeignClient).get(any(), any(), any(), any(), any(), any());
        verify(sigHaropaRiverStructureService).treatSigRiverStructures(List.of(riverStructure), dataPont.getFeatures(), "pont");
    }

    @Test
    @SneakyThrows
    void should_treat_messages_riverstructure_ecluse_sig() {
        String code = "1riverstructure";
        RiverStructure riverStructure = new RiverStructure().toBuilder().code(code).type("ecluse").build();
        Set<Model> toAdd = Set.of(riverStructure);
        Set<Model> toUp = Set.of(riverStructure);
        CrudOperation<Model> crudOperation = new CrudOperation<>(toAdd, toUp, Set.of());

        //mock
        PaginatedResponse<RiverStructure> panelPaginatedResponse = new PaginatedResponse<>(List.of(riverStructure), new PaginatedResponse.Pagination());
        when(riverStructureGenFeignClient.get(any(), any(), any(), any(), any(), any())).thenReturn(panelPaginatedResponse);
        when(sigHaropaRiverStructureService.treatSigRiverStructures(anyList(), anyList(), anyString())).thenReturn(crudOperation);

        String fileBody = fromFileToString("riverStructureSig_ecluse.json");
        sigHaropaRiverStructureEcluseHandler.handle(fileBody);

        RiverStructureCollection dataEcluse = objectMapper.readValue(fileBody, RiverStructureCollection.class);
        dataEcluse.getFeatures().forEach(comingRiverStructure -> comingRiverStructure.setTypeObject(dataEcluse.getTypeObject()));

        verify(riverStructureGenFeignClient).get(any(), any(), any(), any(), any(), any());
        verify(sigHaropaRiverStructureService).treatSigRiverStructures(List.of(riverStructure), dataEcluse.getFeatures(), "ecluse");
    }
}