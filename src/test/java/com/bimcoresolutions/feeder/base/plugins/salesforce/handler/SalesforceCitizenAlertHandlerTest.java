package com.bimcoresolutions.feeder.base.plugins.salesforce.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ReportApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.connectorstate.ConnectorStateService;
import com.bimcoresolutions.feeder.base.plugins.salesforce.SalesforceUtils;
import com.bimcoresolutions.feeder.base.plugins.salesforce.model.DataplatformSalesforceCitizenAlert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Report;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class SalesforceCitizenAlertHandlerTest extends BimTest {
    public static final String SALESFORCE = "salesforce";
    public static final String RANDOM_ID = "random id";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    private SalesforceCitizenAlertHandler salesforceCitizenAlertHandler;
    private ConnectorStateService connectorStateServiceMock;
    private ReportApiClient reportApiClient;

    @BeforeEach
    void init() {
        SalesforceUtils salesforceUtils = new SalesforceUtils();
        connectorStateServiceMock = mock(ConnectorStateService.class);
        BimApiClient bimApiClient = mock(BimApiClient.class);
        reportApiClient = mock(ReportApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        salesforceCitizenAlertHandler = new SalesforceCitizenAlertHandler(bimApiClient, objectMapper, cmSender, reportApiClient, salesforceUtils, connectorStateServiceMock);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        Report report = new Report().toBuilder().code("myCode").build();
        String fileBody = fromFileToString("citizenAlertSalesforce.json");

        //mock
        when(connectorStateServiceMock.treatConnectorState(anyList(), any())).thenReturn(new CrudOperation<>());
        when(reportApiClient.getReportByExternalIds(eq(SALESFORCE), any())).thenReturn(List.of(report));

        //when
        salesforceCitizenAlertHandler.handle(fileBody);

        verify(reportApiClient).getReportByExternalIds(eq(SALESFORCE), any());
        verify(connectorStateServiceMock).handleConnectorState(any(), anyString());
    }

    @Test
    void should_create_salesforce_report() {
        //given
        List<DataplatformSalesforceCitizenAlert> incomingSalesforceCitizenAlertDPs = fromFileToObject("citizenAlertSalesforce.json", new TypeReference<>() {
        });

        //when
        CrudOperation<Model> result = salesforceCitizenAlertHandler.treatReport(List.of(), incomingSalesforceCitizenAlertDPs);

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    void should_update_salesforce_report() {
        //given
        Map<String, Object> externalIds = new HashMap<>();
        externalIds.put(SALESFORCE, "a5522255dfdef5f5e5f5fe");

        List<DataplatformSalesforceCitizenAlert> incomingSalesforceCitizenAlertDPs = fromFileToObject("citizenAlertSalesforce.json", new TypeReference<>() {
        });

        Report existingReportToBeUpdated = new Report().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("a5522255dfdef5f5e5f5fe")
                .externalids(externalIds)
                .address("1 Rue de la République, 82000 Montauban")
                .comment("to be updated")
                .source(SALESFORCE)
                .build();

        Map<String, Object> expectedDocuments = Map.of("url", "url_ici");

        Report expectedReportUpdate = new Report().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("a5522255dfdef5f5e5f5fe")
                .name("101")
                .externalids(externalIds)
                .location(point)
                .priority(1)
                .reportdate(Date.from(Instant.parse("2021-10-20T13:53:54.515Z")))
                .metier(Map.of("metiers", List.of("EP")))
                .comment("mat à remplacer")
                .documents(Map.of("documents", List.of(expectedDocuments)))
                .status("New")
                .address("1 Rue de la République, 82000 Montauban")
                .laststatuschangedate(new Date())
                .source(SALESFORCE)
                .build();

        //when
        CrudOperation<Model> result = salesforceCitizenAlertHandler.treatReport(List.of(existingReportToBeUpdated), incomingSalesforceCitizenAlertDPs);

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location", "laststatuschangedate").isEqualTo(expectedReportUpdate);
        assertThat(result.getDelete()).isEmpty();
    }

}