package com.bimcoresolutions.feeder.base.plugins.interact.handler;

import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.interact.model.InteractDataplatformAsset;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

class InteractAssetHandlerTest {
    public static final String INTERACT = "interact";
    public static final String CITYAPP = "cityapp";
    private InteractAssetHandler interactAssetHandler;

    @BeforeEach
    void init() {
        CMSender cmSender = mock(CMSender.class);
        BimApiClient bimApiClient = mock(BimApiClient.class);
        LightingPointApiClient lightingPointApiClient = mock(LightingPointApiClient.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        interactAssetHandler = new InteractAssetHandler(bimApiClient, lightingPointApiClient, objectMapper, cmSender);
    }

    @Test
    void check_if_lightingpoint_contains_externalids() {
        InteractDataplatformAsset interactDataplatformAsset = new InteractDataplatformAsset().toBuilder()
                .externalAssetId("123")
                .codeCityApp("A65-24")
                .build();

        Map<String, Object> externalIds = new HashMap<>();
        externalIds.put(INTERACT, "123");

        LightingPoint existingLightingPoint = LightingPoint.builder()
                .code("myCode")
                .name("A65-24")
                .externalids(externalIds)
                .build();

        externalIds.put(CITYAPP, "456");
        LightingPoint expected = LightingPoint.builder()
                .code("myCode")
                .name("A65-24")
                .externalids(externalIds)
                .build();

        CrudOperation<Model> result = interactAssetHandler.treatLightingPointsAssets(List.of(interactDataplatformAsset), List.of(existingLightingPoint));

        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().isEqualTo(expected);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    void check_if_lightingpoint_doesnt_contain_externalids() {
        InteractDataplatformAsset interactDataplatformAsset = new InteractDataplatformAsset().toBuilder()
                .externalAssetId("123")
                .codeCityApp("A65-24")
                .build();

        Map<String, Object> externalIds = new HashMap<>();
        externalIds.put(INTERACT, "123");

        LightingPoint existingLightingPoint = LightingPoint.builder()
                .code("myCode")
                .name("A65-25")
                .externalids(externalIds)
                .build();

        CrudOperation<Model> result = interactAssetHandler.treatLightingPointsAssets(List.of(interactDataplatformAsset), List.of(existingLightingPoint));

        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }
}
