package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.environmentalsensor.EnvironmentalSensorCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.EnvironmentalSensorGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.EnvironmentalSensor;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.EnvironmentalSensorCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaEnvironmentalSensorHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String CAPTOR = "captor";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    SigHaropaEnvironmentalSensorHandler sigHaropaEnvironmentalSensorHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    EnvironmentalSensorGenFeignClient environmentalSensorGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        environmentalSensorGenFeignClient = mock(EnvironmentalSensorGenFeignClient.class);
        sigHaropaEnvironmentalSensorHandler = new SigHaropaEnvironmentalSensorHandler(bimApiClient, objectMapper, cmSender, environmentalSensorGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_environmentalsensor_sig() {
        String code = "1environmentalsensor";
        EnvironmentalSensor environmentalSensor = new EnvironmentalSensor().toBuilder().code(code).build();

        //mock
        PaginatedResponse<EnvironmentalSensor> panelPaginatedResponse = new PaginatedResponse<>(List.of(environmentalSensor), new PaginatedResponse.Pagination());
        when(environmentalSensorGenFeignClient.get(new EnvironmentalSensorCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("environmentalSensorSig.json");
        sigHaropaEnvironmentalSensorHandler.handle(fileBody);
        verify(environmentalSensorGenFeignClient).get(new EnvironmentalSensorCriteria(), null, null, null, null, null);
    }


    @Test
    @SneakyThrows
    void should_create_environmentalsensor() {
        String fileBody = fromFileToString("environmentalSensorSig.json");
        EnvironmentalSensorCollection data = objectMapper.readValue(fileBody, EnvironmentalSensorCollection.class);

        Map<String, Object> ids = Map.of(CAPTOR, "Antifer", SIG, "10001", "dp¤generic_dyn-height-environmentalsensor_v0", "atmo_sensor-height_v0¤Antifer");
        EnvironmentalSensor environmentalSensor = new EnvironmentalSensor().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("TANCA_MAR_4")
                .status(0)
                .externalids(ids)
                .type("maree")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaEnvironmentalSensorHandler.treatSigEnvironmentalSensors(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(environmentalSensor);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_environmentalsensor() {
        Map<String, Object> ids = Map.of(CAPTOR, "Antifer", SIG, "10001", "dp¤generic_dyn-height-environmentalsensor_v0", "atmo_sensor-height_v0¤Antifer");

        EnvironmentalSensor environmentalSensor = new EnvironmentalSensor().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("TANCA_MAR_4")
                .status(0)
                .externalids(ids)
                .type("maree")
                .build();

        String fileBody = fromFileToString("environmentalSensorSig.json");
        EnvironmentalSensorCollection data = objectMapper.readValue(fileBody, EnvironmentalSensorCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaEnvironmentalSensorHandler.treatSigEnvironmentalSensors(List.of(environmentalSensor), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(environmentalSensor);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_environmentalsensor() {
        Map<String, Object> ids = Map.of(CAPTOR, "", SIG, "10001");

        EnvironmentalSensor environmentalSensor = new EnvironmentalSensor().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("TANCA_MAR_4")
                .status(0)
                .externalids(ids)
                .type("Capteur Environnement")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaEnvironmentalSensorHandler.treatSigEnvironmentalSensors(List.of(environmentalSensor), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(environmentalSensor.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}