package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.RiverStructureApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructure.state.RiverStructureState;
import com.bimcoresolutions.feeder.base.plugins.captor.model.riverstructure.riverstructure.state.RiverStructureStateCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RiverStructure;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorRiverStructureStateHandlerTest extends BimTest {
    public static final String CAPTOR = "captor";
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    RiverStructureApiClient riverStructureApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorRiverStructureStateHandler captorRiverStructureStateHandler;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        riverStructureApiClient = mock(RiverStructureApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorRiverStructureStateHandler = new CaptorRiverStructureStateHandler(bimApiClient, objectMapper, cmSender, captorUtils, riverStructureApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_riverstructurestate() {
        Map<String, Object> external_ids = Map.of(CAPTOR, "PONT_ROUGE");
        RiverStructure riverStructure = new RiverStructure().toBuilder().externalids(external_ids).build();

        List<String> externalIds = List.of("PONT_ROUGE");

        //mock
        when(riverStructureApiClient.getRiverStructureByExternalsIds("captor", externalIds)).thenReturn(List.of(riverStructure));

        //when
        String fileBody = fromFileToString("riverStructureState.json");
        captorRiverStructureStateHandler.handle(fileBody);

        verify(riverStructureApiClient).getRiverStructureByExternalsIds(CAPTOR, externalIds);
    }

    @Test
    @SneakyThrows
    void should_update_riverstructurestate() {
        Map<String, Object> ids = Map.of(CAPTOR, "PONT_ROUGE");

        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1riverstructure")
                .inhibition(false)
                .location(point)
                .name("de Tancarville (ancienne)")
                .status(0)
                .operatingstate(1)
                .lockstate(1)
                .externalids(ids)
                .type("Pont")
                .build();

        RiverStructure expected = new RiverStructure().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1riverstructure")
                .inhibition(false)
                .location(point)
                .name("de Tancarville (ancienne)")
                .status(0)
                .operatingstate(0)
                .lockstate(0)
                .externalids(ids)
                .type("Pont")
                .build();

        String fileBody = fromFileToString("riverStructureState.json");
        RiverStructureStateCollection data = objectMapper.readValue(fileBody, RiverStructureStateCollection.class);

        List<RiverStructureState> comingRiverStructureState = data.getItems();

        //when
        CrudOperation<Model> result = captorRiverStructureStateHandler.treatRiverStructureState(
                List.of(riverStructure), comingRiverStructureState);

        //then
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location", "lastupdate").isEqualTo(expected);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

}