package com.bimcoresolutions.feeder.base.plugins.mivisu.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.DynPanelApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.mivisu.MivisuUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.DynPanel;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.assertj.core.data.MapEntry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.nio.charset.Charset;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;

class MivisuEntityHandlerTest extends BimTest {
    private MivisuEntityHandler handler;
    private BimApiClient bimApiClient;
    private DynPanelApiClient dynPanelApiClient;
    private CMSender cmSender;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        dynPanelApiClient = mock(DynPanelApiClient.class);
        AlarmApiClient alarmApiClient = mock(AlarmApiClient.class);
        cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        MivisuUtils mivisuUtils = new MivisuUtils();
        handler = new MivisuEntityHandler(bimApiClient, objectMapper, cmSender, mivisuUtils, alarmApiClient, dynPanelApiClient);
    }

    @Test
    @SneakyThrows
    void should_process_mivisu_message() {
        String fileAsString = IOUtils.toString(Objects.requireNonNull(this.getClass().getResource("mivisu_message.json")), Charset.defaultCharset());

        DynPanel dynPanel = DynPanel.builder()
                .externalids(Map.ofEntries(MapEntry.entry("mivisu", "europe")))
                .messages(new HashMap<>())
                .status(1)
                .build();
        Mockito.when(dynPanelApiClient.getDynPanelByExternalIds(Mockito.anyString(), Mockito.anyList())).thenReturn(List.of(dynPanel));
        handler.handle(fileAsString);
        Mockito.verify(dynPanelApiClient).getDynPanelByExternalIds(Mockito.anyString(), Mockito.anyList());
        Mockito.verify(cmSender).commitWithRelations(any(), any());
    }

    @Test
    @SneakyThrows
    void should_process_mivisu_unknown_dynpanel() {
        String fileAsString = IOUtils.toString(Objects.requireNonNull(this.getClass().getResource("mivisu_message.json")), Charset.defaultCharset());

        DynPanel dynPanel = DynPanel.builder()
                .externalids(Map.ofEntries(MapEntry.entry("mivisu", "test_unknown_dynpanel")))
                .build();
        Mockito.when(dynPanelApiClient.getDynPanelByExternalIds(Mockito.anyString(), Mockito.anyList())).thenReturn(List.of(dynPanel));
        handler.handle(fileAsString);
        Mockito.verify(dynPanelApiClient).getDynPanelByExternalIds(Mockito.anyString(), Mockito.anyList());
        Mockito.verify(cmSender, Mockito.times(0)).commit(Mockito.<CrudOperation<Model>>any());
    }
}
