package com.bimcoresolutions.feeder.base.plugins.monvillage.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.PacmanClient;
import com.bimcoresolutions.feeder.base.client.bimcity.NotifEventApiClient;
import com.bimcoresolutions.feeder.base.client.pacman.PacManAction;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.monvillage.MonVillageUtils;
import com.bimcoresolutions.feeder.base.plugins.monvillage.model.TalqCabinetActionTemplate;
import com.bimcoresolutions.feeder.base.plugins.monvillage.model.TalqCabinetActionTemplateRelais;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.NotifEvent;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import java.util.List;
import java.util.Map;

import static java.util.Map.entry;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class MonVillageCommandHandlerTest extends BimTest {

    private MonVillageCommandHandler handler;
    private NotifEventApiClient notifEventApiClient;
    private MonVillageUtils monVillageUtils;
    private PacmanClient pacmanClient;
    private BimApiClient bimApiClient;
    private CMSender cmSender;
    private ObjectMapper objectMapper;
    private String commands = "{\"1\":[{\"cmd\":true,\"code\":\"123456789012345\",\"dureeMinutes\":4,\"provider\":\"LACROIX\"}],\"2\":[{\"code\":\"123456789012349\",\"dureeMinutes\":4,\"provider\":\"LACROIX\",\"relais\":{\"0\":{\"cmd\":true,\"dureeMinutes\":5},\"1\":{\"cmd\":true,\"dureeMinutes\":15}}}],\"3\":[{\"cmd\":true,\"code\":\"123456789012345\",\"dureeMinutes\":4,\"provider\":\"LACROIX\"},{\"cmd\":true,\"code\":\"123456789012346\",\"dureeMinutes\":4,\"provider\":\"LACROIX\"},{\"cmd\":true,\"code\":\"123456789012347\",\"dureeMinutes\":4,\"provider\":\"LACROIX\"}]}";

    @BeforeEach
    void init() {
        monVillageUtils = new MonVillageUtils();
        notifEventApiClient = mock(NotifEventApiClient.class);
        bimApiClient = mock(BimApiClient.class);
        cmSender = mock(CMSender.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        pacmanClient = mock(PacmanClient.class);

        handler = new MonVillageCommandHandler(
                bimApiClient,
                notifEventApiClient,
                pacmanClient,
                objectMapper,
                cmSender,
                monVillageUtils,
                commands
        );
    }

    @Test
    @SneakyThrows
    void handleValidMonVillageInput3Cabinets() {
        NotifEvent expectedNotif = NotifEvent.builder()
                .externalids(Map.ofEntries(entry("MonVillage", "CMD-003")))
                .code("CMD-003")
                .category("CITIZEN_COMMAND_SCENARIO_[3]")
                .source("MonVillage")
                .comment("Totem : [3] et Scénario : [3], actionId : [IDPAC,IDPAC,IDPAC]")
                .metier(Map.of("metiers", List.of("EP")))
                .name("Demande de commande, scénario [3]")
                .event(List.of())
                .settings(Map.of("id_totem", "[3]", "id_scenario", "[3]"))
                .status("ToApprove")
                .build();

        // Given
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        when(notifEventApiClient.getNotifEventByExternalIds(anyString(), anyList()))
                .thenReturn(List.of());
        when(pacmanClient.action(any()))
                .thenReturn("IDPAC");

        // When
        String fileBody = fromFileToString("notifMonVillageScenario3.json");
        handler.handle(fileBody);

        // Then
        verify(notifEventApiClient).getNotifEventByExternalIds(eq("MonVillage"), anyList());
        verify(cmSender).commit(crudCaptor.capture());
        assertThat(crudCaptor.getValue().get(NotifEvent.class).getCreate().size()).isEqualTo(1);
        NotifEvent ne = crudCaptor.getValue().get(NotifEvent.class).getCreate().iterator().next();
        assertThat(ne).usingRecursiveComparison().ignoringFields("location", "apparitiondate").isEqualTo(expectedNotif);
        verify(pacmanClient, times(3)).action(any());
    }

    @Test
    @SneakyThrows
    void handleValidMonVillageInput1Cabinet() {
        NotifEvent expectedNotif = NotifEvent.builder()
                .externalids(Map.ofEntries(entry("MonVillage", "CMD-001")))
                .code("CMD-001")
                .category("CITIZEN_COMMAND_SCENARIO_[1]")
                .source("MonVillage")
                .comment("Totem : [1] et Scénario : [1], actionId : [IDPAC]")
                .metier(Map.of("metiers", List.of("EP")))
                .name("Demande de commande, scénario [1]")
                .event(List.of())
                .settings(Map.of("id_totem", "[1]", "id_scenario", "[1]"))
                .status("ToApprove")
                .build();

        TalqCabinetActionTemplate expectedCmd = TalqCabinetActionTemplate.builder()
                .code("123456789012345")
                .provider("LACROIX")
                .cmd(true)
                .dureeMinutes(4)
                .relais(null)
                .build();

        // Given
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<PacManAction<TalqCabinetActionTemplate>> cmdCaptor = ArgumentCaptor.forClass(PacManAction.class);
        when(notifEventApiClient.getNotifEventByExternalIds(anyString(), anyList()))
                .thenReturn(List.of());
        when(pacmanClient.action(any()))
                .thenReturn("IDPAC");

        // When
        String fileBody = fromFileToString("notifMonVillageScenario1.json");
        handler.handle(fileBody);

        // Then
        verify(notifEventApiClient).getNotifEventByExternalIds(eq("MonVillage"), anyList());
        verify(cmSender).commit(crudCaptor.capture());
        assertThat(crudCaptor.getValue().get(NotifEvent.class).getCreate().size()).isEqualTo(1);
        NotifEvent ne = crudCaptor.getValue().get(NotifEvent.class).getCreate().iterator().next();
        assertThat(ne).usingRecursiveComparison().ignoringFields("location", "apparitiondate").isEqualTo(expectedNotif);
        verify(pacmanClient).action(cmdCaptor.capture());
        assertThat(cmdCaptor.getValue().getSettings()).usingRecursiveComparison().isEqualTo(expectedCmd);
    }

    @Test
    @SneakyThrows
    void handleValidMonVillageInput2Relais() {
        NotifEvent expectedNotif = NotifEvent.builder()
                .externalids(Map.ofEntries(entry("MonVillage", "CMD-002")))
                .code("CMD-002")
                .category("CITIZEN_COMMAND_SCENARIO_[2]")
                .source("MonVillage")
                .comment("Totem : [2] et Scénario : [2], actionId : [IDPAC]")
                .metier(Map.of("metiers", List.of("EP")))
                .name("Demande de commande, scénario [2]")
                .event(List.of())
                .settings(Map.of("id_totem", "[2]", "id_scenario", "[2]"))
                .status("ToApprove")
                .build();

        TalqCabinetActionTemplate expectedCmd = TalqCabinetActionTemplate.builder()
                .code("123456789012349")
                .provider("LACROIX")
                .cmd(null)
                .dureeMinutes(4)
                .relais(
                        Map.of(
                                "0", TalqCabinetActionTemplateRelais.builder()
                                        .cmd(true)
                                        .dureeMinutes(5)
                                        .build(),
                                "1", TalqCabinetActionTemplateRelais.builder()
                                        .cmd(true)
                                        .dureeMinutes(15)
                                        .build()
                        )
                )
                .build();
        // Given
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<PacManAction<TalqCabinetActionTemplate>> cmdCaptor = ArgumentCaptor.forClass(PacManAction.class);
        when(notifEventApiClient.getNotifEventByExternalIds(anyString(), anyList()))
                .thenReturn(List.of());
        when(pacmanClient.action(any()))
                .thenReturn("IDPAC");

        // When
        String fileBody = fromFileToString("notifMonVillageScenario2.json");
        handler.handle(fileBody);

        // Then
        verify(notifEventApiClient).getNotifEventByExternalIds(eq("MonVillage"), anyList());
        verify(cmSender).commit(crudCaptor.capture());
        assertThat(crudCaptor.getValue().get(NotifEvent.class).getCreate().size()).isEqualTo(1);
        NotifEvent ne = crudCaptor.getValue().get(NotifEvent.class).getCreate().iterator().next();
        assertThat(ne).usingRecursiveComparison().ignoringFields("location", "apparitiondate").isEqualTo(expectedNotif);
        verify(pacmanClient, times(1)).action(cmdCaptor.capture());
        assertThat(cmdCaptor.getValue().getSettings()).usingRecursiveComparison().isEqualTo(expectedCmd);
    }

    @Test
    @SneakyThrows
    void handleValidMonVillageInputExistingCommand() {
        // Given
        NotifEvent existingNotif = NotifEvent.builder()
                .externalids(Map.ofEntries(entry("MonVillage", "CMD-001")))
                .code("CMD-001")
                .category("CITIZEN_COMMAND_SCENARIO_[1]")
                .source("MonVillage")
                .comment("Totem : [1] et Scénario : [1]")
                .metier(Map.of("metiers", List.of("EP")))
                .name("Demande de commande, scénario [1]")
                .event(List.of())
                .settings(Map.of("id_totem", "[1]", "id_scenario", "[1]"))
                .status("ToApprove")
                .build();

        when(notifEventApiClient.getNotifEventByExternalIds(anyString(), anyList()))
                .thenReturn(List.of(existingNotif));

        // When
        String fileBody = fromFileToString("notifMonVillageScenario1.json");
        handler.handle(fileBody);

        // Then
        verify(cmSender, never()).commit(any(CrudOperation.class));
        verify(notifEventApiClient).getNotifEventByExternalIds(eq("MonVillage"), anyList());
        verify(pacmanClient, never()).action(any());
    }
}