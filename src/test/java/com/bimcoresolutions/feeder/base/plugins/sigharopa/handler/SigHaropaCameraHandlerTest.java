package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.camera.CameraCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.CameraGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Camera;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CameraCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaCameraHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String CAPTOR = "captor";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    SigHaropaCameraHandler sigHaropaControllerHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    CameraGenFeignClient cameraGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        cameraGenFeignClient = mock(CameraGenFeignClient.class);
        sigHaropaControllerHandler = new SigHaropaCameraHandler(bimApiClient, objectMapper, cmSender, cameraGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_camera_sig() {
        String code = "1camera";
        Camera camera = new Camera().toBuilder().code(code).build();

        //mock
        PaginatedResponse<Camera> panelPaginatedResponse = new PaginatedResponse<>(List.of(camera), new PaginatedResponse.Pagination());
        when(cameraGenFeignClient.get(new CameraCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("cameraSig.json");
        sigHaropaControllerHandler.handle(fileBody);

        verify(cameraGenFeignClient).get(new CameraCriteria(), null, null, null, null, null);
    }


    @Test
    @SneakyThrows
    void should_create_camera() {
        String fileBody = fromFileToString("cameraSig.json");
        CameraCollection data = objectMapper.readValue(fileBody, CameraCollection.class);

        Map<String, Object> ids = Map.of(CAPTOR, "3", SIG, "10001");
        Camera camera = new Camera().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .inhibition(false)
                .controllable(false)
                .status(0)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .name("1")
                .type("vidéosurveillance caméra fixe analogique")
                .site("Vétillart Mât amont Sud")
                .metier(Map.of("metiers", List.of("maritime")))
                .build();

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigCameras(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(camera);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_camera() {
        Map<String, Object> ids = Map.of(CAPTOR, "3", SIG, "10001");

        Camera camera = new Camera().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code(SIGHAROPA_ + "10001")
                .externalids(ids)
                .location(point)
                .name("1")
                .type("vidéosurveillance caméra fixe analogique")
                .site("Armature du pont Est")
                .metier(Map.of("metiers", List.of("maritime")))
                .build();

        String fileBody = fromFileToString("cameraSig.json");
        CameraCollection data = objectMapper.readValue(fileBody, CameraCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigCameras(List.of(camera), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(camera);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_camera() {
        Map<String, Object> ids = Map.of(CAPTOR, "1", SIG, "10001");

        Camera camera = new Camera().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .name("1")
                .type("vidéosurveillance caméra fixe")
                .site("Armature du pont Est")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigCameras(List.of(camera), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(camera.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}