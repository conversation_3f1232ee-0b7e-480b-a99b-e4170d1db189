package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.cabinet.CabinetCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.CabinetGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CabinetCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaCabinetHandlerTest extends BimTest {
    public static final String CAPTOR = "captor";
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    public static final String SIG = "sig";
    SigHaropaCabinetHandler sigHaropaCabinetHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    CabinetGenFeignClient cabinetGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        cabinetGenFeignClient = mock(CabinetGenFeignClient.class);
        sigHaropaCabinetHandler = new SigHaropaCabinetHandler(bimApiClient, objectMapper, cmSender, cabinetGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_cabinet_sig() {
        String code = "1cabinet";
        Cabinet cabinet = new Cabinet().toBuilder().code(code).build();

        //mock
        PaginatedResponse<Cabinet> panelPaginatedResponse = new PaginatedResponse<>(List.of(cabinet), new PaginatedResponse.Pagination());
        when(cabinetGenFeignClient.get(new CabinetCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("cabinetSig.json");
        sigHaropaCabinetHandler.handle(fileBody);
        verify(cabinetGenFeignClient).get(new CabinetCriteria(), null, null, null, null, null);
    }


    @Test
    @SneakyThrows
    void should_create_cabinet() {
        String fileBody = fromFileToString("cabinetSig.json");
        CabinetCollection data = objectMapper.readValue(fileBody, CabinetCollection.class);

        Map<String, Object> ids = Map.of(CAPTOR, "ECPU_GPMH_CC-A", SIG, "10001");
        Cabinet cabinet = new Cabinet().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .status(0)
                .name("ECPU_GPMH_CC-A")
                .externalids(ids)
                .build();

        //when
        CrudOperation<Model> result = sigHaropaCabinetHandler.treatSigCabinets(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(cabinet);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_cabinet() {
        Map<String, Object> ids = Map.of(CAPTOR, "ECPU_GPMH_CC-A", SIG, "10001");

        Cabinet cabinet = new Cabinet().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .status(0)
                .name("ECPU_GPMH_CC-A")
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("cabinetSig.json");
        CabinetCollection data = objectMapper.readValue(fileBody, CabinetCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaCabinetHandler.treatSigCabinets(List.of(cabinet), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(cabinet);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_cabinet() {
        Map<String, Object> ids = Map.of(CAPTOR, "ECPU_GPMH_CC-A", SIG, "10001");

        Cabinet cabinet = new Cabinet().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .status(0)
                .name("ECPU_GPMH_CC-A")
                .externalids(ids)
                .build();

        //when
        CrudOperation<Model> result = sigHaropaCabinetHandler.treatSigCabinets(List.of(cabinet), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(cabinet.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }

}