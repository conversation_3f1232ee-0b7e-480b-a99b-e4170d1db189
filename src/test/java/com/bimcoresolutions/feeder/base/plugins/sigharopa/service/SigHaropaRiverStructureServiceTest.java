package com.bimcoresolutions.feeder.base.plugins.sigharopa.service;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.riverstructure.RiverStructureCollection;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.riverstructure.RiverStructureSig;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RiverStructure;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.mock;

class SigHaropaRiverStructureServiceTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String CAPTOR = "captor";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    SigHaropaUtils sigHaropaUtils;
    SigHaropaRiverStructureService sigHaropaRiverStructureService;
    ObjectMapper objectMapper;

    SigHaropaRiverStructureServiceTest() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        sigHaropaRiverStructureService = new SigHaropaRiverStructureService(sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_create_riverstructure_pont() {
        String fileBody = fromFileToString("riverStructureSig_pont.json");
        RiverStructureCollection data = objectMapper.readValue(fileBody, RiverStructureCollection.class);

        List<RiverStructureSig> comingRiverStructures = data.getFeatures();
        comingRiverStructures.forEach(comingRiverStructure -> comingRiverStructure.setTypeObject(data.getTypeObject()));

        Map<String, Object> ids = Map.of(CAPTOR, "PONT_5", SIG, "10001");
        RiverStructure riverstructure = new RiverStructure().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code(SIGHAROPA_ + "10001pont")
                .inhibition(false)
                .location(point)
                .name("5 (cinq)")
                .status(0)
                .externalids(ids)
                .type("pont")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaRiverStructureService.treatSigRiverStructures(List.of(), comingRiverStructures, "pont");

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(riverstructure);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();


    }

    @Test
    @SneakyThrows
    void should_update_riverstructure_pont() {
        Map<String, Object> ids = Map.of(CAPTOR, "PONT_5", SIG, "10001");

        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code(SIGHAROPA_ + "10001pont")
                .inhibition(false)
                .location(point)
                .name("5 (cinq)")
                .status(0)
                .externalids(ids)
                .type("pont")
                .build();

        String fileBody = fromFileToString("riverStructureSig_pont.json");
        RiverStructureCollection data = objectMapper.readValue(fileBody, RiverStructureCollection.class);

        List<RiverStructureSig> comingRiverStructures = data.getFeatures();
        comingRiverStructures.forEach(comingRiverStructure -> comingRiverStructure.setTypeObject(data.getTypeObject()));

        //when
        CrudOperation<Model> result = sigHaropaRiverStructureService.treatSigRiverStructures(List.of(riverStructure), comingRiverStructures, "pont");

        //then
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(riverStructure);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_riverstructure_pont() {
        Map<String, Object> ids = Map.of(CAPTOR, "1", SIG, "10001");

        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code(SIGHAROPA_ + "10001pont")
                .inhibition(false)
                .location(point)
                .name("de Tancarville (ancienne)")
                .status(0)
                .externalids(ids)
                .type("pont")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaRiverStructureService.treatSigRiverStructures(List.of(riverStructure), List.of(), "pont");

        //then
        assertThat(result.getDelete()).hasSize(1);
        assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(riverStructure.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_create_riverstructure_ecluse() {
        String fileBody = fromFileToString("riverStructureSig_ecluse.json");
        RiverStructureCollection data = objectMapper.readValue(fileBody, RiverStructureCollection.class);

        List<RiverStructureSig> comingRiverStructures = data.getFeatures();
        comingRiverStructures.forEach(comingRiverStructure -> comingRiverStructure.setTypeObject(data.getTypeObject()));

        Map<String, Object> ids = Map.of(SIG, "10001");
        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code(SIGHAROPA_ + "10001ecluse")
                .inhibition(false)
                .location(point)
                .name("A 29 (vingt-neuf)")
                .status(0)
                .externalids(ids)
                .type("porte d'ecluse")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaRiverStructureService.treatSigRiverStructures(List.of(), comingRiverStructures, "ecluse");

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(riverStructure);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_riverstructure_ecluse() {
        Map<String, Object> ids = Map.of(SIG, "10001");

        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code(SIGHAROPA_ + "10001ecluse")
                .inhibition(false)
                .location(point)
                .name("A 29 (vingt-neuf)")
                .status(0)
                .externalids(ids)
                .type("porte d'ecluse")
                .build();

        String fileBody = fromFileToString("riverStructureSig_ecluse.json");
        RiverStructureCollection data = objectMapper.readValue(fileBody, RiverStructureCollection.class);

        List<RiverStructureSig> comingRiverStructures = data.getFeatures();
        comingRiverStructures.forEach(comingRiverStructure -> comingRiverStructure.setTypeObject(data.getTypeObject()));

        //when
        CrudOperation<Model> result = sigHaropaRiverStructureService.treatSigRiverStructures(List.of(riverStructure), comingRiverStructures, "ecluse");

        //then
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(riverStructure);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_riverstructure_ecluse() {
        Map<String, Object> ids = Map.of(SIG, "10001");

        RiverStructure riverStructure = new RiverStructure().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code(SIGHAROPA_ + "10001ecluse")
                .inhibition(false)
                .location(point)
                .name("A 29 (vingt-neuf)")
                .status(0)
                .externalids(ids)
                .type("porte d'ecluse")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaRiverStructureService.treatSigRiverStructures(List.of(riverStructure), List.of(), "ecluse");

        //then
        assertThat(result.getDelete()).hasSize(1);
        assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(riverStructure.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}