package com.bimcoresolutions.feeder.base.plugins.exedra.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlertApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.configuration.SpringBeanConfiguration;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.exedra.model.EnumExedra;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.modelmessage.commitmetier.requestv2.body.CMUpdRelBodyv2;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.mockito.Mockito.mock;

public class ExedraAssetHandlerTest extends BimTest {

    private static final ObjectMapper objectMapper = new SpringBeanConfiguration().objectMapper();
    private ExedraAssetHandler exedraAssetHandler;
    private CMSender cmSender;
    private LightingPointApiClient lightingPointApiClient;
    private AlarmApiClient alarmApiClient;
    private AlertApiClient alertApiClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        cmSender = mock(CMSender.class);
        lightingPointApiClient = mock(LightingPointApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        alertApiClient = mock(AlertApiClient.class);
        exedraAssetHandler = new ExedraAssetHandler(bimApiClient, objectMapper, cmSender, lightingPointApiClient,
                alarmApiClient, alertApiClient);
        Mockito.doAnswer(invocation -> {
            Set<Model> datas = invocation.getArgument(0);
            for (Model data : datas) {
                data.set_id_bimcore(UUID.randomUUID().toString());
            }
            return null;
        }).when(bimApiClient).addMissingIds(Mockito.anySet());
    }

    private static final List<String> lpNames = List.of("100-007", "101-007");
    private static final List<String> lpExtIds = List.of("0a7e7477-bad1-4b6b-a853-a3fc90f00ffd", "0a7e7477-bad2-4b6b-a853-ae752e4a584b");
    private static final List<String> lpBimIds = List.of("0a7e7477-bad1-dead-a853-a3fc90f00ffd", "0a7e7477-bad2-dead-a853-ae752e4a584b");
    private static final String alarmBimId = "0a7e747a-bad1-dead-beaf-a3fc90f00ffd";
    private static final String alertBimId = "0a7e747b-bad1-dead-beaf-a3fc90f00ffd";

    private static LightingPoint buildLp(int index, int status) {
        return LightingPoint.builder()
                ._id_bimcore(lpBimIds.get(index))
                .name(lpNames.get(index))
                .status(status)
                .externalids(Map.of(ExedraAssetHandler.EXEDRA, lpExtIds.get(index)))
                .build();
    }

    @SneakyThrows
    private static Alarm buildAlarm(int indexLp, EnumExedra type, boolean presence, String sDate) {
        return buildAlarm(indexLp, type, presence, sDate, false);
    }

    @SneakyThrows
    private static Alarm buildAlarm(int indexLp, EnumExedra type, boolean presence, String sDate, boolean hasAlert) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        return Alarm.builder()
                ._id_bimcore(hasAlert ? alarmBimId : UUID.randomUUID().toString())
                .code(lpExtIds.get(indexLp) + "_" + type.getValue())
                .name(type.getName())
                .presence(presence)
                .lastupdate(sdf.parse(sDate))
                .externalids(Map.of(ExedraAssetHandler.EXEDRA, lpExtIds.get(indexLp)))
                .lightingpoint(List.of(lpBimIds.get(indexLp)))
                .alerts(hasAlert ? List.of(alertBimId) : List.of())
                .build();
    }

    private static Alert buildAlert(int indexLp) {
        return Alert.builder()
                ._id_bimcore(alertBimId)
                .alarms(List.of(alertBimId))
                .lightingpoint((List.of(lpBimIds.get(indexLp))))
                .build();
    }

    private static void assertCaptors(ArgumentCaptor<CrudOperation<Model>> crudCaptor, ArgumentCaptor<Map<String, List<CMUpdRelBodyv2>>> relationCaptor,
                                      int expLpC, int expLpU, int expAlaC, int expAlaU, int expAleC, int expRel) {
        Assertions.assertThat(crudCaptor.getValue().getCreate().size()).isEqualTo(expLpC+expAlaC+expAleC);
        Assertions.assertThat(crudCaptor.getValue().getUpdate().size()).isEqualTo(expLpU+expAlaU);
        Assertions.assertThat(crudCaptor.getValue().getDelete().size()).isEqualTo(0);
        Assertions.assertThat(crudCaptor.getValue().get(LightingPoint.class).getCreate().size()).isEqualTo(expLpC);
        Assertions.assertThat(crudCaptor.getValue().get(LightingPoint.class).getUpdate().size()).isEqualTo(expLpU);
        Assertions.assertThat(crudCaptor.getValue().get(LightingPoint.class).getDelete().size()).isEqualTo(0);
        Assertions.assertThat(crudCaptor.getValue().get(Alarm.class).getCreate().size()).isEqualTo(expAlaC);
        Assertions.assertThat(crudCaptor.getValue().get(Alarm.class).getUpdate().size()).isEqualTo(expAlaU);
        Assertions.assertThat(crudCaptor.getValue().get(Alarm.class).getDelete().size()).isEqualTo(0);
        Assertions.assertThat(crudCaptor.getValue().get(Alert.class).getCreate().size()).isEqualTo(expAleC);
        Assertions.assertThat(crudCaptor.getValue().get(Alert.class).getUpdate().size()).isEqualTo(0);
        Assertions.assertThat(crudCaptor.getValue().get(Alert.class).getDelete().size()).isEqualTo(0);
        Assertions.assertThat(relationCaptor.getValue().size()).isEqualTo(expRel);
    }

    @Test
    @SneakyThrows
    void assets_2_faults_0_active_6_inactive_similar_alarm_on_lp() {
        // Les PL sont deja dans le meme etat
        // On attend :
        // LP Creation: 0, MAJ: 0
        // Alarme Creation: 0, MAJ: 0
        // Alert Creation: 0
        List<LightingPoint> lps = List.of(buildLp(0, 1), buildLp(1, 1));
        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, false, "2020-01-01T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lampFailure, false, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z")
        );

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                        ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);

        String fileBody = fromFileToString("exedra_assets_2_faults_0_active_6_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(cmSender, Mockito.never()).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender, Mockito.never()).commitWithRelations(ArgumentMatchers.any(), ArgumentMatchers.any());
    }

    @Test
    @SneakyThrows
    void assets_2_faults_0_active_6_inactive_no_alarm_on_lp() {
        // Les PL n'ont pas d'alarme en cours
        // On attend :
        // LP Creation: 0, MAJ: 0
        // Alarme Creation: 6, MAJ: 0
        // Alert Creation: 0
        List<LightingPoint> lps = List.of(buildLp(0, 1), buildLp(1, 1));

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);

        String fileBody = fromFileToString("exedra_assets_2_faults_0_active_6_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertCategoriesByLabel(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).createAlertCategory(ArgumentMatchers.any());
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<Map<String, List<CMUpdRelBodyv2>>> relCaptor = ArgumentCaptor.forClass(Map.class);
        Mockito.verify(cmSender).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender).commitWithRelations(crudCaptor.capture(), relCaptor.capture());
        assertCaptors(crudCaptor, relCaptor, 0, 0, 6, 0, 0, 3);
    }

    @Test
    @SneakyThrows
    void assets_2_faults_0_active_6_inactive_change_alarm_on_lp() {
        // 1 PL a un defaut actif en cours
        // On attend :
        // LP Creation: 0, MAJ: 1
        // Alarme Creation: 0, MAJ: 1
        // Alert Creation: 0
        List<LightingPoint> lps = List.of(buildLp(0, -1), buildLp(1, 1));
        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, false, "2020-01-01T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lampFailure, true, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z")
        );

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);

        String fileBody = fromFileToString("exedra_assets_2_faults_0_active_6_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertCategoriesByLabel(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).createAlertCategory(ArgumentMatchers.any());
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<Map<String, List<CMUpdRelBodyv2>>> relCaptor = ArgumentCaptor.forClass(Map.class);
        Mockito.verify(cmSender).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender).commitWithRelations(crudCaptor.capture(), relCaptor.capture());
        assertCaptors(crudCaptor, relCaptor, 0, 1, 0, 1, 0, 3);
    }

    @Test
    @SneakyThrows
    void assets_2_faults_1_active_7_inactive_no_alarm_on_lp() {
        // Les PL n'ont pas de defaut en cours. 1 nouveau defaut inconnu apparait actif sur l'un et pas sur l'autre
        // On attend :
        // LP Creation: 0, MAJ: 1
        // Alarme Creation: 2, MAJ: 0
        // Alert Creation: 1
        List<LightingPoint> lps = List.of(buildLp(0, 1), buildLp(1, 1));
        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, false, "2020-01-01T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lampFailure, false, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z")
        );

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);

        String fileBody = fromFileToString("exedra_assets_2_faults_1_active_7_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(alertApiClient).getAlertCategoriesByLabel(ArgumentMatchers.any());
        Mockito.verify(alertApiClient).createAlertCategory(ArgumentMatchers.any());
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<Map<String, List<CMUpdRelBodyv2>>> relCaptor = ArgumentCaptor.forClass(Map.class);
        Mockito.verify(cmSender).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender).commitWithRelations(crudCaptor.capture(), relCaptor.capture());
        assertCaptors(crudCaptor, relCaptor, 0, 1, 2, 0, 1, 3);
    }

    @Test
    @SneakyThrows
    void assets_2_faults_1_active_7_inactive_alarm_on_lp_same() {
        // 1 PL en defaut a 1 defaut en cours. On recoit 1 defaut similaire plus recent sur le meme PL
        // On attend :
        // LP Creation: 0, MAJ: 0
        // Alarme Creation: 0, MAJ: 0
        // Alert Creation: 0
        List<LightingPoint> lps = List.of(buildLp(0, -1), buildLp(1, 1));

        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, false, "2020-01-01T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lampFailure, false, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.potentialCabinetIssue, true, "2020-01-04T10:00:00.000Z", true),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z"),
                buildAlarm(1, EnumExedra.potentialCabinetIssue, false, "2020-01-01T10:00:40.000Z")
        );

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);

        String fileBody = fromFileToString("exedra_assets_2_faults_1_active_7_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(cmSender, Mockito.never()).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender, Mockito.never()).commitWithRelations(ArgumentMatchers.any(), ArgumentMatchers.any());
    }

    @Test
    @SneakyThrows
    void assets_2_faults_1_active_7_inactive_alarm_on_lp_inactive_newer() {
        // Pas de PL en defaut. Reception 1 defaut mais on a deja recu une fin d'alarme plus recente sur le meme PL
        // Typiquement si on recoit un historique. On ne veut donc rien modifier
        // On attend :
        // LP Creation: 0, MAJ: 0
        // Alarme Creation: 0, MAJ: 0
        // Alert Creation: 0
        List<LightingPoint> lps = List.of(buildLp(0, 1), buildLp(1, 1));

        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, false, "2020-01-01T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lampFailure, false, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.potentialCabinetIssue, false, "2025-03-03T10:00:00.000Z", true),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z"),
                buildAlarm(1, EnumExedra.potentialCabinetIssue, false, "2020-01-01T10:00:40.000Z")
        );

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);

        String fileBody = fromFileToString("exedra_assets_2_faults_1_active_7_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(cmSender, Mockito.never()).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender, Mockito.never()).commitWithRelations(ArgumentMatchers.any(), ArgumentMatchers.any());
    }

    @Test
    @SneakyThrows
    void assets_2_faults_1_active_7_inactive_alarm_on_lp_other() {
        // 1 PL en defaut a 1 defaut en cours. 1 defaut similaire apparait sur l'autre PL et disparait du 1er
        // On attend :
        // LP Creation: 0, MAJ: 2
        // Alarme Creation: 0, MAJ: 2
        // Alert Creation: 1
        List<LightingPoint> lps = List.of(buildLp(0, 1), buildLp(1, -1));

        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, false, "2020-01-01T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lampFailure, false, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.potentialCabinetIssue, false, "2020-01-04T10:00:00.000Z"),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z"),
                buildAlarm(1, EnumExedra.potentialCabinetIssue, true, "2020-01-01T10:00:40.000Z", true)
        );

        List<Alert> alerts = List.of(buildAlert(0));

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);
        Mockito.when(alertApiClient.getAlertByNotClosedAndSameCategory(ArgumentMatchers.eq(EnumExedra.potentialCabinetIssue.getValue())))
                .thenReturn(alerts);
        Mockito.when(alertApiClient.getAlertCategoriesByLabel(ArgumentMatchers.eq(EnumExedra.potentialCabinetIssue.getName())))
                .thenReturn(List.of(EnumExedra.potentialCabinetIssue.getName()));

        String fileBody = fromFileToString("exedra_assets_2_faults_1_active_7_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(alertApiClient).getAlertCategoriesByLabel(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).createAlertCategory(ArgumentMatchers.any());
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<Map<String, List<CMUpdRelBodyv2>>> relCaptor = ArgumentCaptor.forClass(Map.class);
        Mockito.verify(cmSender).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender).commitWithRelations(crudCaptor.capture(), relCaptor.capture());
        assertCaptors(crudCaptor, relCaptor, 0, 2, 0, 2, 1, 3);
    }

    @Test
    @SneakyThrows
    void assets_2_faults_1_active_7_inactive_alarm_on_lp_diff() {
        // 1 PL en defaut a 1 defaut en cours. 1 defaut different apparait sur le meme PL et l'ancien disparait
        // On attend :
        // LP Creation: 0, MAJ: 0
        // Alarme Creation: 0, MAJ: 1
        // Alert Creation: 1
        List<LightingPoint> lps = List.of(buildLp(0, -1), buildLp(1, 1));

        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, true, "2020-01-01T10:00:00.000Z", true),
                buildAlarm(0, EnumExedra.lampFailure, false, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.potentialCabinetIssue, false, "2020-01-04T10:00:00.000Z"),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z"),
                buildAlarm(1, EnumExedra.potentialCabinetIssue, false, "2020-01-01T10:00:40.000Z")
        );

        List<Alert> alerts = List.of(buildAlert(0));

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);
        Mockito.when(alertApiClient.getAlertByNotClosedAndSameCategory(ArgumentMatchers.eq(EnumExedra.connectionLost.getName())))
                .thenReturn(alerts);
        Mockito.when(alertApiClient.getAlertCategoriesByLabel(ArgumentMatchers.eq(EnumExedra.potentialCabinetIssue.getName())))
                .thenReturn(List.of(EnumExedra.potentialCabinetIssue.getName()));

        String fileBody = fromFileToString("exedra_assets_2_faults_1_active_7_inactive.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(alertApiClient).getAlertCategoriesByLabel(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).createAlertCategory(ArgumentMatchers.any());
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<Map<String, List<CMUpdRelBodyv2>>> relCaptor = ArgumentCaptor.forClass(Map.class);
        Mockito.verify(cmSender).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender).commitWithRelations(crudCaptor.capture(), relCaptor.capture());
        assertCaptors(crudCaptor, relCaptor, 0, 0, 0, 2, 1, 3);
    }

    @Test
    @SneakyThrows
    void assets_2_faults_1_active_7_inactive_alarm_only() {
        // Pas de PL en defaut, alarmes inactives sur les 2 PLs. Reception 1 defaut qui cree 1 alarme sans alerte
        // On attend :
        // LP Creation: 0, MAJ: 1
        // Alarme Creation: 0, MAJ: 1
        // Alert Creation: 0
        List<LightingPoint> lps = List.of(buildLp(0, 1), buildLp(1, 1));

        List<Alarm> alarms = List.of(
                buildAlarm(0, EnumExedra.connectionLost, false, "2020-01-01T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lampFailure, false, "2020-01-02T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.lowCommunication, false, "2020-01-03T10:00:00.000Z"),
                buildAlarm(0, EnumExedra.potentialCabinetIssue, false, "2025-03-03T10:00:00.000Z"),
                buildAlarm(1, EnumExedra.connectionLost, false, "2020-01-01T10:10:00.000Z"),
                buildAlarm(1, EnumExedra.lampFailure, false, "2020-01-01T10:00:20.000Z"),
                buildAlarm(1, EnumExedra.lowCommunication, false, "2020-01-01T10:00:30.000Z"),
                buildAlarm(1, EnumExedra.potentialCabinetIssue, false, "2020-01-01T10:00:40.000Z")
        );

        //mock
        Mockito.when(lightingPointApiClient.getLightingPointsByNames(ArgumentMatchers.eq(lpNames))).thenReturn(lps);
        Mockito.when(alarmApiClient.getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA),
                ArgumentMatchers.eq(lpExtIds))).thenReturn(alarms);

        String fileBody = fromFileToString("exedra_assets_2_faults_1_active_7_inactive_alarm_only.json");
        exedraAssetHandler.handle(fileBody);

        Mockito.verify(alarmApiClient).getAlarmByExternalIds(ArgumentMatchers.eq(ExedraAssetHandler.EXEDRA), ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertByNotClosedAndSameCategory(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).getAlertCategoriesByLabel(ArgumentMatchers.any());
        Mockito.verify(alertApiClient, Mockito.never()).createAlertCategory(ArgumentMatchers.any());
        ArgumentCaptor<CrudOperation<Model>> crudCaptor = ArgumentCaptor.forClass(CrudOperation.class);
        ArgumentCaptor<Map<String, List<CMUpdRelBodyv2>>> relCaptor = ArgumentCaptor.forClass(Map.class);
        Mockito.verify(cmSender).commit(ArgumentMatchers.<CrudOperation<LightingPoint>>any());
        Mockito.verify(cmSender).commitWithRelations(crudCaptor.capture(), relCaptor.capture());
        assertCaptors(crudCaptor, relCaptor, 0, 1, 0, 1, 0, 3);
    }
}
