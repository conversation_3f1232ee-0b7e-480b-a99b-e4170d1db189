package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.CountingStationDP;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.CountingStationGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CountingStation;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CountingStationCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaCountingStationHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String IROAD = "iroad";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    private final TypeReference<List<CountingStationDP>> typeReferenceListCountingStationDP = new TypeReference<>() {
    };
    SigHaropaCountingStationHandler sigHaropaCountingStationHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    CountingStationGenFeignClient countingStationGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        countingStationGenFeignClient = mock(CountingStationGenFeignClient.class);
        sigHaropaCountingStationHandler = new SigHaropaCountingStationHandler(bimApiClient, objectMapper, cmSender, countingStationGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_counting_station_sig() {
        String code = "1countingstation";
        CountingStation countingStation = new CountingStation().toBuilder().code(code).build();
        //mock
        PaginatedResponse<CountingStation> panelPaginatedResponse = new PaginatedResponse<>(List.of(countingStation), new PaginatedResponse.Pagination());
        when(countingStationGenFeignClient.get(new CountingStationCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("stationSig.json");
        sigHaropaCountingStationHandler.handle(fileBody);

        List<CountingStationDP> data = objectMapper.readValue(fileBody, typeReferenceListCountingStationDP);

        verify(countingStationGenFeignClient).get(new CountingStationCriteria(), null, null, null, null, null);
    }


    @Test
    @SneakyThrows
    void should_create_station() {
        String fileBody = fromFileToString("stationSig.json");
        List<CountingStationDP> data = objectMapper.readValue(fileBody, typeReferenceListCountingStationDP);

        Map<String, Object> ids = Map.of(IROAD, "PTM62", SIG, "10001");
        CountingStation station = new CountingStation().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .site("PTM CHANTIER N/S")
                .name("PTM62")
                .status(0)
                .type("Mesure")
                .externalids(ids)
                .build();

        //when
        CrudOperation<Model> result = sigHaropaCountingStationHandler.treatSigCountingStations(List.of(), data);

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(station);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_station() {
        Map<String, Object> ids = Map.of(IROAD, "PTM62", SIG, "10001");

        CountingStation station = new CountingStation().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .site("PTM CHANTIER N/S")
                .name("PTM62")
                .status(0)
                .type("Mesure")
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("stationSig.json");
        List<CountingStationDP> data = objectMapper.readValue(fileBody, typeReferenceListCountingStationDP);

        //when
        CrudOperation<Model> result = sigHaropaCountingStationHandler.treatSigCountingStations(List.of(station), data);

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(station);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_station() {
        Map<String, Object> ids = Map.of(IROAD, "1");

        CountingStation station = new CountingStation().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .site("PTM CHANTIER N/S")
                .name("PTM62")
                .type("Mesure")
                .status(0)
                .externalids(ids)
                .build();

        //when
        CrudOperation<Model> result = sigHaropaCountingStationHandler.treatSigCountingStations(List.of(station), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(station.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}