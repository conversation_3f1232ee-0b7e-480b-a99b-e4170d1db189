package com.bimcoresolutions.feeder.base.plugins.neocity.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.ReportApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.connectorstate.ConnectorStateService;
import com.bimcoresolutions.feeder.base.plugins.neocity.NeocityUtils;
import com.bimcoresolutions.feeder.base.plugins.neocity.model.DataplatformNeocityCitizenAlert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Report;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class NeocityCitizenAlertHandlerTest extends BimTest {

    public static final String NEOCITY = "neocity";
    public static final String RANDOM_ID = "random id";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    private NeocityCitizenAlertHandler neocityCitizenAlertHandler;
    private ConnectorStateService connectorStateServiceMock;
    private ReportApiClient reportApiClient;

    @BeforeEach
    void init() {
        NeocityUtils neocityUtils = new NeocityUtils();
        connectorStateServiceMock = mock(ConnectorStateService.class);
        BimApiClient bimApiClient = mock(BimApiClient.class);
        reportApiClient = mock(ReportApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        neocityCitizenAlertHandler = new NeocityCitizenAlertHandler(bimApiClient, objectMapper, cmSender, reportApiClient, neocityUtils, connectorStateServiceMock);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        Report report = new Report().toBuilder().code("myCode").build();

        String fileBody = fromFileToString("citizenAlertNeocity.json");

        //mock
        when(reportApiClient.getReportByExternalIds(eq(NEOCITY), any())).thenReturn(List.of(report));
        when(connectorStateServiceMock.treatConnectorState(anyList(), any())).thenReturn(new CrudOperation<>());

        //when
        neocityCitizenAlertHandler.handle(fileBody);

        verify(reportApiClient).getReportByExternalIds(eq(NEOCITY), any());
        verify(connectorStateServiceMock).handleConnectorState(any(), anyString());
    }

    @Test
    void should_create_neocity_report() {
        //given
        List<DataplatformNeocityCitizenAlert> incomingNeocityCitizenAlertDPs = fromFileToObject(
                "citizenAlertNeocity.json",
                new TypeReference<>() {
                });

        //when
        CrudOperation<Model> result = neocityCitizenAlertHandler.treatReport(List.of(), incomingNeocityCitizenAlertDPs);

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    void should_create_neocity_report_without_pictures() {
        //given
        List<DataplatformNeocityCitizenAlert> incomingNeocityCitizenAlertDPs = fromFileToObject(
                "citizenAlertNeocityNoPictures.json",
                new TypeReference<>() {
                });

        //when
        CrudOperation<Model> result = neocityCitizenAlertHandler.treatReport(List.of(), incomingNeocityCitizenAlertDPs);

        //then
        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    void should_update_neocity_report() {
        //given
        Map<String, Object> externalIds = new HashMap<>();
        externalIds.put(NEOCITY, "a1z2e3");

        List<DataplatformNeocityCitizenAlert> incomingNeocityCitizenAlertDPs = fromFileToObject(
                "citizenAlertNeocity.json",
                new TypeReference<>() {
                });

        Report existingReportToBeUpdated = new Report().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("a1z2e3")
                .externalids(externalIds)
                .comment("to be updated")
                .source(NEOCITY)
                .build();

        Map<String, Object> expectedDocuments = Map.of("documents", List.of(Map.of("url", "url_ici"),
                Map.of("url", "aaa"),
                Map.of("url", "bbbb")));

        Report expectedReportUpdate = new Report().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .code("a1z2e3")
                .name("123")
                .externalids(externalIds)
                .location(point)
                .priority(2)
                .reportdate(Date.from(Instant.parse("2021-10-20T13:53:54.515Z")))
                .metier(Map.of("metiers", List.of("EP")))
                .comment("équipement à remplacer")
                .documents(expectedDocuments)
                .status("New")
                .laststatuschangedate(new Date())
                .source(NEOCITY)
                .build();

        //when
        CrudOperation<Model> result = neocityCitizenAlertHandler.treatReport(List.of(existingReportToBeUpdated), incomingNeocityCitizenAlertDPs);

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location", "laststatuschangedate").isEqualTo(expectedReportUpdate);
        assertThat(result.getDelete()).isEmpty();
    }

}