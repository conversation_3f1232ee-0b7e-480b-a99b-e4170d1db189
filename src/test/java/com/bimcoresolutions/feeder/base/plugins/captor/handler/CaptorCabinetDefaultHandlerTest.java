package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.cabinetdefault.CabinetDefault;
import com.bimcoresolutions.feeder.base.plugins.captor.model.cabinetdefault.CabinetDefaultCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorCabinetDefaultHandlerTest extends BimTest {
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final String CAPTOR = "captor";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorCabinetDefaultHandler captorCabinetDefaultHandler;
    CabinetApiClient cabinetApiClient;
    AlarmApiClient alarmApiClient;

    @BeforeEach
    void init() {
        CMSender cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        cabinetApiClient = mock(CabinetApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorCabinetDefaultHandler = new CaptorCabinetDefaultHandler(bimApiClient, objectMapper, cmSender, captorUtils, cabinetApiClient, alarmApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_cabinetdefault() {
        Map<String, Object> ids = Map.of("captor", "ECPU 270 P");
        Cabinet cabinet = new Cabinet().toBuilder().externalids(ids).build();

        List<String> externalIds = List.of("ECPU 270 P");

        Alarm alarmCabinet = new Alarm().toBuilder()
                .code("ECPU 270 Perror")
                .externalids(ids)
                ._id_bimcore("ID_ALARM")
                .build();

        //mock
        when(cabinetApiClient.getCabinetsByExternalIds(CAPTOR, externalIds)).thenReturn(List.of(cabinet));
        when(alarmApiClient.getAlarmByExternalIds(CAPTOR, externalIds)).thenReturn(List.of(alarmCabinet));

        //when
        String fileBody = fromFileToString("cabinetDefaultCaptor.json");
        captorCabinetDefaultHandler.handle(fileBody);

        verify(cabinetApiClient).getCabinetsByExternalIds(CAPTOR, externalIds);
        verify(alarmApiClient).getAlarmByExternalIds(CAPTOR, externalIds);
    }

    @Test
    @SneakyThrows
    void should_create_alarm_cabinet() {
        Map<String, Object> ids = Map.of(CAPTOR, "ECPU 270 P");

        Cabinet cabinet = new Cabinet().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1cabinet")
                .inhibition(false)
                .location(point)
                .name("ECPU 270 P")
                .status(0)
                .externalids(ids)
                .build();

        Cabinet expected = new Cabinet().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1cabinet")
                .inhibition(false)
                .location(point)
                .name("ECPU 270 P")
                .status(1)
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("cabinetDefaultCaptor.json");
        CabinetDefaultCollection data = objectMapper.readValue(fileBody, CabinetDefaultCollection.class);
        List<CabinetDefault> comingCabinetDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorCabinetDefaultHandler.treatCabinetDefault(
                List.of(cabinet), comingCabinetDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // CabinetDefault alarm is created
        assertThat(result.getUpdate()).hasSize(1); // cabinet's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location").isEqualTo(expected);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_cabinet_status() {
        Map<String, Object> ids = Map.of(CAPTOR, "ECPU 270 P");

        Cabinet cabinet = new Cabinet().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1cabinet")
                .inhibition(false)
                .location(point)
                .name("ECPU 270 P")
                .status(-1)
                .externalids(ids)
                .build();

        Cabinet expectedCabinet = new Cabinet().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1cabinet")
                .inhibition(false)
                .location(point)
                .name("ECPU 270 P")
                .status(1)
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("cabinetDefaultCaptor.json");
        CabinetDefaultCollection data = objectMapper.readValue(fileBody, CabinetDefaultCollection.class);
        List<CabinetDefault> comingCabinetDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorCabinetDefaultHandler.treatCabinetDefault(
                List.of(cabinet), comingCabinetDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // CabinetDefault alarm is created
        assertThat(result.getUpdate()).hasSize(1); // cabinet's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location").isEqualTo(expectedCabinet);
        assertThat(result.getDelete()).isEmpty();

    }

    @Test
    @SneakyThrows
    void should_extinguish_alarm_cabinetdefault() {
        Map<String, Object> ids = Map.of(CAPTOR, "ECPU 270 P");

        Cabinet cabinet = new Cabinet().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1cabinet")
                .inhibition(false)
                .location(point)
                .name("ECPU 270 P")
                .status(-1)
                .externalids(ids)
                .build();

        Alarm alarmCabinet = new Alarm().toBuilder()
                .code("ECPU 270 Perror")
                .externalids(Map.of(CAPTOR, "ECPU 270 P"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.TRUE)
                .lastupdate(Date.from(Instant.ofEpochSecond(123456789L)))
                .cabinet(List.of(cabinet.get_id_bimcore()))
                .build();


        Alarm expectedAlarmCabinet = new Alarm().toBuilder()
                .code("ECPU 270 Perror")
                .externalids(Map.of(CAPTOR, "ECPU 270 P"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.FALSE)
                .cabinet(List.of(cabinet.get_id_bimcore()))
                .lastupdate(Date.from(Instant.ofEpochSecond(1696634486L)))
                .build();

        String fileBody = fromFileToString("cabinetDefaultCaptor.json");
        CabinetDefaultCollection data = objectMapper.readValue(fileBody, CabinetDefaultCollection.class);
        List<CabinetDefault> comingCabinetDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorCabinetDefaultHandler.treatCabinetDefault(
                List.of(cabinet), comingCabinetDefaults, List.of(alarmCabinet));
        CrudOperation<Alarm> resultAlarm = result.get(Alarm.class);

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(2); //cabinet's status is updated + cabinetDefault alarm is updated (already exists)
        assertThat(resultAlarm.getUpdate()).hasSize(1);
        assertThat(resultAlarm.getUpdate().stream().toList().getFirst()).usingRecursiveComparison()
                .ignoringFields("location").isEqualTo(expectedAlarmCabinet);
        assertThat(result.getDelete()).isEmpty();
    }

}