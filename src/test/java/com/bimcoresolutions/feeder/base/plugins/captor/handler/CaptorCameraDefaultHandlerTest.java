package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CountingStationApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.cameradefault.CameraDefault;
import com.bimcoresolutions.feeder.base.plugins.captor.model.cameradefault.CameraDefaultCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.CountingStation;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorCameraDefaultHandlerTest extends BimTest {
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final String IROAD = "iroad";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorCameraDefaultHandler captorCameraDefaultHandler;
    CountingStationApiClient countingStationApiClient;
    AlarmApiClient alarmApiClient;

    @BeforeEach
    void init() {
        CMSender cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        countingStationApiClient = mock(CountingStationApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorCameraDefaultHandler = new CaptorCameraDefaultHandler(bimApiClient, objectMapper, cmSender, captorUtils, countingStationApiClient, alarmApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_camera_default() {
        Map<String, Object> ids = Map.of(IROAD, "Cam 10.1 Ouest");
        CountingStation countingStation = new CountingStation().toBuilder()._id_bimcore(ID_BIMCORE).externalids(ids).build();

        List<String> externalIds = List.of("Cam 10.1 Ouest");

        Alarm alarmCamera = new Alarm().toBuilder()
                .code("Cam 10.1 OuestLAST_PREVIEW_FAILED")
                .externalids(ids)
                ._id_bimcore("ID_ALARM")
                .build();

        //mock
        when(countingStationApiClient.getCountingStationByExternalIds(IROAD, externalIds)).thenReturn(List.of(countingStation));
        when(alarmApiClient.getAlarmByExternalIds(IROAD, externalIds)).thenReturn(List.of(alarmCamera));

        //when
        String fileBody = fromFileToString("cameraDefaultCaptor.json");
        captorCameraDefaultHandler.handle(fileBody);

        verify(countingStationApiClient).getCountingStationByExternalIds(IROAD, externalIds);
        verify(alarmApiClient).getAlarmByExternalIds(IROAD, externalIds);
    }

    @Test
    @SneakyThrows
    void should_create_alarm_camera() {
        Map<String, Object> ids = Map.of(IROAD, "Cam 10.1 Ouest");

        CountingStation countingStation = new CountingStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1countingstation")
                .inhibition(false)
                .location(point)
                .name("Cam 10.1 Ouest")
                .status(0)
                .externalids(ids)
                .build();

        CountingStation expected = new CountingStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1countingstation")
                .inhibition(false)
                .location(point)
                .name("Cam 10.1 Ouest")
                .status(1)
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("cameraDefaultCaptor.json");
        CameraDefaultCollection data = objectMapper.readValue(fileBody, CameraDefaultCollection.class);
        List<CameraDefault> comingCameraDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorCameraDefaultHandler.treatCameraDefault(List.of(countingStation), comingCameraDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // CameraEvent alarm is created
        assertThat(result.getUpdate()).hasSize(1); // Countingstation's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location", "lastupdate").isEqualTo(expected);
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_camera_status() {
        Map<String, Object> ids = Map.of(IROAD, "Cam 10.1 Ouest");

        CountingStation countingStation = new CountingStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1countingstation")
                .inhibition(false)
                .location(point)
                .name("Cam 10.1 Ouest")
                .status(-1)
                .externalids(ids)
                .build();

        CountingStation expectedCountingStation = new CountingStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1countingstation")
                .inhibition(false)
                .location(point)
                .name("Cam 10.1 Ouest")
                .status(1)
                .externalids(ids)
                .build();

        String fileBody = fromFileToString("cameraDefaultCaptor.json");
        CameraDefaultCollection data = objectMapper.readValue(fileBody, CameraDefaultCollection.class);
        List<CameraDefault> comingCameraDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorCameraDefaultHandler.treatCameraDefault(List.of(countingStation), comingCameraDefaults, List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // CameraEvent alarm is created
        assertThat(result.getUpdate()).hasSize(1); // Countingstation's status is updated
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location", "lastupdate").isEqualTo(expectedCountingStation);
        assertThat(result.getDelete()).isEmpty();

    }

    @Test
    @SneakyThrows
    void should_extinguish_alarm_camera_default() {
        Map<String, Object> ids = Map.of(IROAD, "Cam 10.1 Ouest");

        CountingStation countingStation = new CountingStation().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1countinstation")
                .inhibition(false)
                .location(point)
                .name("Cam 10.1 Ouest")
                .status(-1)
                .externalids(ids)
                .build();

        Alarm alarmCamera = new Alarm().toBuilder()
                .code("Cam 10.1 OuestLAST_PREVIEW_FAILED")
                .name("Dernière image échouée")
                .externalids(Map.of(IROAD, "Cam 10.1 Ouest"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.TRUE)
                .countingstation(List.of(countingStation.get_id_bimcore()))
                .build();


        Alarm expectedAlarmCamera = new Alarm().toBuilder()
                .code("Cam 10.1 OuestLAST_PREVIEW_FAILED")
                .name("Dernière image échouée")
                .externalids(Map.of(IROAD, "Cam 10.1 Ouest"))
                ._id_bimcore("ID_ALARM")
                .presence(Boolean.FALSE)
                .countingstation(List.of(countingStation.get_id_bimcore()))
                .build();

        String fileBody = fromFileToString("cameraDefaultCaptor.json");
        CameraDefaultCollection data = objectMapper.readValue(fileBody, CameraDefaultCollection.class);
        List<CameraDefault> comingCameraDefaults = data.getItems();

        //when
        CrudOperation<Model> result = captorCameraDefaultHandler.treatCameraDefault(List.of(countingStation), comingCameraDefaults, List.of(alarmCamera));
        CrudOperation<Alarm> resultAlarm = result.get(Alarm.class);

        //then
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(2); //Countingstation's status is updated + cameraDefault alarm is updated (already exists)
        assertThat(resultAlarm.getUpdate()).hasSize(1);
        assertThat(resultAlarm.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location", "lastupdate").isEqualTo(expectedAlarmCamera);
        assertThat(result.getDelete()).isEmpty();
    }

}