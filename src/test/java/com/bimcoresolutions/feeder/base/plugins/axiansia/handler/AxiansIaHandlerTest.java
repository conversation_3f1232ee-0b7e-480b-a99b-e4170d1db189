package com.bimcoresolutions.feeder.base.plugins.axiansia.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlertApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.axiansia.AxiansiaUtils;
import com.bimcoresolutions.feeder.base.plugins.axiansia.model.EnumAxiansia;
import com.bimcoresolutions.feeder.base.plugins.axiansia.model.InputAxiansIa;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;

import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

class AxiansIaHandlerTest extends BimTest {

    public static final String RANDOM_ID = "random id";
    private AxiansiaHandler axiansiaHandler;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        AlertApiClient alertApiClient = mock(AlertApiClient.class);
        LightingPointApiClient lightingPointApiClient = mock(LightingPointApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper();
        AxiansiaUtils axiansiaUtils = new AxiansiaUtils(alertApiClient);
        axiansiaHandler = new AxiansiaHandler(cmSender, bimApiClient, objectMapper, alertApiClient, axiansiaUtils, lightingPointApiClient);
    }

    private LightingPoint buildLp() {
        return LightingPoint.builder()
                ._id_bimcore("0a7e7477-bad1-dead-a853-a3fc90f00ffd")
                .name("SL552013")
                .location(new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d)))
                .lamps(Map.of("SL552013L1", Map.of("model", "Schreder AVENTO LED 108W", "externalids", Map.of("cityapp", "123456"))))
                .status(1)
                .build();
    }

    @Test
    void should_create_alert() {
        List<InputAxiansIa> incomingAxiansIaAlerts = fromFileToObject("axiansIaInputFault.json", new TypeReference<>() {
        });
        LightingPoint lp = buildLp();
        Alert expectedAlert = Alert.builder()
                ._id_bimcore(null)
                .code("EQ_1000")
                .location(lp.getLocation())
                .metier(Map.of("Metiers", List.of("EP")))
                .comment("High anomaly - Voltage drop >= 0.05 - V18=2")
                .name("SL552013L1")
                .status("New")
                .category(EnumAxiansia.failure.getName())
                .source(AxiansiaUtils.AXIANS)
                .creator(AxiansiaUtils.AXIANS)
                .creationdate(Date.from(Instant.parse("2022-11-02T06:00:00.000Z")))
                .lightingpoint(List.of(lp.get_id_bimcore()))
                .alarms(List.of())
                .interventionrequest(List.of())
                .priority(3)
                .barrier(List.of())
                .cabinet(List.of())
                .camera(List.of())
                .chargingpoint(List.of())
                .chargingstation(List.of())
                .controller(List.of())
                .countingstation(List.of())
                .dynpanel(List.of())
                .environmentalsensor(List.of())
                .intersection(List.of())
                .meter(List.of())
                .parking(List.of())
                .railcrossing(List.of())
                .railway(List.of())
                .riverstructure(List.of())
                .transformerstation(List.of())
                .build();

        CrudOperation<Model> result = axiansiaHandler.treatAlerts(List.of(), List.of(lp), incomingAxiansIaAlerts);

        assertThat(result.getCreate()).hasSize(1);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
        assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("laststatuschangedate").isEqualTo(expectedAlert);
    }

    @Test
    void should_not_create_alert() {
        List<InputAxiansIa> incomingAxiansIaAlerts = fromFileToObject("axiansIaInputNoFault.json", new TypeReference<>() {
        });
        CrudOperation<Model> result = axiansiaHandler.treatAlerts(List.of(), List.of(buildLp()), incomingAxiansIaAlerts);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    void should_update_alert() {
        List<InputAxiansIa> incomingAxiansIaAlerts = fromFileToObject("axiansIaInputFault.json", new TypeReference<>() {
        });

        LightingPoint lp = buildLp();
        Alert existingAlertToBeUpdated = Alert.builder()
                ._id_bimcore(RANDOM_ID)
                .code("SL575061")
                .location(lp.getLocation())
                .metier(Map.of("Metiers", List.of("EP")))
                .comment("TO BE UPDATED")
                .name("SL552013L1")
                .status("New")
                .category(EnumAxiansia.failure.getName())
                .source(AxiansiaUtils.AXIANS)
                .creator(AxiansiaUtils.AXIANS)
                .creationdate(Date.from(Instant.parse("2022-11-02T06:00:00.000Z")))
                .lightingpoint(List.of(lp.get_id_bimcore()))
                .build();

        Alert expectedAlert = existingAlertToBeUpdated.toBuilder()
                .comment("High anomaly - Voltage drop >= 0.05 - V18=2")
                .build();

        CrudOperation<Model> result = axiansiaHandler.treatAlerts(List.of(existingAlertToBeUpdated), List.of(lp), incomingAxiansIaAlerts);

        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getUpdate()).hasSize(1);
        assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("laststatuschangedate").isEqualTo(expectedAlert);
        assertThat(result.getDelete()).isEmpty();
    }
}
