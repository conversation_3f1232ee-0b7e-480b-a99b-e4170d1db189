package com.bimcoresolutions.feeder.base.plugins.cityapp.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.DataplatformCityAppAssetState;
import com.bimcoresolutions.feeder.base.plugins.cityapp.util.CityAppUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CityAppAssetStateHandlerTest extends BimTest {
    private static final TypeReference<List<DataplatformCityAppAssetState>> typeReferenceAssetState = new TypeReference<>() {
    };
    private CityAppAssetStateHandler cityAppAssetStateHandler;
    private CabinetApiClient cabinetApiClient;
    private LightingPointApiClient lightingPointApiClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        cabinetApiClient = mock(CabinetApiClient.class);
        lightingPointApiClient = mock(LightingPointApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        CityAppUtils cityAppUtils = new CityAppUtils();
        cityAppAssetStateHandler = new CityAppAssetStateHandler(bimApiClient, objectMapper, cmSender, cityAppUtils, cabinetApiClient, lightingPointApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        Cabinet cabinet = new Cabinet().toBuilder().code("1").build();
        LightingPoint lightingPoint = new LightingPoint().toBuilder().code("1").build();

        when(cabinetApiClient.getCabinetsByExternalIds(anyString(), anyList())).thenReturn(List.of(cabinet));
        when(lightingPointApiClient.getLightingPointsByExternalIds(anyString(), anyList())).thenReturn(List.of(lightingPoint));


        String fileBody = fromFileToString("cityAppAssetStates.json");
        cityAppAssetStateHandler.handle(fileBody);

        verify(cabinetApiClient).getCabinetsByExternalIds(anyString(), anyList());
        verify(lightingPointApiClient).getLightingPointsByExternalIds(anyString(), anyList());
    }

    @Test
    void should_update_cityapp_lightingpointassetstate() {
        List<DataplatformCityAppAssetState> incomingDataplatformCityAppAssetState = fromFileToObject("cityAppAssetStates.json", typeReferenceAssetState);
        LightingPoint existingLightingPoint = new LightingPoint().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(-1)
                .forced(0)
                .operatingstate(0)
                .build();

        LightingPoint expectedLightingPoint = new LightingPoint().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(1)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetStateHandler.treatAssetStatesForLightingPoints(List.of(existingLightingPoint), incomingDataplatformCityAppAssetState);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(expectedLightingPoint);

    }

    @Test
    void should_update_cityapp_lightingpointassetstate_nok() {
        List<DataplatformCityAppAssetState> incomingDataplatformCityAppAssetState = fromFileToObject("cityAppAssetStatesNok.json", typeReferenceAssetState);
        LightingPoint existingLightingPoint = new LightingPoint().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(1)
                .forced(0)
                .operatingstate(0)
                .build();

        LightingPoint expectedLightingPoint = new LightingPoint().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(-1)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetStateHandler.treatAssetStatesForLightingPoints(List.of(existingLightingPoint), incomingDataplatformCityAppAssetState);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(expectedLightingPoint);

    }

    @Test
    void should_update_cityapp_cabinetassetstate() {
        List<DataplatformCityAppAssetState> incomingDataplatformCityAppAssetState = fromFileToObject("cityAppAssetStates.json", typeReferenceAssetState);
        Cabinet existingCabinet = new Cabinet().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(-1)
                .forced(0)
                .operatingstate(0)
                .build();

        Cabinet expectedCabinet = new Cabinet().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(1)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetStateHandler.treatAssetStatesForCabinets(List.of(existingCabinet), incomingDataplatformCityAppAssetState);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(expectedCabinet);

    }

    @Test
    void should_update_cityapp_cabinetassetstate_nok() {
        List<DataplatformCityAppAssetState> incomingDataplatformCityAppAssetState = fromFileToObject("cityAppAssetStatesNok.json", typeReferenceAssetState);
        Cabinet existingCabinet = new Cabinet().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(1)
                .forced(0)
                .operatingstate(0)
                .build();

        Cabinet expectedCabinet = new Cabinet().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(-1)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetStateHandler.treatAssetStatesForCabinets(List.of(existingCabinet), incomingDataplatformCityAppAssetState);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(expectedCabinet);

    }

}