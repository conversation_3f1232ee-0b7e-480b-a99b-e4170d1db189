package com.bimcoresolutions.feeder.base.plugins.captor.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CountingStationApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.NotifEventApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.captor.model.cameraevent.CameraEvent;
import com.bimcoresolutions.feeder.base.plugins.captor.model.cameraevent.CameraEventCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.NotifEvent;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CaptorCameraEventHandlerTest extends BimTest {
    private static final String ID_BIMCORE = "myIdBimCore";
    private static final String CAPTOR = "captor";
    private static final String FLIR = "flir";
    private static final String CODE = "myCode";
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    CaptorCameraNotifHandler captorCameraNotifHandler;
    NotifEventApiClient notifEventApiClient;
    CountingStationApiClient countingStationApiClient;

    @BeforeEach
    void init() {
        CMSender cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        notifEventApiClient = mock(NotifEventApiClient.class);
        countingStationApiClient = mock(CountingStationApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        captorCameraNotifHandler = new CaptorCameraNotifHandler(bimApiClient, notifEventApiClient, objectMapper, cmSender, captorUtils, countingStationApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_camera_event() {
        Map<String, Object> ids = Map.of(CAPTOR, "61221349");
        NotifEvent notifEvent = new NotifEvent().toBuilder()
                .code(CODE)
                ._id_bimcore(ID_BIMCORE)
                .externalids(ids)
                .build();

        List<String> externalIds = List.of("61221349");

        //mock
        when(notifEventApiClient.getNotifEventByExternalIds(CAPTOR, externalIds)).thenReturn(List.of(notifEvent));

        //when
        String fileBody = fromFileToString("cameraEventCaptor.json");
        captorCameraNotifHandler.handle(fileBody);
        verify(notifEventApiClient).getNotifEventByExternalIds(CAPTOR, externalIds);
    }

    @Test
    @SneakyThrows
    void should_create_notif() {
        Map<String, Object> ids = Map.of(CAPTOR, "61221349");
        NotifEvent expected = new NotifEvent().toBuilder()
                .code("61221349")
                .externalids(ids)
                .name("Jour Nuit Cam 10.1 Ouest")
                .category("Jour Nuit")
                .source(FLIR)
                .status("ToApprove")
                .comment("Cam 10.1 Ouest")
                .build();

        String fileBody = fromFileToString("cameraEventCaptor.json");
        CameraEventCollection data = objectMapper.readValue(fileBody, CameraEventCollection.class);
        List<CameraEvent> comingCameraEvents = data.getItems();

        //when
        CrudOperation<Model> result = captorCameraNotifHandler.treatNotifs(comingCameraEvents, List.of(), List.of());

        //then
        assertThat(result.getCreate()).hasSize(1); // CameraEvent Notif is created
        assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("code", "location", "apparitiondate").isEqualTo(expected);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }
}