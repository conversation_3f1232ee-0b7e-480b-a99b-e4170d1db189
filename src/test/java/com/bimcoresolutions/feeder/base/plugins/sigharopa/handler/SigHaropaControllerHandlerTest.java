package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.controller.ControllerCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.ControllerGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Controller;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.ControllerCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaControllerHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String SIG = "sig";
    public static final String IROAD = "iroad";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    SigHaropaControllerHandler sigHaropaControllerHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    ControllerGenFeignClient controllerGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        controllerGenFeignClient = mock(ControllerGenFeignClient.class);
        sigHaropaControllerHandler = new SigHaropaControllerHandler(bimApiClient, objectMapper, cmSender, controllerGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_controller_sig() {
        String code = "1controller";
        Controller controller = new Controller().toBuilder().code(code).build();

        //mock
        PaginatedResponse<Controller> panelPaginatedResponse = new PaginatedResponse<>(List.of(controller), new PaginatedResponse.Pagination());
        when(controllerGenFeignClient.get(new ControllerCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("controllerSig.json");
        sigHaropaControllerHandler.handle(fileBody);

        verify(controllerGenFeignClient).get(new ControllerCriteria(), null, null, null, null, null);
    }


    @Test
    @SneakyThrows
    void should_create_controller() {
        String fileBody = fromFileToString("controllerSig.json");
        ControllerCollection data = objectMapper.readValue(fileBody, ControllerCollection.class);

        Map<String, Object> ids = Map.of(IROAD, "16", SIG, "10001");
        Controller controller = new Controller().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .inhibition(false)
                .operatingstate(0)
                .status(0)
                .type("Contrôleur de carrefours")
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .model("SAGEMCASTOR")
                .name("CC17")
                .site("SORTIE CERT SUD")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigController(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(controller);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_controller() {
        Map<String, Object> ids = Map.of(IROAD, "16", SIG, "10001");

        Controller controller = new Controller().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .model("SAGEMCASTOR")
                .name("CC17")
                .site("SORTIE CERT SUD")
                .build();

        String fileBody = fromFileToString("controllerSig.json");
        ControllerCollection data = objectMapper.readValue(fileBody, ControllerCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigController(List.of(controller), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(controller);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_controller() {
        Map<String, Object> ids = Map.of(IROAD, "16", SIG, "10001");

        Controller controller = new Controller().toBuilder()
                ._id_bimcore(RANDOM_ID)
                .externalids(ids)
                .location(point)
                .code(SIGHAROPA_ + "10001")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigController(List.of(controller), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(controller.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }

}