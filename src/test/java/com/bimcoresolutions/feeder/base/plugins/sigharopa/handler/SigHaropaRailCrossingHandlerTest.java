package com.bimcoresolutions.feeder.base.plugins.sigharopa.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.SigHaropaUtils;
import com.bimcoresolutions.feeder.base.plugins.sigharopa.model.railcrossing.RailCrossingCollection;
import com.bimcoresolutions.product.bimcity.api.generated.client.feignclient.RailCrossingGenFeignClient;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailCrossing;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.RailCrossingCriteria;
import com.bimcoresolutions.util.base.model.Model;
import com.bimcoresolutions.util.base.rest.PaginatedResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;


class SigHaropaRailCrossingHandlerTest extends BimTest {
    public static final String SIGHAROPA_ = "sigharopa_";
    public static final String RANDOM_ID = "random id";
    public static final String CAPTOR = "captor";
    public static final String SIG = "sig";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));

    SigHaropaRailCrossingHandler sigHaropaControllerHandler;
    BimApiClient bimApiClient;
    ObjectMapper objectMapper;
    CMSender cmSender;
    RailCrossingGenFeignClient railCrossingGenFeignClient;
    SigHaropaUtils sigHaropaUtils;

    @BeforeEach
    void init() {
        bimApiClient = mock(BimApiClient.class);
        sigHaropaUtils = new SigHaropaUtils(bimApiClient);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        cmSender = mock(CMSender.class);
        railCrossingGenFeignClient = mock(RailCrossingGenFeignClient.class);
        sigHaropaControllerHandler = new SigHaropaRailCrossingHandler(bimApiClient, objectMapper, cmSender, railCrossingGenFeignClient, sigHaropaUtils);
    }

    @Test
    @SneakyThrows
    void should_treat_messages_railcrossing_sig() {
        String code = "1railcrossing";
        RailCrossing railCrossing = new RailCrossing().toBuilder().code(code).build();

        //mock
        PaginatedResponse<RailCrossing> panelPaginatedResponse = new PaginatedResponse<>(List.of(railCrossing), new PaginatedResponse.Pagination());
        when(railCrossingGenFeignClient.get(new RailCrossingCriteria(), null, null, null, null, null)).thenReturn(panelPaginatedResponse);

        //when
        String fileBody = fromFileToString("railCrossingSig.json");
        sigHaropaControllerHandler.handle(fileBody);

        verify(railCrossingGenFeignClient).get(new RailCrossingCriteria(), null, null, null, null, null);
    }

    @Test
    @SneakyThrows
    void should_create_railcrossing() {
        String fileBody = fromFileToString("railCrossingSig.json");
        RailCrossingCollection data = objectMapper.readValue(fileBody, RailCrossingCollection.class);

        Map<String, Object> ids = Map.of(CAPTOR, "PN504", SIG, "10001");
        RailCrossing railCrossing = new RailCrossing().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("PN504")
                .operatingstate(0)
                .site("Route du Mole Central")
                .status(0)
                .externalids(ids)
                .type("railcross")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigRailCrossing(List.of(), data.getFeatures());

        //then
        assertThat(result.getCreate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("_id_bimcore", "location", "installationdate").isEqualTo(railCrossing);
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_update_railcrossing() {
        Map<String, Object> ids = Map.of(CAPTOR, "PN504", SIG, "10001");

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("PN504")
                .operatingstate(0)
                .site("Route du Mole Central")
                .status(0)
                .externalids(ids)
                .type("railcross")
                .build();

        String fileBody = fromFileToString("railCrossingSig.json");
        RailCrossingCollection data = objectMapper.readValue(fileBody, RailCrossingCollection.class);

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigRailCrossing(List.of(railCrossing), data.getFeatures());

        //then
        assertThat(result.getUpdate()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("location").isEqualTo(railCrossing);
        assertThat(result.getCreate()).isEmpty();
        assertThat(result.getDelete()).isEmpty();
    }

    @Test
    @SneakyThrows
    void should_delete_railcrossing() {
        Map<String, Object> ids = Map.of(CAPTOR, "PN504", SIG, "10001");

        RailCrossing railCrossing = new RailCrossing().toBuilder()
                .code(SIGHAROPA_ + "10001")
                .inhibition(false)
                .installationdate(new Date())
                .location(point)
                .name("PN504")
                .operatingstate(0)
                .site("Route du Mole Central")
                .status(0)
                .externalids(ids)
                .type("railcross")
                .build();

        //when
        CrudOperation<Model> result = sigHaropaControllerHandler.treatSigRailCrossing(List.of(railCrossing), List.of());

        //then
        assertThat(result.getDelete()).hasSize(1);
        AssertionsForClassTypes.assertThat(result.getDelete().stream().toList().getFirst().get_id_bimcore()).isEqualTo(railCrossing.get_id_bimcore());
        assertThat(result.getUpdate()).isEmpty();
        assertThat(result.getCreate()).isEmpty();
    }
}