package com.bimcoresolutions.feeder.base.plugins.cityapp.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.CabinetApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.cityapp.model.DataplatformCityAppAsset;
import com.bimcoresolutions.feeder.base.plugins.cityapp.util.CityAppUtils;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Cabinet;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

class CityAppAssetHandlerTest extends BimTest {
    private static final TypeReference<List<DataplatformCityAppAsset>> typeReferenceAsset = new TypeReference<>() {
    };
    private CityAppAssetHandler cityAppAssetHandler;
    private CabinetApiClient cabinetApiClient;
    private LightingPointApiClient lightingPointApiClient;

    @BeforeEach
    void init() {
        BimApiClient bimApiClient = mock(BimApiClient.class);
        cabinetApiClient = mock(CabinetApiClient.class);
        lightingPointApiClient = mock(LightingPointApiClient.class);
        CMSender cmSender = mock(CMSender.class);
        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules();
        CityAppUtils cityAppUtils = new CityAppUtils();
        cityAppAssetHandler = new CityAppAssetHandler(bimApiClient, objectMapper, cmSender, cityAppUtils, cabinetApiClient, lightingPointApiClient);
    }

    @Test
    @SneakyThrows
    void should_treat_dataplatform_message() {
        Cabinet cabinet = new Cabinet().toBuilder().code("1").build();
        LightingPoint lightingPoint = new LightingPoint().toBuilder().code("1").build();

        when(cabinetApiClient.getCabinetsByExternalIds(anyString(), anyList())).thenReturn(List.of(cabinet));
        when(lightingPointApiClient.getLightingPointsByExternalIds(anyString(), anyList())).thenReturn(List.of(lightingPoint));

        String fileBody = fromFileToString("cityAppAssetsCabinet.json");
        cityAppAssetHandler.handle(fileBody);

        verify(cabinetApiClient).getCabinetsByExternalIds(anyString(), anyList());
        verify(lightingPointApiClient).getLightingPointsByExternalIds(anyString(), anyList());
    }

    @Test
    void should_create_cityapp_cabinet() {
        List<DataplatformCityAppAsset> incomingDataplatformCityAppAsset = fromFileToObject("cityAppAssetsCabinet.json", typeReferenceAsset);
        CrudOperation<Model> crudOperation = cityAppAssetHandler.treatAssetsForCabinets(List.of(), incomingDataplatformCityAppAsset);

        assertThat(crudOperation.getCreate()).hasSize(1);
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
    }

    @Test
    void should_update_cityapp_cabinet() {
        List<DataplatformCityAppAsset> incomingDataplatformCityAppAsset = fromFileToObject("cityAppAssetsCabinet.json", typeReferenceAsset);
        Cabinet existingCabinet = new Cabinet().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .telegestionaddress("NEW_PL_tech_20131217_414_")
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(0)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetHandler.treatAssetsForCabinets(List.of(existingCabinet), incomingDataplatformCityAppAsset);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(existingCabinet);
        assertThat(crudOperation.getDelete()).isEmpty();

    }

    @Test
    void should_delete_cityapp_cabinet() {
        List<DataplatformCityAppAsset> incomingDataplatformCityAppAsset = fromFileToObject("cityAppAssetsCabinetToDelete.json", typeReferenceAsset);
        Cabinet existingCabinet = new Cabinet().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(0)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetHandler.treatAssetsForCabinets(List.of(existingCabinet), incomingDataplatformCityAppAsset);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getDelete()).hasSize(1);
        assertThat(crudOperation.getDelete().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(existingCabinet);

    }

    @Test
    void should_create_cityapp_lightingpoint() {
        List<DataplatformCityAppAsset> incomingDataplatformCityAppAsset = fromFileToObject("cityAppAssetsLightingPoint.json", typeReferenceAsset);
        CrudOperation<Model> crudOperation = cityAppAssetHandler.treatAssetsForLightingPoints(List.of(), incomingDataplatformCityAppAsset);

        assertThat(crudOperation.getCreate()).hasSize(1);
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getDelete()).isEmpty();
    }

    @Test
    void should_update_cityapp_lightingpoint() {
        List<DataplatformCityAppAsset> incomingDataplatformCityAppAsset = fromFileToObject("cityAppAssetsLightingPoint.json", typeReferenceAsset);
        LightingPoint existingLightingPoint = new LightingPoint().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(0)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetHandler.treatAssetsForLightingPoints(List.of(existingLightingPoint), incomingDataplatformCityAppAsset);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getUpdate()).hasSize(1);
        assertThat(crudOperation.getUpdate().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(existingLightingPoint);
        assertThat(crudOperation.getDelete()).isEmpty();

    }

    @Test
    void should_delete_cityapp_lightingpoint() {
        List<DataplatformCityAppAsset> incomingDataplatformCityAppAsset = fromFileToObject("cityAppAssetsLightingPointToDelete.json", typeReferenceAsset);
        LightingPoint existingLightingPoint = new LightingPoint().toBuilder()
                .code("cityapp_415")
                .controllable(false)
                .externalids(Map.of("cityapp", "415"))
                .name("NEW_PL_tech_20131217_414_")
                .site("12 Avenue Lamartine ")
                .status(0)
                .forced(0)
                .operatingstate(0)
                .build();

        CrudOperation<Model> crudOperation = cityAppAssetHandler.treatAssetsForLightingPoints(List.of(existingLightingPoint), incomingDataplatformCityAppAsset);

        assertThat(crudOperation.getCreate()).isEmpty();
        assertThat(crudOperation.getUpdate()).isEmpty();
        assertThat(crudOperation.getDelete()).hasSize(1);
        assertThat(crudOperation.getDelete().stream().toList().getFirst())
                .usingRecursiveComparison()
                .ignoringFields("installationdate", "location")
                .isEqualTo(existingLightingPoint);

    }

}