package com.bimcoresolutions.feeder.base.plugins.interact.handler;

import com.bimcoresolutions.feeder.base.BimTest;
import com.bimcoresolutions.feeder.base.CMSender;
import com.bimcoresolutions.feeder.base.client.BimApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlarmApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.AlertApiClient;
import com.bimcoresolutions.feeder.base.client.bimcity.LightingPointApiClient;
import com.bimcoresolutions.feeder.base.notification.CrudOperation;
import com.bimcoresolutions.feeder.base.plugins.captor.CaptorUtils;
import com.bimcoresolutions.feeder.base.plugins.interact.model.InteractFault;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alarm;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.Alert;
import com.bimcoresolutions.product.bimcity.api.generated.client.model.LightingPoint;
import com.bimcoresolutions.util.base.model.Model;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

class InteractFaultHandlerTest extends BimTest {
    public static final String ID_BIMCORE = "myIdBimCore";
    public static final String INTERACT = "interact";
    public static final Point point = new GeometryFactory().createPoint(new Coordinate(1.09d, 49.45d));
    BimApiClient bimApiClient;
    AlarmApiClient alarmApiClient;
    AlertApiClient alertApiClient;
    LightingPointApiClient lightingPointApiClient;
    ObjectMapper objectMapper;
    CaptorUtils captorUtils;
    InteractFaultHandler interactFaultHandler;

    @BeforeEach
    void init() {
        CMSender cmSender = mock(CMSender.class);
        bimApiClient = mock(BimApiClient.class);
        alarmApiClient = mock(AlarmApiClient.class);
        alertApiClient = mock(AlertApiClient.class);
        lightingPointApiClient = mock(LightingPointApiClient.class);
        objectMapper = new ObjectMapper().findAndRegisterModules();
        captorUtils = new CaptorUtils();
        interactFaultHandler = new InteractFaultHandler(bimApiClient, lightingPointApiClient, objectMapper, cmSender, alarmApiClient, alertApiClient);
    }

    @Test
    @SneakyThrows
    void should_create_alarm_interact() {
        Map<String, Object> ids = Map.of(INTERACT, "123");

        LightingPoint lightingPoint = new LightingPoint().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1lightingpoint")
                .inhibition(false)
                .location(point)
                .name("myName")
                .status(0)
                .externalids(ids)
                .build();

        LightingPoint expected = new LightingPoint().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1lightingpoint")
                .inhibition(false)
                .location(point)
                .name("myName")
                .status(-1)
                .externalids(ids)
                .build();

        InteractFault interactFault = new InteractFault().toBuilder()
                .categoryKey("Event_OlcNotReportingLogData")
                .errorKey("Event_OlcNotReportingLogData")
                .severity("Warning")
                .isActive(true)
                .creationTimestamp(Instant.now())
                .externalAssetId("123")
                .elementId("456")
                .build();

        Alarm expectedAlarm = new Alarm().toBuilder()
                ._id_bimcore("myIdBimCoreAlarm")
                .code("123_Event_OlcNotReportingLogData")
                .lightingpoint(List.of(ID_BIMCORE))
                .name("Le réverbère n'est pas alimenté, le nœud ne communique plus ses données horaires.")
                .metier(Map.of("metiers", List.of("EP")))
                .presence(true)
                .externalids(ids)
                .source(INTERACT)
                .build();

        CrudOperation<Model> result = interactFaultHandler.treatInteractFaults(List.of(interactFault), List.of(lightingPoint), List.of());
        result.get(Alarm.class).getCreate().forEach(a -> a.set_id_bimcore("myIdBimCoreAlarm"));

        CrudOperation<LightingPoint> crudLightingPoint = result.get(LightingPoint.class);
        assertThat(crudLightingPoint.getUpdate()).hasSize(1);
        assertThat(crudLightingPoint.getUpdate().stream().toList().getFirst()).isEqualTo(expected);

        CrudOperation<Alarm> crudAlarm = result.get(Alarm.class);
        assertThat(crudAlarm.getCreate()).hasSize(1);
        assertThat(crudAlarm.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("lastupdate").isEqualTo(expectedAlarm);
    }


    @Test
    @SneakyThrows
    void should_update_alarm_interact() {
        Map<String, Object> ids = Map.of(INTERACT, "123");

        LightingPoint lightingPoint = new LightingPoint().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1lightingpoint")
                .inhibition(false)
                .location(point)
                .name("myName")
                .status(0)
                .externalids(ids)
                .build();

        LightingPoint expected = new LightingPoint().toBuilder()
                ._id_bimcore(ID_BIMCORE)
                .code("1lightingpoint")
                .inhibition(false)
                .location(point)
                .name("myName")
                .status(1)
                .externalids(ids)
                .build();

        InteractFault interactFault = new InteractFault().toBuilder()
                .categoryKey("Event_LampOutage")
                .errorKey("Event_LampOutage")
                .severity("Warning")
                .isActive(false)
                .creationTimestamp(Instant.now())
                .closedTimestamp(Instant.now())
                .externalAssetId("123")
                .elementId("456")
                .build();

        Alarm alarm = new Alarm().toBuilder()
                ._id_bimcore("myIdBimCoreAlarm")
                .code("123_Event_LampOutage")
                .lightingpoint(List.of(ID_BIMCORE))
                .name("Panne de la source lumineuse")
                .metier(Map.of("metiers", List.of("EP")))
                .presence(true)
                .externalids(ids)
                .source(INTERACT)
                .build();

        Alarm expectedAlarm = new Alarm().toBuilder()
                ._id_bimcore("myIdBimCoreAlarm")
                .code("123_Event_LampOutage")
                .lightingpoint(List.of(ID_BIMCORE))
                .name("Panne de la source lumineuse")
                .metier(Map.of("metiers", List.of("EP")))
                .presence(false)
                .externalids(ids)
                .source(INTERACT)
                .build();

        List<String> alarms = new ArrayList<>();
        if (alarm.get_id_bimcore() != null) {
            alarms.add(alarm.get_id_bimcore());
        }

        Alert expectedAlert = new Alert().toBuilder()
                ._id_bimcore("myIdBimCoreAlert")
                .code("123456_Event_LampOutage")
                .location(lightingPoint.getLocation())
                .priority(3)
                .name(alarm.getName())
                .source(INTERACT)
                .category(alarm.getName())
                .status("New")
                .creationdate(new Date())
                .laststatuschangedate(new Date())
                .alarms(alarms)
                .lightingpoint(List.of(lightingPoint.get_id_bimcore()))
                .build();

        CrudOperation<Model> result = interactFaultHandler.treatInteractFaults(List.of(interactFault), List.of(lightingPoint), List.of(alarm));
        result.get(Alarm.class).getCreate().forEach(a -> a.set_id_bimcore("myIdBimCoreAlarm"));

        CrudOperation<LightingPoint> crudLightingPoint = result.get(LightingPoint.class);
        assertThat(crudLightingPoint.getUpdate()).hasSize(1);
        assertThat(crudLightingPoint.getUpdate().stream().toList().getFirst()).isEqualTo(expected);

        CrudOperation<Alarm> crudAlarm = result.get(Alarm.class);
        assertThat(crudAlarm.getUpdate()).hasSize(1);
        assertThat(crudAlarm.getUpdate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("lastupdate").isEqualTo(expectedAlarm);

        result.get(Alert.class).getCreate().forEach(a -> a.set_id_bimcore("myIdBimCoreAlert"));
        CrudOperation<Alert> crudAlert = result.get(Alert.class);
        assertThat(crudAlert.getCreate()).hasSize(1);
        assertThat(crudAlert.getCreate().stream().toList().getFirst()).usingRecursiveComparison().ignoringFields("code", "location", "creationdate", "laststatuschangedate").isEqualTo(expectedAlert);

    }
}